package cn.jihong.mes.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.jihong.common.model.Pagination;
import cn.jihong.mes.api.model.dto.ActualLossStatisticsParamGroupDTO;
import cn.jihong.mes.api.model.dto.GroupLossStatisticsDayDTO;
import cn.jihong.mes.api.model.dto.GroupLossStatisticsDayParamDTO;
import cn.jihong.mes.api.model.po.GroupLossStatisticsDayPO;
import cn.jihong.mes.api.model.vo.ActualLossStatisticsMesGroupDetailVO;
import cn.jihong.mes.api.model.vo.GroupLossStatisticsDayVO;
import cn.jihong.mes.api.service.IActualLossStatisticsGroupSheetService;
import cn.jihong.mes.app.mapper.GroupLossStatisticsDayMapper;
import cn.jihong.mes.api.service.IGroupLossStatisticsDayService;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.erp.api.model.po.OocqlTPO;
import cn.jihong.oa.erp.api.model.vo.OocqlTVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 集团损耗统计日报-单位张 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-21
 */
@Service
public class GroupLossStatisticsDayServiceImpl extends JiHongServiceImpl<GroupLossStatisticsDayMapper, GroupLossStatisticsDayPO> implements IGroupLossStatisticsDayService {
    @Resource
    private IActualLossStatisticsGroupSheetService actualLossStatisticsGroupSheetService;
    @Override
    public void saveBatch(String startDate,String endDate) {
        ActualLossStatisticsParamGroupDTO actualLossStatisticsParamGroupDTO = new ActualLossStatisticsParamGroupDTO();
        actualLossStatisticsParamGroupDTO.setStartDate(startDate);
        actualLossStatisticsParamGroupDTO.setEndDate(endDate);
        List<ActualLossStatisticsMesGroupDetailVO> actualLossStatisticsMesGroupDetailVOS = actualLossStatisticsGroupSheetService.selectActualLosssDetail(actualLossStatisticsParamGroupDTO);
        List<GroupLossStatisticsDayPO> groupLossStatisticsDayPOS = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(actualLossStatisticsMesGroupDetailVOS)){
            for (ActualLossStatisticsMesGroupDetailVO actualLossStatisticsMesGroupDetailVO:actualLossStatisticsMesGroupDetailVOS){
                GroupLossStatisticsDayPO groupLossStatisticsDayPO = new GroupLossStatisticsDayPO();
                BeanUtil.copyProperties(actualLossStatisticsMesGroupDetailVO,groupLossStatisticsDayPO);
                groupLossStatisticsDayPO.setReportDate(DateUtil.parse(startDate,"yyyy-MM-dd"));
                groupLossStatisticsDayPOS.add(groupLossStatisticsDayPO);
            }
        }
        saveBatch(groupLossStatisticsDayPOS);
    }

    @Override
    public Pagination<GroupLossStatisticsDayVO> getList(GroupLossStatisticsDayParamDTO groupLossStatisticsDayParamDTO) {
        LambdaQueryWrapper<GroupLossStatisticsDayPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(GroupLossStatisticsDayPO::getReportDate,groupLossStatisticsDayParamDTO.getStartDate());
        queryWrapper.lt(GroupLossStatisticsDayPO::getReportDate,groupLossStatisticsDayParamDTO.getEndDate());
        IPage<GroupLossStatisticsDayPO> page = page(groupLossStatisticsDayParamDTO.getPage(),queryWrapper);
        List<GroupLossStatisticsDayPO> groupLossStatisticsDayPOS = page.getRecords();
        if (CollectionUtil.isEmpty(groupLossStatisticsDayPOS)){
            return Pagination.newInstance(null,0,0);
        }
        List<GroupLossStatisticsDayVO> groupLossStatisticsDayVOS = BeanUtil.copyToList(groupLossStatisticsDayPOS,GroupLossStatisticsDayVO.class);
        return Pagination.newInstance(groupLossStatisticsDayVOS,page.getTotal(),page.getPages());
    }
}
