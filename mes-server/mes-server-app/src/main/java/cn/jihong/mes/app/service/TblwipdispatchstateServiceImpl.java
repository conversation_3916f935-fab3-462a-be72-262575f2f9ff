package cn.jihong.mes.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.jihong.mes.api.model.dto.TblwipdispatchstateDTO;
import cn.jihong.mes.api.model.po.TblwipdispatchstatePO;
import cn.jihong.mes.app.config.DataSourceContextHolder;
import cn.jihong.mes.app.enums.DataSourceEnum;
import cn.jihong.mes.app.mapper.TblwipdispatchstateMapper;
import cn.jihong.mes.api.service.ITblwipdispatchstateService;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
@Service
public class TblwipdispatchstateServiceImpl extends JiHongServiceImpl<TblwipdispatchstateMapper, TblwipdispatchstatePO> implements ITblwipdispatchstateService {

    @Override
    public void saveBatch(List<TblwipdispatchstateDTO> tblwipdispatchstateDTOList) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum(DataSourceEnum.SITE_20.getCode()));
        if (CollectionUtil.isNotEmpty(tblwipdispatchstateDTOList)){
            List<TblwipdispatchstatePO> tblwipdispatchstatePOS = BeanUtil.copyToList(tblwipdispatchstateDTOList,TblwipdispatchstatePO.class);
            saveBatch(tblwipdispatchstatePOS);
        }
    }

    @Override
    public List<TblwipdispatchstateDTO> getListByLotNos(List<String> lotNos) {
        DataSourceContextHolder.setDataSourceType(DataSourceEnum.getDataSourceEnum(DataSourceEnum.SITE_20.getCode()));
        LambdaQueryWrapper<TblwipdispatchstatePO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TblwipdispatchstatePO::getLotno,lotNos);
        List<TblwipdispatchstatePO> tblwipdispatchstatePOS = list(queryWrapper);
        List<TblwipdispatchstateDTO> tblwipdispatchstateDTOList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(tblwipdispatchstatePOS)){
            tblwipdispatchstateDTOList = BeanUtil.copyToList(tblwipdispatchstatePOS,TblwipdispatchstateDTO.class);
            return tblwipdispatchstateDTOList;
        }
        return null;
    }

    @Override
    public void updateBatch(List<TblwipdispatchstateDTO> tblwipdispatchstateDTOList) {
        if (CollectionUtil.isNotEmpty(tblwipdispatchstateDTOList)){
            for(TblwipdispatchstateDTO tblwipdispatchstateDTO:tblwipdispatchstateDTOList){
                LambdaUpdateWrapper<TblwipdispatchstatePO> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(TblwipdispatchstatePO::getEquipmentno,tblwipdispatchstateDTO.getEquipmentno());
                updateWrapper.eq(TblwipdispatchstatePO::getLotno,tblwipdispatchstateDTO.getLotno());
                updateWrapper.eq(TblwipdispatchstatePO::getOpno,tblwipdispatchstateDTO.getOpno());
                updateWrapper.eq(TblwipdispatchstatePO::getSeq,tblwipdispatchstateDTO.getSeq());
                updateWrapper.set(TblwipdispatchstatePO::getDispEndTime,tblwipdispatchstateDTO.getDispEndTime());
                updateWrapper.set(TblwipdispatchstatePO::getDispStartTime,tblwipdispatchstateDTO.getDispStartTime());
                updateWrapper.set(TblwipdispatchstatePO::getQty,tblwipdispatchstateDTO.getQty());
                updateWrapper.set(TblwipdispatchstatePO::getRevisedate,tblwipdispatchstateDTO.getRevisedate());
                updateWrapper.set(TblwipdispatchstatePO::getReviser,tblwipdispatchstateDTO.getReviser());
                updateWrapper.set(TblwipdispatchstatePO::getStdDispEndTime,tblwipdispatchstateDTO.getStdDispEndTime());
                updateWrapper.set(TblwipdispatchstatePO::getStdDispStartTime,tblwipdispatchstateDTO.getStdDispStartTime());
                updateWrapper.set(TblwipdispatchstatePO::getWorkdate,tblwipdispatchstateDTO.getWorkdate());
                updateWrapper.set(TblwipdispatchstatePO::getChildProcessNo,tblwipdispatchstateDTO.getChildProcessNo());
                updateWrapper.set(TblwipdispatchstatePO::getChildProcessVersion,tblwipdispatchstateDTO.getChildProcessVersion());
                updateWrapper.set(TblwipdispatchstatePO::getQcLotFlag,tblwipdispatchstateDTO.getQcLotFlag());
                updateWrapper.set(TblwipdispatchstatePO::getDispQCLot,tblwipdispatchstateDTO.getDispQCLot());
                updateWrapper.set(TblwipdispatchstatePO::getShiftNO,tblwipdispatchstateDTO.getShiftNO());
                updateWrapper.set(TblwipdispatchstatePO::getCombinedTag,tblwipdispatchstateDTO.getCombinedTag());
                update(updateWrapper);
            }
        }
    }


}
