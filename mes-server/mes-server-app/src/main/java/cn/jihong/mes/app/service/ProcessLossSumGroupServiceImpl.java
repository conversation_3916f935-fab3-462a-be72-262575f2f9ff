package cn.jihong.mes.app.service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.util.OssUtils;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.api.model.dto.ProcessLossSumParamGroupDTO;
import cn.jihong.mes.api.model.vo.ActualLossStatisticsMesVO;
import cn.jihong.mes.api.model.vo.ProcessLossSumMesGroupVO;
import cn.jihong.mes.api.service.IProcessLossSumGroupService;
import cn.jihong.mes.app.property.AliyunOssProperty;
import cn.jihong.mes.app.util.TimeTypeUtil;
import cn.jihong.oa.erp.api.model.dto.ProcessLossSumDTO;
import cn.jihong.oa.erp.api.model.vo.ProcessLossSumGroupVO;
import cn.jihong.oa.erp.api.service.ISffbTService;
import cn.jihong.oa.erp.api.service.ISiteViewService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ProcessLossSumGroupServiceImpl implements IProcessLossSumGroupService {
    @DubboReference
    private ISffbTService sffbTService;
    @Autowired
    private RedisTemplate redisTemplate;
    @DubboReference
    private ISiteViewService siteViewService;
    @Resource
    private AliyunOssProperty aliyunOssProperty;
    @Override
    public List<ProcessLossSumMesGroupVO> selectProcessLossSum(ProcessLossSumParamGroupDTO processLossSumParamGroupDTO) {
        if (processLossSumParamGroupDTO == null){
            return null;
        }
        if (StringUtil.isEmpty(processLossSumParamGroupDTO.getStartDate())){
            throw new CommonException("开始日期不能为空");
        }else {
            if (!TimeTypeUtil.isLegalDate(processLossSumParamGroupDTO.getStartDate().length(), processLossSumParamGroupDTO.getStartDate(), "yyyy-MM-dd")){
                throw new CommonException("开始日期不符合格式：yyyy-MM-dd");
            }
        }
        if (StringUtil.isEmpty(processLossSumParamGroupDTO.getEndDate())){
            throw new CommonException("结束日期不能为空");
        }else {
            if (!TimeTypeUtil.isLegalDate(processLossSumParamGroupDTO.getEndDate().length(), processLossSumParamGroupDTO.getStartDate(), "yyyy-MM-dd")){
                throw new CommonException("结束日期不符合格式：yyyy-MM-dd");
            }
        }
        if (StringUtil.isEmpty(processLossSumParamGroupDTO.getFactoryCode())){
            throw new CommonException("公司不能为空");
        }

        ProcessLossSumDTO processLossSumDTO = new ProcessLossSumDTO();
        Date startDate = DateUtil.parse(processLossSumParamGroupDTO.getStartDate(),"yyyy-MM-dd");
        Date endDate = DateUtil.offsetDay(DateUtil.parse(processLossSumParamGroupDTO.getEndDate(),"yyyy-MM-dd"),1);
        processLossSumDTO.setStartDate(DateUtil.format(startDate,"yyyy-MM-dd"));
        processLossSumDTO.setEndDate(DateUtil.format(endDate,"yyyy-MM-dd"));
        processLossSumDTO.setProductCategoryNo(processLossSumParamGroupDTO.getProductCategoryNo());
        processLossSumDTO.setFactoryCode(processLossSumParamGroupDTO.getFactoryCode());
        processLossSumDTO.setProcessNo(processLossSumParamGroupDTO.getProcessNo());
        processLossSumDTO.setFactoryType(processLossSumParamGroupDTO.getFactoryType());
        List<ProcessLossSumGroupVO> processLossSumVOS = sffbTService.selectProcessLossSumGroup(processLossSumDTO);
        if (CollectionUtil.isNotEmpty(processLossSumVOS)){
            //计算损耗率
            BigDecimal zero = new BigDecimal("0");
            List<ProcessLossSumMesGroupVO> processLossSumMesGroupVOS = new ArrayList<>();
            for (ProcessLossSumGroupVO processLossSumVO:processLossSumVOS){
                ProcessLossSumMesGroupVO processLossSumMesGroupVO = new ProcessLossSumMesGroupVO();
                BeanUtil.copyProperties(processLossSumVO,processLossSumMesGroupVO);
                if (processLossSumVO.getInputQuantity() != null && processLossSumVO.getInputQuantity().compareTo(zero) == 1) {
                    //标准损耗率
                    if (processLossSumVO.getStandardLossQuantity()!=null){
                        processLossSumMesGroupVO.setStandardLossRate(processLossSumVO.getStandardLossQuantity()
                                .divide(processLossSumVO.getInputQuantity(), 4, RoundingMode.HALF_UP)
                                .movePointRight(2));
                    }
                    //额定损耗率
                    if (processLossSumVO.getRatedLossQuantity()!=null){
                        processLossSumMesGroupVO.setRatedLossRate(processLossSumVO.getRatedLossQuantity()
                                .divide(processLossSumVO.getInputQuantity(), 4, RoundingMode.HALF_UP)
                                .movePointRight(2));
                    }
                    //开单损耗率
                    if (processLossSumVO.getBillingLossQuantity()!=null){
                        processLossSumMesGroupVO.setBillingLossRate(processLossSumVO.getBillingLossQuantity()
                                .divide(processLossSumVO.getInputQuantity(), 4, RoundingMode.HALF_UP)
                                .movePointRight(2));
                    }
                    //实际损耗率
                    processLossSumMesGroupVO.setActualLossRate(processLossSumVO.getDefectiveProductNum()
                            .divide(processLossSumVO.getInputQuantity(), 4, RoundingMode.HALF_UP)
                            .movePointRight(2));
                    //产出比
                    processLossSumMesGroupVO.setOutputRatio(processLossSumVO.getQualifiedQuantity()
                            .divide(processLossSumVO.getInputQuantity(), 4, RoundingMode.HALF_UP)
                            .movePointRight(2));
                }

                processLossSumMesGroupVOS.add(processLossSumMesGroupVO);
            }
            return processLossSumMesGroupVOS;
        }
        return null;
    }

    @Override
    public String exportToExcel(ProcessLossSumParamGroupDTO processLossSumParamGroupDTO) {
        List<ProcessLossSumMesGroupVO> dataList = selectProcessLossSum(processLossSumParamGroupDTO);
        String url = getDataToExcel(dataList, ActualLossStatisticsMesVO.class,"集团损耗趋势表");
        return url;
    }

    private String getDataToExcel(Collection<?> data,
                                  Class<?> clz, String fileName) {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        EasyExcel.write(os, clz).sheet("模板").doWrite(() -> {
            // 分页查询数据
            return data;
        });

        ByteArrayInputStream byteArrayInputStream = null;
        try {
            byteArrayInputStream = new ByteArrayInputStream(os.toByteArray());
            String OBJECT_NAME = "temp";
            String dateStr = DateUtil.format(LocalDateTime.now(), "yyyyMMdd");
            String objectName = OBJECT_NAME + "/" + dateStr + "/" + System.currentTimeMillis()
                    + "/" + fileName + ".xlsx";
            String uploadFile = OssUtils.uploadFile(aliyunOssProperty.getAccessId(), aliyunOssProperty.getAccessKey(),
                    aliyunOssProperty.getHzEndpoint(), aliyunOssProperty.getCommissionBucketName(), objectName,
                    byteArrayInputStream);
            return uploadFile;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            // 关闭
            try {
                byteArrayInputStream.close();
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
            try {
                os.close();
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        return null;
    }
}
