package cn.jihong.mes.api.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 产品工序标准损耗设定表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
@Data
public class ProductProcessStandardLossSetVO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    private Long id;


    /**
     * 产品类型编号
     */
    private String productCategoryNo;


    /**
     * 产品类型名称
     */
    private String productCategoryName;


    /**
     * 产品工序编号
     */
    private String productionProcesseNo;


    /**
     * 产品工序名称
     */
    private String productionProcesseName;


    /**
     * 标准损耗(单位：%)
     */
    private BigDecimal standardLoss;


    /**
     * 是否删除1-是，0-否
     */
    private Boolean isDeleted;


    /**
     * 创建人
     */
    private Long createBy;


    /**
     * 编辑人
     */
    private Long updateBy;


    /**
     * 创建时间
     */
    private Date createTime;


    /**
     * 编辑时间
     */
    private Date updateTime;

}
