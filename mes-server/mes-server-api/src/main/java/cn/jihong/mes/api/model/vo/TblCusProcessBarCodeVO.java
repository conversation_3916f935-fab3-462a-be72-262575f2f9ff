package cn.jihong.mes.api.model.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Data
public class TblCusProcessBarCodeVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String processBarCode;

    private String productNo;

    private Double state;

    private String productName;

    private Double qty;

    private String unitNo;

    private String mono;

    private String baseLotNo;

    private String equipmentNo;

    private String equipmentName;

    private String nodeId;

    private String opNo;

    private Date createDateTime;

    private String userNo;

    private String username;

    private String createUserId;

    private String productType;

}
