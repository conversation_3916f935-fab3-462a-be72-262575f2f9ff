package cn.jihong.mes.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.dto.PageRequest;
import cn.jihong.mes.api.model.dto.ProductProcessStandardLossSetDTO;
import cn.jihong.mes.api.model.po.ProductProcessStandardLossSetPO;
import cn.jihong.mes.api.model.vo.ProductProcessStandardLossSetVO;
import cn.jihong.mybatis.api.service.IJiHongService;

/**
 * <p>
 * 产品工序标准损耗设定表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-03
 */
public interface IProductProcessStandardLossSetService extends IJiHongService<ProductProcessStandardLossSetPO> {
    /**
     * 保存产品工序标准损耗设定表
     * @param productProcessStandardLossSetDTO
     */
    void save(ProductProcessStandardLossSetDTO productProcessStandardLossSetDTO);

    /**
     * 编辑产品工序标准损耗设定表
     * @param productProcessStandardLossSetDTO
     */
    void edit(ProductProcessStandardLossSetDTO productProcessStandardLossSetDTO);

    /**
     * 删除
     * @param id
     */
    void delete(String id);

    Pagination<ProductProcessStandardLossSetVO> getList(ProductProcessStandardLossSetDTO productProcessStandardLossSetDTO, PageRequest pageRequest);


}
