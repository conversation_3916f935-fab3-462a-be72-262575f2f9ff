package cn.jihong.mes.api.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Data
public class TblCusProcessBarCodeHistoryDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String upProcessBarCode;

    private List<String> onProcessBarCode;

    private String productNo;

    private String productName;

    private String productType;

    private Double qty;

    private String mono;

    private String baseLotNo;

    private String unitNo;

    private String equipmentNo;

    private String equipmentName;

    private Date createDate;

    private String userNo;

    private String userName;

}
