package cn.jihong.mes.api.model.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-24
 */
@Data
public class TblCusProcessBarCodeBoxDTO extends BaseDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String processBarCode;

    private String boxId;

    private Double qty;

    private String mono;

    private String lotNo;

    private String itemNo;

    private String itemName;

    private String itemBarcodeType;

    private String packUserNo;

    private String packUsername;

    private Date packTime;

}
