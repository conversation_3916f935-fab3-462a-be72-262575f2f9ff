# 吉宏 MES 代码风格规范

## 1. 数据库 PO 类规范

### 1.1 基本结构

```java
@Getter
@Setter
@TableName("table_name")
public class EntityNamePO implements Serializable {
    private static final long serialVersionUID = 1L;

    // 常量定义
    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    // 其他字段常量...

    // 字段定义
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;

    @TableField(COMPANY_CODE)
    private String companyCode;

    // 其他字段...
}
```

### 1.2 注解使用

- 使用 `@Getter` 和 `@Setter` 注解，不使用 `@Data`
- 使用 `@TableName` 指定表名
- 使用 `@TableId` 标记主键，指定主键类型
- 使用 `@TableField` 标记字段，指定数据库字段名
- 使用 `@TableLogic` 标记逻辑删除字段
- 使用 `@Version` 标记乐观锁字段
- 使用 `@TableField(fill = FieldFill.INSERT)` 标记创建时间等自动填充字段
- 使用 `@TableField(fill = FieldFill.INSERT_UPDATE)` 标记更新时间等自动填充字段

### 1.3 字段定义

- 每个字段都要有详细的中文注释
- 每个字段都要定义对应的静态常量
- 字段名使用驼峰命名，对应的常量使用大写下划线命名
- 所有 PO 类都要实现 `Serializable` 接口
- 所有 PO 类都要定义 `serialVersionUID`

### 1.4 示例

```java
@Getter
@Setter
@TableName("product_ticket")
public class ProductTicketPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String MACHINE_NAME = "machine_name";
    // 其他字段常量...

    /**
     * id
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;

    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;

    /**
     * 机台名称
     */
    @TableField(MACHINE_NAME)
    private String machineName;

    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

    // 其他字段...
}
```

## 2. VO/DTO 类规范

### 2.1 基本结构

```java
@Data
public class EntityNameInVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 字段说明
     */
    @NotNull(message = "xxx不能为空")
    private Type fieldName;

    // 其他字段...
}
```

### 2.2 注解使用

- 使用 `@Data` 注解
- 使用 `@NotNull`、`@NotBlank`、`@NotEmpty` 等注解进行参数校验
- 使用 `@JsonFormat` 注解格式化日期

### 2.3 命名规范

- 入参 VO 类命名为 `EntityNameInVO`
- 出参 VO 类命名为 `EntityNameOutVO`
- 查询条件 VO 类命名为 `EntityNameQueryInVO`
- 保存/更新 VO 类命名为 `EntityNameSaveInVO`
- DTO 类命名为 `EntityNameDTO`

## 3. Controller 类规范

### 3.1 基本结构

```java
/**
 * 功能描述
 *
 * <AUTHOR>
 * @since yyyy-MM-dd
 */
@RestController
@RequestMapping("/path")
@ShenyuSpringMvcClient(path = "/path/**")
public class EntityNameController {

    @Resource
    private IEntityNameService entityNameService;

    /**
     * 方法描述
     *
     * @param param 参数说明
     * @return 返回值说明
     */
    @PostMapping("/method")
    public StandardResult<ReturnType> methodName(@RequestBody @Valid ParamType param) {
        return StandardResult.resultCode(OperateCode.SUCCESS, entityNameService.method(param));
    }
}
```

### 3.2 注解使用

- 使用 `@RestController` 和 `@RequestMapping` 注解
- 使用 `@ShenyuSpringMvcClient` 注解
- 使用 `@PostMapping`、`@GetMapping`、`@PutMapping`、`@DeleteMapping` 等注解
- 使用 `@RequestBody` 和 `@Valid` 注解进行参数校验
- 使用 `@PathVariable` 注解获取路径参数

### 3.3 返回值规范

- 所有方法都返回 `StandardResult<T>` 类型
- 使用 `StandardResult.resultCode(OperateCode.SUCCESS, data)` 返回成功结果
- 明确指定泛型类型，如 `StandardResult<Long>`、`StandardResult<List<String>>`、`StandardResult<Boolean>` 等

## 4. Service 接口规范

### 4.1 基本结构

```java
/**
 * 功能描述
 *
 * <AUTHOR>
 * @since yyyy-MM-dd
 */
public interface IEntityNameService extends IJiHongService<EntityNamePO> {

    /**
     * 方法描述
     *
     * @param param 参数说明
     * @return 返回值说明
     */
    ReturnType methodName(ParamType param);
}
```

### 4.2 命名规范

- 接口名以 `I` 开头，后跟实体名和 `Service` 后缀
- 方法名使用动词开头，如 `get`、`find`、`query`、`list`、`page`、`save`、`update`、`delete` 等
- 分页查询方法命名为 `page`
- 列表查询方法命名为 `list` 或 `getList`
- 详情查询方法命名为 `getDetail` 或 `getById`
- 保存方法命名为 `save`
- 更新方法命名为 `update`
- 删除方法命名为 `delete` 或 `deleteById`

## 5. Service 实现类规范

### 5.1 基本结构

```java
/**
 * 功能描述
 *
 * <AUTHOR>
 * @since yyyy-MM-dd
 */
@Service
@DubboService
public class EntityNameServiceImpl
    extends JiHongServiceImpl<EntityNameMapper, EntityNamePO>
    implements IEntityNameService {

    @Resource
    private IOtherService otherService;

    @DubboReference
    private IRemoteService remoteService;

    /**
     * 方法描述
     *
     * @param param 参数说明
     * @return 返回值说明
     */
    @Override
    public ReturnType methodName(ParamType param) {
        // 方法实现
    }
}
```

### 5.2 继承和实现

- 继承 `JiHongServiceImpl<EntityNameMapper, EntityNamePO>`
- 实现对应的 Service 接口
- 使用 `@Service` 注解标记为 Spring 服务
- 使用 `@DubboService` 注解标记为 Dubbo 服务（如需要）

### 5.3 依赖注入

- 使用 `@Resource` 注入本地服务
- 使用 `@DubboReference` 注入远程服务
- 使用 `@Autowired` 注入其他 Spring 组件

### 5.4 事务管理

- 使用 `@Transactional(rollbackFor = Exception.class)` 注解事务方法
- 保存、更新、删除等修改操作都要添加事务注解

## 6. 查询条件构建

### 6.1 基本用法

```java
LambdaQueryWrapper<EntityNamePO> queryWrapper = new LambdaQueryWrapper<>();
queryWrapper.eq(EntityNamePO::getCompanyCode, getCompanyCode())
    .eq(StringUtils.isNotBlank(param.getField1()), EntityNamePO::getField1, param.getField1())
    .like(StringUtils.isNotBlank(param.getField2()), EntityNamePO::getField2, param.getField2())
    .ge(param.getStartTime() != null, EntityNamePO::getCreateTime, param.getStartTime())
    .le(param.getEndTime() != null, EntityNamePO::getCreateTime, param.getEndTime())
    .orderByDesc(EntityNamePO::getCreateTime);
```

### 6.2 条件判断

- 使用 `eq`、`ne`、`gt`、`ge`、`lt`、`le`、`like`、`in` 等方法构建条件
- 条件判断使用链式调用
- 字符串判空使用 `StringUtils.isNotBlank()`
- 对象判空使用 `xxx != null`
- 集合判空使用 `CollectionUtil.isNotEmpty()`

### 6.3 排序

- 使用 `orderByDesc` 或 `orderByAsc` 方法添加排序条件
- 默认按创建时间倒序排序

## 7. 数据转换

### 7.1 对象转换

```java
// 单个对象转换
EntityNameOutVO outVO = BeanUtil.copyProperties(po, EntityNameOutVO.class);

// 列表转换
List<EntityNameOutVO> outVOList = BeanUtil.copyToList(poList, EntityNameOutVO.class);
```

### 7.2 列表转换

```java
// 使用 stream 进行复杂转换
List<EntityNameOutVO> outVOList = poList.stream()
    .map(po -> {
        EntityNameOutVO outVO = BeanUtil.copyProperties(po, EntityNameOutVO.class);
        // 其他处理...
        return outVO;
    })
    .toList();
```

## 8. 分页查询规范

### 8.1 基本用法

```java
@Override
public Pagination<EntityNameOutVO> page(EntityNameQueryInVO inVO) {
    LambdaQueryWrapper<EntityNamePO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(EntityNamePO::getCompanyCode, getCompanyCode())
        // 其他条件...
        .orderByDesc(EntityNamePO::getCreateTime);

    IPage<EntityNamePO> page =
        page(new Page<>(inVO.getPageNum(), inVO.getPageSize()), queryWrapper);
    if (CollectionUtil.isEmpty(page.getRecords())) {
        return Pagination.newInstance(null);
    }
    List<EntityNameOutVO> records = BeanUtil.copyToList(page.getRecords(), EntityNameOutVO.class);
    return Pagination.newInstance(records, page.getTotal(), page.getPages());
}
```

### 8.2 返回值处理

- 先判断 `CollectionUtil.isEmpty(page.getRecords())`
- 空结果返回 `Pagination.newInstance(null)`
- 有结果返回 `Pagination.newInstance(records, page.getTotal(), page.getPages())`

## 9. 异常处理

### 9.1 业务异常

```java
if (condition) {
    throw new CommonException("错误信息");
}
```

### 9.2 参数校验

- 使用 `@Valid` 注解进行参数校验
- 使用 `@NotNull`、`@NotBlank`、`@NotEmpty` 等注解标记必填字段

## 10. 枚举使用

### 10.1 枚举定义

```java
@Getter
public enum StatusEnum implements IntCodeEnum {

    ACTIVE("1", "激活"),
    INACTIVE("0", "未激活"),
    ;

    private String code;
    private String name;

    StatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static StatusEnum getByCode(String code) {
        for (StatusEnum value : StatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的状态");
    }

    @Override
    public <T extends Enum<?>> Object getCode(T enumValue) {
        return ((StatusEnum) enumValue).getCode();
    }
}
```

### 10.2 枚举使用

```java
// 获取枚举值
String code = StatusEnum.ACTIVE.getCode();
String name = StatusEnum.ACTIVE.getName();

// 根据 code 获取枚举
StatusEnum status = StatusEnum.getByCode("1");

// 获取整型 code
Integer intCode = StatusEnum.ACTIVE.getIntCode();
```

## 11. 代码格式

### 11.1 注释规范

- 类注释包含描述、作者、时间
- 方法注释包含描述、参数说明、返回值说明
- 复杂逻辑添加行内注释

### 11.2 命名规范

- 类名使用大驼峰命名法
- 方法名和变量名使用小驼峰命名法
- 常量使用大写下划线命名法
- 包名使用小写字母

### 11.3 缩进和空行

- 使用 4 个空格缩进
- 方法之间添加一个空行
- 逻辑块之间添加空行

## 12. 其他规范

### 12.1 安全规范

- 所有查询都要加上公司编码条件 `getCompanyCode()`
- 所有查询都要考虑权限控制
- 敏感信息不要直接返回给前端

### 12.2 性能规范

- 避免大事务
- 避免循环中进行数据库操作
- 合理使用批量操作
- 合理设置查询条件，避免全表扫描

## 13. 常见代码示例

### 13.1 分页查询示例

```java
@Override
public Pagination<ProductOutVO> page(ProductQueryInVO inVO) {
    LambdaQueryWrapper<ProductPO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(ProductPO::getCompanyCode, getCompanyCode())
        .like(StringUtils.isNotBlank(inVO.getCode()),
            ProductPO::getCode, inVO.getCode())
        .orderByDesc(ProductPO::getCreateTime);

    IPage<ProductPO> page =
        page(new Page<>(inVO.getPageNum(), inVO.getPageSize()), queryWrapper);
    if (CollectionUtil.isEmpty(page.getRecords())) {
        return Pagination.newInstance(null);
    }
    List<ProductOutVO> records = BeanUtil.copyToList(page.getRecords(), ProductOutVO.class);
    return Pagination.newInstance(records, page.getTotal(), page.getPages());
}
```

### 13.2 保存方法示例

```java
@Override
@Transactional(rollbackFor = Exception.class)
public boolean save(ProductSaveInVO inVO) {
    ProductPO po = new ProductPO();
    BeanUtil.copyProperties(inVO, po);
    po.setCompanyCode(getCompanyCode());

    // 检查编码是否重复
    LambdaQueryWrapper<ProductPO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(ProductPO::getCompanyCode, getCompanyCode())
            .eq(ProductPO::getCode, inVO.getCode());
    if (count(queryWrapper) > 0) {
        throw new CommonException("编码已存在");
    }

    return save(po);
}
```

### 13.3 获取详情示例

```java
@Override
public ProductOutVO getDetail(Long id) {
    LambdaQueryWrapper<ProductPO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(ProductPO::getId, id)
            .eq(ProductPO::getCompanyCode, getCompanyCode());

    ProductPO po = getOne(queryWrapper);
    if (po == null) {
        return null;
    }

    return BeanUtil.copyProperties(po, ProductOutVO.class);
}
```

### 13.4 列表查询示例

```java
@Override
public List<ProductOutVO> list(ProductQueryInVO inVO) {
    LambdaQueryWrapper<ProductPO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(ProductPO::getCompanyCode, getCompanyCode())
        .eq(StringUtils.isNotBlank(inVO.getStatus()), ProductPO::getStatus, inVO.getStatus())
        .orderByDesc(ProductPO::getCreateTime);

    List<ProductPO> list = list(queryWrapper);
    if (CollectionUtil.isEmpty(list)) {
        return Collections.emptyList();
    }

    return BeanUtil.copyToList(list, ProductOutVO.class);
}
```

### 13.5 更新方法示例

```java
@Override
@Transactional(rollbackFor = Exception.class)
public boolean update(ProductUpdateInVO inVO) {
    // 检查记录是否存在
    ProductPO existPO = getById(inVO.getId());
    if (existPO == null) {
        throw new CommonException("记录不存在");
    }

    // 检查公司编码是否一致
    if (!existPO.getCompanyCode().equals(getCompanyCode())) {
        throw new CommonException("无权操作该记录");
    }

    // 检查编码是否重复
    if (StringUtils.isNotBlank(inVO.getCode()) && !existPO.getCode().equals(inVO.getCode())) {
        LambdaQueryWrapper<ProductPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductPO::getCompanyCode, getCompanyCode())
                .eq(ProductPO::getCode, inVO.getCode());
        if (count(queryWrapper) > 0) {
            throw new CommonException("编码已存在");
        }
    }

    // 更新记录
    ProductPO po = new ProductPO();
    BeanUtil.copyProperties(inVO, po);

    return updateById(po);
}
```

### 13.6 删除方法示例

```java
@Override
@Transactional(rollbackFor = Exception.class)
public boolean delete(Long id) {
    // 检查记录是否存在
    ProductPO existPO = getById(id);
    if (existPO == null) {
        throw new CommonException("记录不存在");
    }

    // 检查公司编码是否一致
    if (!existPO.getCompanyCode().equals(getCompanyCode())) {
        throw new CommonException("无权操作该记录");
    }

    // 逻辑删除
    return removeById(id);
}
```

### 13.7 批量操作示例

```java
@Override
@Transactional(rollbackFor = Exception.class)
public boolean batchDelete(List<Long> ids) {
    if (CollectionUtil.isEmpty(ids)) {
        return true;
    }

    // 检查记录是否存在且属于当前公司
    LambdaQueryWrapper<ProductPO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.in(ProductPO::getId, ids)
            .eq(ProductPO::getCompanyCode, getCompanyCode());
    List<ProductPO> existList = list(queryWrapper);

    if (existList.size() != ids.size()) {
        throw new CommonException("部分记录不存在或无权操作");
    }

    // 批量删除
    return removeByIds(ids);
}
```