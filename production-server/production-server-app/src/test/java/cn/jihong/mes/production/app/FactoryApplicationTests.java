package cn.jihong.mes.production.app;

import cn.jihong.common.exception.CommonException;
import cn.jihong.mes.production.api.service.IProductSettlementEndProductionService;
import cn.jihong.oa.erp.api.model.vo.GetProcessSeqByTickNoOutVO;
import cn.jihong.oa.erp.api.service.ISfcbTService;
import com.alibaba.fastjson.JSON;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;
import java.util.stream.Collectors;

@SpringBootTest
class FactoryApplicationTests {

    @DubboReference
    private ISfcbTService iSfcbTService;

    @Autowired
    private IProductSettlementEndProductionService iProductSettlementEndProductionService;

    @Test
    void contextLoads() {
        /*List<GetProcessSeqByTickNoOutVO> poList = iSfcbTService.getProcessSeqByTickNo("125-S299-23080000057");
        convertSeq(poList);*/

        iProductSettlementEndProductionService.getSettlementEndProductionList("125-S299-23080000057");
    }

    private List<GetProcessSeqByTickNoOutVO> convertSeq(List<GetProcessSeqByTickNoOutVO> poList){
        List<GetProcessSeqByTickNoOutVO> list = new ArrayList<>();
        Map<String,GetProcessSeqByTickNoOutVO> map = poList.stream().collect(Collectors.toMap(GetProcessSeqByTickNoOutVO::getSfcb003,o->o));
        GetProcessSeqByTickNoOutVO outVO = map.values().stream().filter(t->t.getSfcb007().equals("INIT")).findFirst().orElse(null);
        int seq = 1;
        if(Objects.nonNull(outVO)){
            outVO.setProcessSeq(seq);
            list.add(outVO);
            while (Objects.nonNull(map.get(outVO.getSfcb009())) && !map.get(outVO.getSfcb009()).getSfcb009().equals("END")){
                seq++;
                outVO = map.get(outVO.getSfcb009());
                outVO.setProcessSeq(seq);
                list.add(outVO);
            }
            if(outVO.getProcessSeq() > 1) {
                GetProcessSeqByTickNoOutVO endOutVO = map.get(outVO.getSfcb009());
                endOutVO.setProcessSeq(seq+1);
                list.add(endOutVO);
            }
        }else {
            throw new CommonException("工程单没有起始工序顺序号");
        }
        return list.stream().sorted(Comparator.comparing(GetProcessSeqByTickNoOutVO::getProcessSeq)).collect(Collectors.toList());
    }
}
