//package cn.jihong.mes.production.app.service;
//
//import cn.jihong.common.enums.BooleanEnum;
//import cn.jihong.common.exception.CommonException;
//import cn.jihong.common.model.dto.request.UserInfo;
//import cn.jihong.common.model.vo.ResponseCodeOutVO;
//import cn.jihong.common.util.holder.UserContextHolder;
//import cn.jihong.mes.production.api.model.enums.ErpDocTypeEnum;
//import cn.jihong.mes.production.app.ProductionBootstrap;
//import cn.jihong.oa.erp.api.model.dto.webservice.CancelApplicationToErpInDTO;
//import cn.jihong.oa.erp.api.service.webservice.IInventoryToErpService;
//import com.alibaba.fastjson.JSON;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.dubbo.config.annotation.DubboReference;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.mock.web.MockHttpServletRequest;
//import org.springframework.mock.web.MockHttpServletResponse;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.web.context.request.RequestContextHolder;
//import org.springframework.web.context.request.ServletRequestAttributes;
//
//import java.util.Arrays;
//
//@Slf4j
//@SpringBootTest(classes = ProductionBootstrap.class)
//@RunWith(SpringJUnit4ClassRunner.class)
//// @ActiveProfiles(value = "dev")
//@ActiveProfiles(value = "prod")
//public class ProductMachineDayServiceImplTest {
//
//    private MockHttpServletRequest request;
//    private MockHttpServletResponse response;
//
//    @DubboReference(url = "dubbo://10.0.0.176:20889")
//    private IInventoryToErpService iInventoryToErpService;
//
//    @Test
//    public void testCancelApplicationToErp() {
//        // 扣料单号
//        String deductionNos = "125-S319-24120000838";
//
//        request = new MockHttpServletRequest();
//        response = new MockHttpServletResponse();
//        UserInfo userInfo = new UserInfo();
//        userInfo.setCompanySite("SITE-20");
//        userInfo.setId(19585L);
//        userInfo.setWorkcode("JHXM03251");
//        userInfo.setName("赖兴隆");
//        UserContextHolder.setUserInfo(userInfo);
//        request.setAttribute("userContext", userInfo);
//        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
//
//        // 删除erp中的数据
//        CancelApplicationToErpInDTO inDTO = new CancelApplicationToErpInDTO();
//        inDTO.setProg(ErpDocTypeEnum.STRIPPING.getCode());
//        inDTO.setDocType("1");
//        inDTO.setDocNos(Arrays.asList(deductionNos));
//        log.info("---CancelApplicationToErpInDTO:{}", JSON.toJSONString(inDTO));
//        ResponseCodeOutVO responseCodeOutVO = null;
//        try {
//            responseCodeOutVO = iInventoryToErpService.cancelApplicationToErp(inDTO);
//        } catch (Exception e) {
//            log.error("---取消扣料失败:{}", e.getMessage());
//            throw new CommonException("取消扣料失败：" + e.getMessage());
//        }
//        if (responseCodeOutVO.getCode().equals(BooleanEnum.FALSE.getCode())) {
//            throw new CommonException(responseCodeOutVO.getMessage());
//        }
//
//    }
//}