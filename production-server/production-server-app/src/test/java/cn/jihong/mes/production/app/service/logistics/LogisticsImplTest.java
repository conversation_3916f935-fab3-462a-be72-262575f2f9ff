package cn.jihong.mes.production.app.service.logistics;

import cn.jihong.mes.production.api.service.logistics.ILogisticsService;
import cn.jihong.mes.production.app.ProductionBootstrap;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@SpringBootTest(classes = ProductionBootstrap.class)
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles(value = "dev")
public class LogisticsImplTest {

    @Resource
    private ILogisticsService iLogisticsService;

//    @Test
//    public void callForMaterial() {
//        iLogisticsService.callForMaterial("111","二期EKOFA1350-6 2#柔印机:柔印EKOFA1350-6 2#","柔印");
//    }
//
//    @Test
//    public void pauseCallMaterialByMeshId() {
//        iLogisticsService.pauseCallMaterialByMeshId("1011");
//        iLogisticsService.startCallMaterialByMeshId("1011");
//        iLogisticsService.itemQueryByCondition("1011");
//    }
//
//    @Test
//    public void startCallMaterialByMeshId() {
//        iLogisticsService.startCallMaterialByMeshId("1001");
//    }
//
//    @Test
//    public void itemQueryByCondition() {
//        iLogisticsService.itemQueryByCondition("1011");
//    }



    // ============================================================================

    private MockHttpServletRequest request;

//    @Test
//    public void internetTape() {
//        request = new MockHttpServletRequest();
//        UserInfo userInfo = new UserInfo();
//        userInfo.setCompanySite("SITE-20");
//        userInfo.setId(19585L);
//        userInfo.setWorkcode("JHXM03251");
//        userInfo.setName("赖兴隆");
//        UserContextHolder.setUserInfo(userInfo);
//        request.setAttribute("userContext", userInfo);
//        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
//        iLogisticsService.internetTape("125-S299-24100000030","097","1001", BigDecimal.ZERO);
//    }


//    StandardResult startPalletAutoCreator(ProductionMachineConfigPO machineConfig);
//    StandardResult pausePalletAutoCreator(ProductionMachineConfigPO machineConfig);
//
//    StandardResult updatePalletAutoCreator(String planTickNo, String processCode,
//                                           String machineName, BigDecimal palletQuquantity,
//                                           String palletId,ProductionMachineConfigPO machineConfig);
//
//    @Test
//    public void startPalletAutoCreator() {
//        request = new MockHttpServletRequest();
//        UserInfo userInfo = new UserInfo();
//        userInfo.setCompanySite("SITE-20");
//        userInfo.setId(19585L);
//        userInfo.setWorkcode("JHXM03251");
//        userInfo.setName("赖兴隆");
//        UserContextHolder.setUserInfo(userInfo);
//        request.setAttribute("userContext", userInfo);
//        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
//
//        ProductionMachineConfigPO machineConfig = new ProductionMachineConfigPO();
//        machineConfig.setExternalMachineId("3601");
//        iLogisticsService.startPalletAutoCreator(machineConfig);
//    }
//
//    @Test
//    public void pausePalletAutoCreator() {
//        request = new MockHttpServletRequest();
//        UserInfo userInfo = new UserInfo();
//        userInfo.setCompanySite("SITE-20");
//        userInfo.setId(19585L);
//        userInfo.setWorkcode("JHXM03251");
//        userInfo.setName("赖兴隆");
//        UserContextHolder.setUserInfo(userInfo);
//        request.setAttribute("userContext", userInfo);
//        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
//        ProductionMachineConfigPO machineConfig = new ProductionMachineConfigPO();
//        machineConfig.setExternalMachineId("3601");
//        iLogisticsService.pausePalletAutoCreator(machineConfig);
//    }
//
//    @Test
//    public void updatePalletAutoCreator() {
//        request = new MockHttpServletRequest();
//        UserInfo userInfo = new UserInfo();
//        userInfo.setCompanySite("SITE-20");
//        userInfo.setId(19585L);
//        userInfo.setWorkcode("JHXM03251");
//        userInfo.setName("赖兴隆");
//        UserContextHolder.setUserInfo(userInfo);
//        request.setAttribute("userContext", userInfo);
//        RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(request));
//        iLogisticsService.updatePalletAutoCreator("125-S299-24100000030","097","3601", BigDecimal.ZERO,null,null);
//    }

}