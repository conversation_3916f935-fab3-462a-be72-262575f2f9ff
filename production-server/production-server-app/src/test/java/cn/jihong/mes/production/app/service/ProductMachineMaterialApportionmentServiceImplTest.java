package cn.jihong.mes.production.app.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import cn.jihong.mes.production.app.ProductionBootstrap;

@SpringBootTest(classes = ProductionBootstrap.class)
@RunWith(SpringJUnit4ClassRunner.class)
@ActiveProfiles(value = "dev")
public class ProductMachineMaterialApportionmentServiceImplTest {


    @Test
    public void test() {

    }


    public static void main(String[] args) {
        String input1 = "ABCD";
        String hex1 = stringToHex(input1);
        System.out.println("ABCD 转成: " + hex1);  // 输出: ABCD 转成: 41 42 43 44

        String input2 = "2169";
        String hex2 = stringToHex(input2);
        System.out.println("2169 转成: " + hex2);  // 输出: 2169 转成: 32 31 36 39
    }

    // 将字符串转换为十六进制
    public static String stringToHex(String input) {
        StringBuilder hexString = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            String hexChar = Integer.toHexString(c).toUpperCase();  // 获取字符的十六进制表示，并转为大写
            hexString.append(hexChar).append(" ");  // 拼接十六进制值和空格
        }
        return hexString.toString().trim();  // 去掉末尾的空格
    }


}