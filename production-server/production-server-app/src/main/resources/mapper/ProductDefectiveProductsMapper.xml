<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductDefectiveProductsMapper">

    <select id="getDefectiveProductsRecords"
            resultType="cn.jihong.mes.production.api.model.vo.out.DefectiveProductsInfoOutVO">
        select
            pt.produce_date as produceDate,
            pt.shift as shift,
            pt.machine_name as machineName,
            pt.team_users as teamUsers,
            pt.process as process,
            pdp.id as id,
            pdp.create_by as createBy,
            pdp.pallet_code as palletCode,
            pdp.defective_products_reason as defectiveProductsReason,
            pdp.defective_products_reason_name as defectiveProductsReasonName,
            pdp.defective_products_quantity as defectiveProductsQuantity,
            pdp.unit as unit,
            pdp.defective_products_source as defectiveProductsSource,
            pdp.defective_products_source_name as defectiveProductsSourceName,
            pdp.pallet_code as palletCode,
            po.outbound_time as outboundTime
        FROM
            product_ticket pt
        LEFT JOIN
            product_defective_products pdp on pt.id = pdp.product_ticket_id
        LEFT JOIN
            product_outbound po on pdp.pallet_code = po.pallet_code
        WHERE
            pt.id = #{productTicketId}
            and pt.company_code = #{companyCode}
            AND pt.deleted = 0
            AND pdp.deleted = 0
    </select>

    <select id="getDefectiveProductsRecordsByTicketBase"
            resultType="cn.jihong.mes.production.api.model.vo.out.DefectiveProductsInfoOutVO">
        select
            pt.produce_date as produceDate,
            pt.shift as shift,
            pt.machine_name as machineName,
            pt.team_users as teamUsers,
            pt.process as process,
            pdp.id as id,
            pdp.create_by as createBy,
            pdp.pallet_code as palletCode,
            pdp.defective_products_reason as defectiveProductsReason,
            pdp.defective_products_reason_name as defectiveProductsReasonName,
            pdp.defective_products_quantity as defectiveProductsQuantity,
            pdp.unit as unit,
            pdp.defective_products_source as defectiveProductsSource,
            pdp.defective_products_source_name as defectiveProductsSourceName,
            pdp.pallet_code as palletCode,
            po.outbound_time as outboundTime
        FROM
            product_ticket pt
        LEFT JOIN
            product_defective_products pdp on pt.id = pdp.product_ticket_id
        LEFT JOIN
            product_outbound po on pdp.pallet_code = po.pallet_code
        WHERE
            pt.machine_name = #{productTicketBaseDTO.machineName}
            AND pt.produce_date = #{productTicketBaseDTO.produceDate}
            AND pt.shift = #{productTicketBaseDTO.shift}
            AND pt.plan_ticket_no = #{productTicketBaseDTO.planTicketNo}
            AND pt.deleted = 0
            and pdp.deleted = 0
    </select>

    <select id="getListByDefectiveSourceName" resultType="cn.jihong.mes.production.api.model.vo.out.GetListByDefectiveSourceNameOutVO">
        SELECT
        b.id,
        a.defective_products_source_name,
        ( CASE WHEN  a.defective_products_quantity  IS NULL THEN 0 ELSE  a.defective_products_quantity  END ) AS defective_products_quantity,
        b.real_product
        FROM product_ticket b
        LEFT JOIN ( SELECT product_ticket_id, defective_products_source_name, SUM( defective_products_quantity ) AS defective_products_quantity FROM product_defective_products WHERE deleted = 0 GROUP BY product_ticket_id, defective_products_source_name ) a
          ON a.product_ticket_id = b.id
        WHERE a.product_ticket_id = #{inVO.productTicketId} AND b.deleted = 0 and b.status = 70
        <if test="inVO.process != null and inVO.process != '' ">
            AND a.defective_source_name = #{inVO.process}
        </if>

    </select>


    <select id="getDefectListByTicketNoAndProcess" resultType="cn.jihong.mes.production.api.model.vo.out.GetDefectListByTicketNoAndProcessOutVO">
        SELECT
            ( CASE WHEN  SUM(a.defective_products_quantity)  IS NULL THEN 0 ELSE  SUM(a.defective_products_quantity)  END ) AS defective_products_quantity,
            b.process
        FROM product_ticket b
            LEFT JOIN ( SELECT product_ticket_id, SUM( defective_products_quantity ) AS defective_products_quantity FROM product_defective_products WHERE deleted = 0 GROUP BY product_ticket_id ) a
                  ON a.product_ticket_id = b.id
        WHERE
            b.deleted = 0 AND b.status = 70 AND  b.plan_ticket_no = #{productTicketNo}
        GROUP BY b.process
    </select>
</mapper>
