<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.jihong.mes.production.app.mapper.ProductMaterialOperationRecordsMapper">

    <select id="getMaterialRecords" resultType="cn.jihong.mes.production.api.model.vo.out.MaterialRecordsOutVO">
        SELECT
            pr.id,
            pr.operation_type,
            pr.ticket_type,
            pr.ticket_number,
            pr.operator as createBy,
            pr.original_quantity,
            pr.consumption_quantity,
            pr.remaining_quantity,
            pr.machine_stop_no,
            pr.remaining_reason,
            pm.material_barcode_no,
            pm.material_code,
            pm.material_name,
            pm.material_unit,
            pm.material_warehousing_time,
            pr.create_time as loadingTime,
            pr.create_time as downLoadingTime,
            pr.original_quantity as loading_quantity,
            pt.company_code,
            pt.produce_date,
            pt.machine_name,
            pt.process_type,
            pt.process,
            pt.shift,
            pt.plan_ticket_no,
            pt.ticket_request_id,
            (case when pr.consumption_quantity <![CDATA[ > ]]> pr.original_quantity then 1 else 0 end) as over_claim,
            pr.workflow_request_id,
            pr.workflow_request_status
        FROM
            product_material_operation_records pr
            LEFT JOIN product_material pm ON pm.id = pr.product_material_id
            LEFT JOIN product_ticket pt ON pt.id = pr.product_ticket_id
        WHERE
            pr.deleted = 0
            AND pm.deleted = 0
            AND pt.deleted = 0
            AND pt.company_code = #{companyCode}
            and pt.id = #{productTicketPageInVO.productTicketId}
            and pr.operation_type = #{operationType}
        order by pr.id desc
    </select>
    
    <select id="getMaterialRecordsByTicketBase"
            resultType="cn.jihong.mes.production.api.model.vo.out.MaterialRecordsOutVO">
        SELECT
            pr.id,
            pr.operation_type,
            pr.ticket_type,
            pr.ticket_number,
            pr.operator as createBy,
            pr.original_quantity,
            pr.consumption_quantity,
            pr.remaining_quantity,
            pr.machine_stop_no,
            pr.remaining_reason,
            pm.material_barcode_no,
            pm.material_code,
            pm.material_name,
            pm.material_unit,
            pm.material_warehousing_time,
            pm.loading_time,
            pr.original_quantity as loading_quantity,
            pt.company_code,
            pt.produce_date,
            pt.machine_name,
            pt.process_type,
            pt.process,
            pt.shift,
            pt.plan_ticket_no,
            pt.ticket_request_id
        FROM
            product_material_operation_records pr
            LEFT JOIN product_material pm ON pm.id = pr.product_material_id
            LEFT JOIN product_ticket pt ON pt.id = pr.product_ticket_id
        WHERE
            pr.deleted = 0
            AND pm.deleted = 0
            AND pt.deleted = 0
            AND pt.machine_name = #{productTicketBaseDTO.machineName}
            AND pt.produce_date = #{productTicketBaseDTO.produceDate}
            AND pt.shift = #{productTicketBaseDTO.shift}
            AND pt.plan_ticket_no = #{productTicketBaseDTO.planTicketNo}
            and pr.operation_type = 10  -- 只查询上料记录，  下料记录不展示
        order by pr.id desc
    </select>

    <select id="getByProductTicketIds"
            resultType="cn.jihong.mes.production.api.model.dto.ProductMaterialOperationRecordsDTO">
        SELECT
            pr.*,
            pm.material_code,
            pm.material_name,
            pm.material_unit,
            pm.material_type,
            pm.purchase_batch
        FROM
            product_material_operation_records pr
            LEFT JOIN product_material pm ON pm.id = pr.product_material_id
        WHERE
            pr.deleted = 0
            AND pm.deleted = 0
            and pr.operation_type in
            <foreach collection="operationTypes" item="operationType" index="index" open="(" close=")" separator=",">
                #{operationType}
            </foreach>
            AND pr.product_ticket_id in
            <foreach collection="productTicketIds" item="productTicketId" index="index" open="(" close=")" separator=",">
                #{productTicketId}
            </foreach>
        order by pr.id desc
    </select>
</mapper>
