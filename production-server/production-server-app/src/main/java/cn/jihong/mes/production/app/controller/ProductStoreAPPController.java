package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.constant.RedisCacheConstant;
import cn.jihong.mes.production.api.model.dto.EnumDTO;
import cn.jihong.mes.production.api.model.dto.ScanInfoDTO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.PalletStoreOutVO;
import cn.jihong.mes.production.api.model.vo.out.QueryStorageInfoOutVO;
import cn.jihong.mes.production.api.model.vo.out.SaveOutboundAndDefectiveInfoOutVO;
import cn.jihong.mes.production.api.service.IProductStoreService;
import cn.jihong.oa.erp.api.model.vo.PageListCommonInVO;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.stream.Collectors;

/**
 * 入库表APP端
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@RestController
@RequestMapping("/app/productStore")
@ShenyuSpringMvcClient(path = "/app/productStore/**")
public class ProductStoreAPPController {

    @Resource
    private IProductStoreService productStoreService;


    /**
     * 入库申请 -- 按箱入库
     */
    @PostMapping("/inboundRequestByBox")
    public StandardResult inboundRequestByBox(@RequestBody @Valid PushWmsAndErpInVO pushWmsAndErpInVO) {
        String LOCK_KEY = RedisCacheConstant.STORAGE  + pushWmsAndErpInVO.getPalletCode();
        productStoreService.inboundRequestByBox(LOCK_KEY,pushWmsAndErpInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }



    /**
     * 入库申请 -- 按托入库
     */
    @PostMapping("/inboundRequestByPallet")
    public StandardResult inboundRequestByPallet(@RequestBody @Valid InboundRequestByPalletInVO inboundRequestByPalletInVO) {
        String LOCK_KEY = RedisCacheConstant.STORAGE  + inboundRequestByPalletInVO.getPlanTicketNo();
        productStoreService.inboundRequestByPallet(LOCK_KEY,inboundRequestByPalletInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 保存出站和报不良信息
     */
    @PostMapping("/saveOutboundAndDefectiveInfo")
    public StandardResult<SaveOutboundAndDefectiveInfoOutVO> saveOutboundAndDefectiveInfo(@RequestBody @Valid SaveOutboundAndDefectiveInfo saveOutboundAndDefectiveInfo) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productStoreService.saveOutboundAndDefectiveInfo(saveOutboundAndDefectiveInfo));
    }

    /**
     * 按托入库 扫码
     * @param scanInfo
     * @return
     */
    @PostMapping("/scanByPallet")
    public StandardResult<PalletStoreOutVO> scanByPallet(@RequestBody @Valid ScanInfoDTO scanInfo) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productStoreService.scanByPallet(scanInfo));
    }


    /**
     * 按单入库 扫码
     * @param queryStoreByTicket
     * @return
     */
    @PostMapping("/queryStoreByTicket")
    public StandardResult<Pagination<QueryStorageInfoOutVO>> scanByTicket(@RequestBody @Valid QueryStoreByTicket queryStoreByTicket) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productStoreService.scanByTicket(queryStoreByTicket));
    }

    /**
     * 确认拉货
     * @param confirmInboundInVO
     * @return
     */
    @PostMapping("/confirmPulled")
    public StandardResult<String> confirmPulled(@RequestBody ConfirmInboundInVO confirmInboundInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productStoreService.confirmPulled(confirmInboundInVO));
    }

    /**
     * 确认入库 -- 按箱入库
     * @param confirmInboundInVO
     * @return
     */
    @PostMapping("/confirmInbound")
    public StandardResult<String> confirmInbound(@RequestBody @Valid ConfirmInboundInVO confirmInboundInVO) {
        String ids = confirmInboundInVO.getIds().stream().map(id -> String.valueOf(id)).collect(Collectors.joining(","));
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productStoreService.confirmInbound(ids,confirmInboundInVO));
    }

    /**
     * 确认入库 -- 按单入库
     * @param confirmInboundInVO
     * @return
     */
    @PostMapping("/confirmInboundByPallet")
    public StandardResult<String> confirmInboundByPallet(@RequestBody @Valid ConfirmInboundInVO confirmInboundInVO) {
        String storeApplyNos = confirmInboundInVO.getStoreApplyNos().stream().map(id -> String.valueOf(id)).collect(Collectors.joining(","));
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productStoreService.confirmInboundByPallet(storeApplyNos,confirmInboundInVO));
    }


//    /**
//     * 入库申请 -- 保存草稿
//     */
//    @PostMapping("/saveDraftRequest")
//    public StandardResult saveDraftRequest(@RequestBody @Valid List<PalletStoreOutVO> palletStoreOutVOS) {
//        productStoreService.saveDraftRequest(palletStoreOutVOS);
//        return StandardResult.resultCode(OperateCode.SUCCESS);
//    }
//
//    /**
//     * 入库申请 -- 获得草稿
//     */
//    @PostMapping("/getDraftRequest")
//    public StandardResult<List<PalletStoreOutVO>> getDraftRequest() {
//        return StandardResult.resultCode(OperateCode.SUCCESS,
//                productStoreService.getDraftRequest());
//    }
//
//    /**
//     * 拉货确认 --保存草稿
//     */
//    @PostMapping("/saveDraftPull")
//    public StandardResult saveDraftPull(@RequestBody @Valid List<PalletStoreOutVO> palletStoreOutVOS) {
//        productStoreService.saveDraftPull(palletStoreOutVOS);
//        return StandardResult.resultCode(OperateCode.SUCCESS);
//    }
//
//    /**
//     * 拉货确认 -- 获得草稿
//     */
//    @PostMapping("/getDraftPull")
//    public StandardResult<List<PalletStoreOutVO>> getDraftPull() {
//        return StandardResult.resultCode(OperateCode.SUCCESS,
//                productStoreService.getDraftPull());
//    }
//
//    /**
//     * 入库确认 -- 保存草稿
//     */
//    @PostMapping("/saveDraftWarehous")
//    public StandardResult saveDraftWarehous(@RequestBody @Valid List<PalletStoreOutVO> palletStoreOutVOS) {
//        productStoreService.saveDraftWarehous(palletStoreOutVOS);
//        return StandardResult.resultCode(OperateCode.SUCCESS);
//    }
//
//    /**
//     * 入库确认 -- 获得草稿
//     */
//    @PostMapping("/getDraftWarehous")
//    public StandardResult<List<PalletStoreOutVO>> getDraftWarehous() {
//        return StandardResult.resultCode(OperateCode.SUCCESS,
//                productStoreService.getDraftWarehous());
//    }

//
//    /**
//     * 设置默认储位
//     */
//    @PostMapping("/setDefaultStorage")
//    public StandardResult setDefaultStorage(@RequestBody @Valid EnumDTO enumDTO) {
//        productStoreService.setDefaultStorage(enumDTO);
//        return StandardResult.resultCode(OperateCode.SUCCESS);
//    }

    /**
     * 默认仓库
     */
    @GetMapping("/getDefaultWarehouse")
    public StandardResult<EnumDTO> getDefaultWarehouse() {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productStoreService.getDefaultWarehouse());
    }

    /**
     * 默认储位
     */
    @GetMapping("/getDefaultStorage")
    public StandardResult<EnumDTO> getDefaultStorage() {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productStoreService.getDefaultStorage());
    }

    /**
     * 仓库列表
     */
    @PostMapping("/getWarehouse")
    public StandardResult<Pagination<EnumDTO>> getWarehouse(@RequestBody PageListCommonInVO pageListCommonInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productStoreService.getWarehouse(pageListCommonInVO));
    }


    /**
     * 储位列表
     */
    @PostMapping("/getStorages")
    public StandardResult<Pagination<EnumDTO>> getStorages(@RequestBody PageListCommonInVO pageListCommonInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                productStoreService.getStorages(pageListCommonInVO));
    }


}

