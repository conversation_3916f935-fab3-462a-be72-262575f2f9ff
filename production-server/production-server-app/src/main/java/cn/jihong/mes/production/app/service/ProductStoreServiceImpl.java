package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.vo.ResponseCodeOutVO;
import cn.jihong.common.util.DateUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.production.api.model.constant.RedisCacheConstant;
import cn.jihong.mes.production.api.model.constant.SequenceConst;
import cn.jihong.mes.production.api.model.dto.*;
import cn.jihong.mes.production.api.model.enums.CompanyBoxEnum;
import cn.jihong.mes.production.api.model.enums.StoreOperationEnum;
import cn.jihong.mes.production.api.model.enums.StoreStatusEnum;
import cn.jihong.mes.production.api.model.enums.StoreTypeEnum;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodeDetailDetailPO;
import cn.jihong.mes.production.api.model.po.ProductStoreBoxPO;
import cn.jihong.mes.production.api.model.po.ProductStorePO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.PalletStoreOutVO;
import cn.jihong.mes.production.api.model.vo.out.QueryPalletInfoOutVO;
import cn.jihong.mes.production.api.model.vo.out.QueryStorageInfoOutVO;
import cn.jihong.mes.production.api.model.vo.out.SaveOutboundAndDefectiveInfoOutVO;
import cn.jihong.mes.production.api.service.*;
import cn.jihong.mes.production.app.aspect.RedisLock;
import cn.jihong.mes.production.app.mapper.ProductStoreMapper;
import cn.jihong.mes.production.app.service.boxCode.BoxCodeHandlerFactory;
import cn.jihong.mes.production.api.service.IBoxCodeService;
import cn.jihong.mes.production.app.util.redis.OrderSequenceUtil;
import cn.jihong.mes.production.app.util.redis.RedisKeyUtil;
import cn.jihong.mes.production.app.util.redis.RedisUtil;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.ehr.api.model.dto.UserDTO;
import cn.jihong.oa.ehr.api.service.IA01Service;
import cn.jihong.oa.erp.api.model.dto.SaveLsafTDTO;
import cn.jihong.oa.erp.api.model.dto.webservice.StorageApplicationToErpInDTO;
import cn.jihong.oa.erp.api.model.po.InabTPO;
import cn.jihong.oa.erp.api.model.po.SfacTPO;
import cn.jihong.oa.erp.api.model.po.SfeaTPO;
import cn.jihong.oa.erp.api.model.po.SfecTPO;
import cn.jihong.oa.erp.api.model.vo.ImaalTVO;
import cn.jihong.oa.erp.api.model.vo.PageListCommonInVO;
import cn.jihong.oa.erp.api.model.vo.SfaaTVO;
import cn.jihong.oa.erp.api.model.vo.XmamTVO;
import cn.jihong.oa.erp.api.service.*;
import cn.jihong.oa.erp.api.service.webservice.IInventoryToErpService;
import cn.jihong.wms.api.model.dto.BarcodeDetailDTO;
import cn.jihong.wms.api.model.dto.UpdateBarcodeStoreDTO;
import cn.jihong.wms.api.model.dto.request.CheckInboundRequest;
import cn.jihong.wms.api.model.po.SrmBarcodeDetailPO;
import cn.jihong.wms.api.model.po.SrmBarcodeStorePO;
import cn.jihong.wms.api.service.*;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 入库表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
@Slf4j
@DubboService
public class ProductStoreServiceImpl extends JiHongServiceImpl<ProductStoreMapper, ProductStorePO> implements IProductStoreService {

    @Resource
    private Executor taskExecutor;
    @Resource
    private IProductOutboundService productOutboundService;
    @Resource
    private IProductStoreRecordService productStoreRecordService;
    @Resource
    private BoxCodeHandlerFactory boxCodeHandlerFactory;
    @Resource
    private IProductStoreBoxService productStoreBoxService;
    @Resource
    private IProductBoxBarcodeDetailDetailService productBoxBarcodeDetailDetailService;

    @DubboReference
    private ISrmBarcodeDetailService srmBarcodeDetailService;
    @DubboReference(timeout = 300000,retries = 0)
    private ISrmBarcodeStoreService srmBarcodeStoreService;
    @DubboReference
    private ISfaaTService sfaaTService;
    @DubboReference
    private IImaalTService iImaalTService;
    @DubboReference
    private ISfacTService sfacTService;
    @DubboReference
    private ILsafTService iLsafTService;
    @DubboReference(timeout = 300000,retries = 0)
    private IInventoryToErpService iInventoryToErpService;
    @DubboReference
    private IXmamTService xmamTService;
    @DubboReference
    private IInabTService inabService;
    @DubboReference
    private IA01Service ia01Service;
    @DubboReference
    private IImaalTService imaalTService;
    @DubboReference
    private ISfeaTService sfeaTService;
    @DubboReference
    private IFinishedInboundDetailService finishedInboundDetailService;




    @Override
    public void inboundRequestByBox(String redisLockKey, PushWmsAndErpInVO pushWmsAndErpInVO) {
        productOutboundService.inboundRequestByBox(redisLockKey,pushWmsAndErpInVO);
    }

    @Override
    public void inboundRequestByPallet(String lock_key, InboundRequestByPalletInVO inboundRequestByPalletInVO) {
        productOutboundService.inboundRequestByPallet(lock_key,inboundRequestByPalletInVO);
    }

    @Override
    public SaveOutboundAndDefectiveInfoOutVO saveOutboundAndDefectiveInfo(SaveOutboundAndDefectiveInfo saveOutboundAndDefectiveInfo) {
        return productOutboundService.saveOutboundAndDefectiveInfo(saveOutboundAndDefectiveInfo);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String request(ProductStoreDTO productStoreDTO) {
        ProductStorePO byPalletCode = getByPalletCode(productStoreDTO.getPalletCode());
        if (Objects.nonNull(byPalletCode)){
            throw new CommonException("该托盘码已存在入库申请单");
        }
        List<String> palletCodes = Arrays.asList(productStoreDTO.getCaseCode().split(","));
        List<ProductStoreBoxPO> productStoreBoxPOS = productStoreBoxService.getByBoxCodes(palletCodes);
        if (CollectionUtil.isNotEmpty(productStoreBoxPOS)) {
            throw new CommonException("该箱码已存在:" + JSON.toJSONString(productStoreBoxPOS.stream().map(ProductStoreBoxPO::getBoxCode).collect(Collectors.toList())));
        }

        // 校验工单号
        verifyPlanTicketNo(productStoreDTO.getCaseCode(),productStoreDTO.getPlanTicketNo());

        ProductStorePO productStorePO = BeanUtil.copyProperties(productStoreDTO, ProductStorePO.class);
        productStorePO.setStoreStatus(StoreStatusEnum.REQUESTED.getCode());
        productStorePO.setCompanyCode(SecurityUtil.getCompanySite());
        productStorePO.setCreateBy(SecurityUtil.getUserId());
        productStorePO.setUpdateBy(SecurityUtil.getUserId());
        Long sequence =
            OrderSequenceUtil.getAndIncrementOrderSequence(SequenceConst.STORE_ORDER_KEY, SequenceConst.STORE_LOCK_KEY);
        String storeApplyNo = SequenceConst.STORE_SEQUENCE_PREFIX
            + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_FORMAT)
            + String.format("%04d", sequence);
        productStorePO.setStoreApplyNo(storeApplyNo);

        // 产品名称+编号
        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(productStoreDTO.getPlanTicketNo());

        SfacTPO sfacTPO = null;
        ImaalTVO imaalTVO = null;
        String caseCode = palletCodes.get(0);
        if (StringUtil.isNotEmpty(caseCode)) {
            String[] split1 = caseCode.split("#");
            if (split1.length > 0) {
                sfacTPO = sfacTService.getByProductNo(productStorePO.getPlanTicketNo(), split1[0]);
                imaalTVO = iImaalTService.getImaalTByProductNo(split1[0]);
            }
        }
        if (Objects.isNull(sfacTPO)) {
            sfacTPO = sfacTService.getByProductNo(productStorePO.getPlanTicketNo(), sfaaTVO.getSfaa010());
        }
        if (Objects.isNull(imaalTVO)) {
            imaalTVO = iImaalTService.getImaalTByProductNo(sfaaTVO.getSfaa010());
        }

        productStorePO.setProductNo(sfacTPO.getSfac001());
        productStorePO.setProductName(imaalTVO.getImaal003());
        // 按箱入库
        XmamTVO xmamTVO = xmamTService.getXmamTByProductionNo(sfaaTVO.getSfaa010(), SecurityUtil.getCompanySite());
        if (xmamTVO != null) {
            BigDecimal xmam008 = xmamTVO.getXmam008();
            productStorePO.setBoxSpace(xmam008);

            // 校验入库数量和箱规
            int length = productStoreDTO.getCaseCode().split(",").length;
            BigDecimal multiply = xmam008.multiply(BigDecimal.valueOf(length));
            if (productStorePO.getProducedQuantity().compareTo(multiply) != 0) {
                throw new CommonException("入库数量" + productStorePO.getProducedQuantity() + "不等于箱规" + xmam008 + " * 箱码数量" + length);
            }
        } else {
            BigDecimal producedQuantity = productStorePO.getProducedQuantity();
            String[] split = productStoreDTO.getCaseCode().split(",");
            int length =  split.length;
            BigDecimal boxSpace = producedQuantity.divide(BigDecimal.valueOf(length), 2, RoundingMode.HALF_UP);
            productStorePO.setBoxSpace(boxSpace);
        }

        // 批次 和 规格
        ImaalTVO imaalTByProductNo = imaalTService.getImaalTByProductNo(sfaaTVO.getSfaa010());
        if (imaalTByProductNo != null) {
            productStorePO.setStandard(imaalTByProductNo.getImaal004());
        }

        String[] split = productStoreDTO.getCaseCode().split(",");
        String bc = split[0];
        // 麦当劳码
        if (bc.contains("4DCD") || bc.contains("MES#")) {
            ProductBoxBarcodeDetailDetailPO productBoxBarcodeDetailDetailPO = productBoxBarcodeDetailDetailService.getByBarcodeNo(bc);
            productStorePO.setLotNo(productBoxBarcodeDetailDetailPO.getLotNo());
        } else {
            SrmBarcodeDetailPO srmBarcodeDetailPO = srmBarcodeDetailService.getSrmBarcodeDetailByBarcodeNo(bc);
            if (srmBarcodeDetailPO != null) {
                productStorePO.setLotNo(srmBarcodeDetailPO.getLotNo());
            } else {
                String[] boxCodeSpit = bc.split("#");
                productStorePO.setLotNo(boxCodeSpit[1]);
            }
        }

        save(productStorePO);

        // 操作记录
        ProductStoreRecordDTO productStoreRecordDTO = new ProductStoreRecordDTO();
        productStoreRecordDTO.setProductStoreId(productStorePO.getId());
        productStoreRecordDTO.setPalletCode(productStoreDTO.getPalletCode());
        productStoreRecordDTO.setDocNo(storeApplyNo);
        productStoreRecordDTO.setOperation(StoreOperationEnum.REQUEST.getCode());
        productStoreRecordDTO.setOperatorName(SecurityUtil.getUserName());
        productStoreRecordService.createRecord(productStoreRecordDTO);

        // 保存箱码信息
        List<ProductStoreBoxPO> productStoreBoxs = Lists.newArrayList();
        Arrays.asList(split).stream().forEach(boxCode -> {
            ProductStoreBoxPO productStoreBoxPO = new ProductStoreBoxPO();
            productStoreBoxPO.setProductStoreId(productStorePO.getId());
            productStoreBoxPO.setBoxCode(boxCode);
            productStoreBoxPO.setStoreType(productStorePO.getStoreType());
            productStoreBoxPO.setCompanyCode(SecurityUtil.getCompanySite());
            productStoreBoxPO.setProducedQuantity(productStorePO.getBoxSpace());
            productStoreBoxPO.setUnit(productStorePO.getUnit());
            productStoreBoxPO.setCreateBy(SecurityUtil.getUserId());
            productStoreBoxPO.setUpdateBy(SecurityUtil.getUserId());
            productStoreBoxPO.setCreateTime(new Date());
            productStoreBoxPO.setUpdateTime(new Date());
            productStoreBoxs.add(productStoreBoxPO);
        });
        productStoreBoxService.saveBatch(productStoreBoxs);

        return storeApplyNo;
    }



    @Override
    public PalletStoreOutVO scanByPallet(ScanInfoDTO scanInfo) {
        // 1 栈板码  2  箱码
        if (scanInfo.getBarcodeType() == 2) {
            List<ProductStoreBoxPO> byBoxCode = productStoreBoxService.getByBoxCode(scanInfo.getScan());
            if (CollectionUtil.isNotEmpty(byBoxCode)) {
                Long productStoreId = byBoxCode.get(0).getProductStoreId();
                ProductStorePO productStorePO = getById(productStoreId);
                scanInfo.setScan(productStorePO.getPalletCode());
            } else {
                throw new CommonException("未找到箱码[" + scanInfo.getScan() + "]的入库申请单");
            }
        }

        LambdaQueryWrapper<ProductStorePO> productStorePOLambdaQueryWrapper =
            Wrappers.lambdaQuery(ProductStorePO.class);
        productStorePOLambdaQueryWrapper.eq(ProductStorePO::getCompanyCode, SecurityUtil.getCompanySite())
            .eq(ProductStorePO::getPalletCode, scanInfo.getScan());
        ProductStorePO productStorePO = getOne(productStorePOLambdaQueryWrapper);

        if (Objects.isNull(productStorePO)) {
            throw new CommonException("未找到栈板码[" + scanInfo.getScan() + "]的入库申请单");
        }
        if (Integer.valueOf(1).equals(scanInfo.getScanType())) {
            if (!productStorePO.getStoreStatus().equals(StoreStatusEnum.REQUESTED.getCode())) {
                throw new CommonException("入库申请单[" + scanInfo.getScan() + "]状态是"
                        + StoreStatusEnum.getStoreStatusEnum(productStorePO.getStoreStatus()).getName() + "，请检查后扫码");
            }
        } else if (Integer.valueOf(2).equals(scanInfo.getScanType())) {
            if (productStorePO.getStoreStatus().equals(StoreStatusEnum.WAREHOUSED.getCode())
                    || productStorePO.getStoreStatus().equals(StoreStatusEnum.CONFIRMED.getCode())) {
                throw new CommonException("入库申请单[" + scanInfo.getScan() + "]状态是"
                        + StoreStatusEnum.getStoreStatusEnum(productStorePO.getStoreStatus()).getName() + "，请检查后扫码");
            }
        }

        PalletStoreOutVO palletStoreOutVO = BeanUtil.copyProperties(productStorePO, PalletStoreOutVO.class);
        if (StringUtils.isNotBlank(productStorePO.getCaseCode())
                && StoreTypeEnum.BY_BOX.getIntCode().equals(productStorePO.getStoreType())){
            String caseCode = palletStoreOutVO.getCaseCode();
            String[] split = caseCode.split(",");
            palletStoreOutVO.setBoxCount(split.length);
        }

        return palletStoreOutVO;
    }

    @Override
    public Pagination<QueryStorageInfoOutVO> scanByTicket(QueryStoreByTicket queryStoreByTicket) {
        QueryPalletInfoInVO queryPalletInfoInVO = new QueryPalletInfoInVO();
        queryPalletInfoInVO.setPlanTicketNo(queryStoreByTicket.getPlanTicketNo());
        queryPalletInfoInVO.setProductName(queryStoreByTicket.getProductName());
        queryPalletInfoInVO.setApplyTime(queryStoreByTicket.getApplyDate());
        queryPalletInfoInVO.setCompanyCode(SecurityUtil.getCompanySite());
//        IPage<QueryStorageInfoOutVO> queryStorageInfoOutVOIPage = baseMapper.queryStorageInfo(queryStoreByTicket.getPage(), queryPalletInfoInVO);
        IPage<QueryStorageInfoOutVO> queryStorageInfoOutVOIPage = baseMapper.queryStorageInfoIsAllIn(queryStoreByTicket.getPage(), queryPalletInfoInVO);

        if (CollectionUtil.isEmpty(queryStorageInfoOutVOIPage.getRecords())) {
            return Pagination.newInstance(null);
        }

        List<Long> userIds = queryStorageInfoOutVOIPage.getRecords().stream()
                .map(productStorePO -> Long.valueOf(productStorePO.getApplyUserName())).collect(Collectors.toList());

        List<UserDTO> userDTOS = ia01Service.getUserInfoByIds(userIds);
        Map<Long, String> userMap = userDTOS.stream().collect(Collectors.toMap(UserDTO::getId, UserDTO::getName, (k1, k2) -> k1));
        queryStorageInfoOutVOIPage.getRecords().stream().forEach(productStorePO -> {
            productStorePO.setApplyUserName(userMap.get(Long.valueOf(productStorePO.getApplyUserName())));
        });

        return Pagination.newInstance(queryStorageInfoOutVOIPage);
    }

    @Override
    @RedisLock
    @GlobalTransactional(rollbackFor = Exception.class, timeoutMills = 300000)
    @Transactional(rollbackFor = Exception.class)
    public String confirmInbound(String lock_key,ConfirmInboundInVO confirmInboundInVO) {
        if (CollectionUtil.isEmpty(confirmInboundInVO.getIds())) {
            throw new CommonException("入库单id不能为空");
        }
        List<ProductStorePO> productStorePOS = listByIds(confirmInboundInVO.getIds());
        if (CollectionUtil.isEmpty(productStorePOS)) {
            throw new CommonException("入库单不存在");
        }
        return confirmInbound(confirmInboundInVO, productStorePOS);
    }

    private String confirmInbound(ConfirmInboundInVO confirmInboundInVO, List<ProductStorePO> productStorePOS) {
        List<String> planTicketNos =
                productStorePOS.stream().map(ProductStorePO::getPlanTicketNo).distinct().collect(Collectors.toList());
        if (planTicketNos.size() > 1) {
            throw new CommonException("入库单号不一致:" + JSON.toJSONString(planTicketNos));
        }
        // 工单是否已结案
        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(planTicketNos.get(0));
        if (StringUtils.isNotBlank(sfaaTVO.getSfaastus()) && !sfaaTVO.getSfaastus().toUpperCase().equals("F")) {
            throw new CommonException("查询erp中工单不是生产中状态：" + planTicketNos.get(0));
        }


        InabTPO inabTPO = inabService.getStoragesNameByCode(confirmInboundInVO.getStorage());
        if (inabTPO != null) {
            confirmInboundInVO.setStorageName(StringUtil.isNotEmpty(inabTPO.getInab003()) ? inabTPO.getInab003() : inabTPO.getInab002() );
        }
        if (StringUtil.isNotBlank(confirmInboundInVO.getWarehouse())) {
            EnumDTO enumDTO = inabService.getWarehouseNameByCode(confirmInboundInVO.getWarehouse());
            if (enumDTO != null) {
                confirmInboundInVO.setWarehouseName(enumDTO.getName());
            }
        }

        List<ProductStoreRecordDTO> productStoreRecordDTOS = Lists.newArrayList();
        // 更新状态为已确认
        productStorePOS.stream().forEach(productStorePO -> {
            if (StoreStatusEnum.WAREHOUSED.getCode().equals(productStorePO.getStoreStatus())) {
                throw new CommonException("入库申请单号[" + productStorePO.getStoreApplyNo() + "]已入库,请勿重复入库！");
            }
            String storeStatusOld = productStorePO.getStoreStatus();

            productStorePO.setStoreStatus(StoreStatusEnum.CONFIRMED.getCode());
            productStorePO.setUpdateBy(SecurityUtil.getUserId());
            productStorePO.setStorage(confirmInboundInVO.getStorage());
            productStorePO.setWarehouse(confirmInboundInVO.getWarehouse());
            productStorePO.setUpdateTime(new Date());

            // 如果之前的已申请状态，则需要自动生成一条拉货记录
            if (storeStatusOld.equals(StoreStatusEnum.REQUESTED.getCode())) {
                Long sequence =
                        OrderSequenceUtil.getAndIncrementOrderSequence(SequenceConst.PULLED_ORDER_KEY, SequenceConst.PULLED_LOCK_KEY);
                String storePulledNo = SequenceConst.PULLED_SEQUENCE_PREFIX
                        + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_FORMAT)
                        + String.format("%04d", sequence);
                // 操作记录
                ProductStoreRecordDTO productStoreRecordDTO = new ProductStoreRecordDTO();
                productStoreRecordDTO.setProductStoreId(productStorePO.getId());
                productStoreRecordDTO.setPalletCode(productStorePO.getPalletCode());
                productStoreRecordDTO.setDocNo(storePulledNo);
                productStoreRecordDTO.setOperation(StoreOperationEnum.PULL.getCode());
                productStoreRecordDTO.setOperatorName(SecurityUtil.getUserName());
                productStoreRecordDTOS.add(productStoreRecordDTO);
            }
        });
        updateBatchById(productStorePOS);
        productStoreRecordService.createRecords(productStoreRecordDTOS);

        // 校验对应的入库单号是否全部都已经确认了
        Map<String, List<ProductStorePO>> applyMap = productStorePOS.stream()
                .collect(Collectors.groupingBy(ProductStorePO::getStoreApplyNo));
        if (CollectionUtil.isEmpty(applyMap)) {
            throw new CommonException("未找到未入库的的入库申请信息，请刷新页面检查");
        }

        Map<String, List<ProductStorePO>> map = getByApplyNo(productStorePOS.stream()
                .map(ProductStorePO::getStoreApplyNo).distinct()
                .collect(Collectors.toList())).stream()
                .collect(Collectors.groupingBy(ProductStorePO::getStoreApplyNo));

        List<ProductStorePO> productStorePOSNew = Lists.newArrayList();
        applyMap.entrySet().stream().forEach(entry -> {
            List<ProductStorePO> pos = map.get(entry.getKey());
            List<String> status = pos.stream().map(ProductStorePO::getStoreStatus).distinct().collect(Collectors.toList());
            if (status.size() > 1) {
                // 有多个状态,则不处理
                return;
            }
            if (StoreStatusEnum.WAREHOUSED.getCode().equals(status.get(0))) {
                throw new CommonException("入库单号[" + entry.getKey() + "]已完成的入库");
            }
            // 这个入库申请单下的所有的栈板都是已确认状态，则需要推送到erp
            if (StoreStatusEnum.CONFIRMED.getCode().equals(status.get(0))) {
                productStorePOSNew.addAll(pos);
            }
        });
        if (CollectionUtil.isEmpty(productStorePOSNew)) {
            return null;
        }
        return inboundToERP(confirmInboundInVO, productStorePOSNew);
    }

    private String inboundToERP(ConfirmInboundInVO confirmInboundInVO, List<ProductStorePO> productStorePOS) {
        String planTicketNo = productStorePOS.get(0).getPlanTicketNo();
        String productNo = productStorePOS.get(0).getProductNo();
        String unit = productStorePOS.get(0).getUnit();
        String productName = productStorePOS.get(0).getProductName();

        SfacTPO sfacTPO = sfacTService.getByProductNo(planTicketNo, productNo);
        String sfac006 = sfacTPO.getSfac006();

        IBoxCodeService boxCodeService =
                boxCodeHandlerFactory.getBoxCodeService(planTicketNo);
        // 封装wms条码信息 wms入库  主要是detail表
        List<BarcodeDetailDTO> barcodeDetailDTOS =
            getBarcodeDetailDTOS(boxCodeService, productStorePOS,
                    planTicketNo, sfac006,confirmInboundInVO.getWarehouse(),true);
        log.info("wms入库  主要是detail表:{}", JSON.toJSONString(barcodeDetailDTOS));
        srmBarcodeDetailService.saveBarcodeDetails(barcodeDetailDTOS);

        // 修改bug 把默认库位值为空   这里可以去拷贝一个新的出来，不重新生成，以便提交效率，懒得搞了
        // ======================================================================
        List<BarcodeDetailDTO> barcodeDetailDTOS2 =
                getBarcodeDetailDTOS(boxCodeService, productStorePOS,
                        planTicketNo,sfac006,confirmInboundInVO.getWarehouse(),false);
        log.info("wms入库  主要是detail表:{}", JSON.toJSONString(barcodeDetailDTOS));
        // 封装erp条码信息
        String uniqueValue = productStorePOS.stream()
                .map(productStorePO -> String.valueOf(productStorePO.getId())).sorted().collect(Collectors.joining(","));
        // 取前20个字节
        uniqueValue = uniqueValue.substring(0, Math.min(uniqueValue.length(), 20));

        String docNo = storageToErp(barcodeDetailDTOS2, planTicketNo, unit, sfac006, boxCodeService,uniqueValue);
        log.info("erp入库  封装erp条码信息:{}", JSON.toJSONString(barcodeDetailDTOS2));
        // ======================================================================


        // 自动过账入库到wms  主要是库存表
        List<UpdateBarcodeStoreDTO> updateBarcodeStoreDTOS =
                saveWmsStore(confirmInboundInVO, planTicketNo, unit, productName,  barcodeDetailDTOS, docNo);
        log.info("自动过账入库到wms  主要是库存表:{}", JSON.toJSONString(updateBarcodeStoreDTOS));
        srmBarcodeStoreService.saveBarcodeStores(updateBarcodeStoreDTOS);
        log.info("自动过账入库结束");

        List<ProductStoreRecordDTO> productStoreRecordDTOS = Lists.newArrayList();
        // 更新入库单状态
        for (ProductStorePO productStorePO : productStorePOS) {
            productStorePO.setStoreStatus(StoreStatusEnum.WAREHOUSED.getCode());
            productStorePO.setStoreNo(docNo);
            productStorePO.setUpdateBy(SecurityUtil.getUserId());

            // 操作记录
            ProductStoreRecordDTO productStoreRecordDTO = new ProductStoreRecordDTO();
            productStoreRecordDTO.setProductStoreId(productStorePO.getId());
            productStoreRecordDTO.setPalletCode(productStorePO.getPalletCode());
            productStoreRecordDTO.setDocNo(docNo);
            productStoreRecordDTO.setOperation(StoreOperationEnum.WAREHOUS.getCode());
            productStoreRecordDTO.setOperatorName(SecurityUtil.getUserName());
            productStoreRecordDTOS.add(productStoreRecordDTO);
        }
        updateBatchById(productStorePOS);

        // 操作记录
        productStoreRecordService.createRecords(productStoreRecordDTOS);

        EnumDTO enumDTO = new EnumDTO();
        enumDTO.setCode(confirmInboundInVO.getStorage());
        enumDTO.setName(confirmInboundInVO.getStorageName());
        setDefaultStorage(enumDTO);

        enumDTO.setCode(confirmInboundInVO.getWarehouse());
        enumDTO.setName(confirmInboundInVO.getWarehouseName());
        setDefaultWarehouse(enumDTO);

        return docNo;
    }

    private void setDefaultWarehouse(EnumDTO enumDTO) {
        String key = RedisKeyUtil.buildKey(RedisCacheConstant.DEFAULT_WAREHOUS + SecurityUtil.getUserId());
        RedisUtil.set(key, JSON.toJSONString(enumDTO), 72, TimeUnit.HOURS);
    }

    private List<UpdateBarcodeStoreDTO> saveWmsStore(ConfirmInboundInVO confirmInboundInVO, String planTicketNo,
                                                     String unit, String productName,
                                                     List<BarcodeDetailDTO> barcodeDetailDTOS, String docNo) {
        // 更新数据到库存表
        ProductTicketPO productTicketPO = new ProductTicketPO();
        productTicketPO.setCompanyCode(SecurityUtil.getCompanySite());
        productTicketPO.setPlanTicketNo(planTicketNo);
        productTicketPO.setProductName(productName);

        List<UpdateBarcodeStoreDTO> updateBarcodeStoreDTOS = Lists.newArrayList();
        barcodeDetailDTOS.stream().forEach(barcodeDetailDTO -> {
            List<BarcodeDetailDTO.BoxInfo> boxInfos = barcodeDetailDTO.getBoxInfos();
            String lotNo = barcodeDetailDTO.getLotNo();
            boxInfos.stream().forEach(boxInfo -> {
                SaveOutboundInfoInVO outboundInfo = new SaveOutboundInfoInVO();
                outboundInfo.setPalletCode(barcodeDetailDTO.getBarcode());
                outboundInfo.setCaseCode(boxInfo.getBoxCode());
                outboundInfo.setProducedQuantity(boxInfo.getBoxQuantity());
                outboundInfo.setUnit(unit);
                String productCode = boxInfo.getProductCode();
                String stockCode = boxInfo.getStockCode();
                UpdateBarcodeStoreDTO updateBarcodeStoreDTO =
                        productOutboundService.saveWmsMaterialInfo(outboundInfo, productTicketPO, docNo,
                                barcodeDetailDTO.getItemNo(), barcodeDetailDTO.getItemSpec(),
                                confirmInboundInVO.getStorage(),
                                confirmInboundInVO.getStorageName(),confirmInboundInVO.getWarehouse(),
                                confirmInboundInVO.getWarehouseName(),lotNo,productCode,stockCode);
                updateBarcodeStoreDTOS.add(updateBarcodeStoreDTO);
            });
        });
        return updateBarcodeStoreDTOS;
    }

    private String storageToErp(List<BarcodeDetailDTO> barcodeDetailDTOS, String planTicketNo,
                                String unit, String sfac006, IBoxCodeService boxCodeService,String uniqueValue) {
        Date curentDate = productOutboundService.updateReportDate();
        String timeStamp = DateUtil.parseDateToStringCustom(curentDate,"yyyyMMddHHmmssSSS");


        barcodeDetailDTOS.stream().forEach(barcodeDetailDTO -> {
            List<SaveLsafTDTO> saveLsafTDTOList = new ArrayList<>();
            for (BarcodeDetailDTO.BoxInfo boxInfo : barcodeDetailDTO.getBoxInfos()) {
                SaveLsafTDTO saveLsafTDTO = boxCodeService.setLsafTInfo(barcodeDetailDTO, boxInfo, curentDate,
                        sfac006, unit,barcodeDetailDTO.getItemNo());
                saveLsafTDTOList.add(saveLsafTDTO);
            }

            // 推送至ERP 入库保存
            log.info("---saveLsafTDTOList:{}", JSON.toJSONString(saveLsafTDTOList));
            iLsafTService.saveLsafT(saveLsafTDTOList);
        });
        StorageApplicationToErpInDTO inDTO = new StorageApplicationToErpInDTO();
        inDTO.setKey(UUID.randomUUID().toString().replaceAll("-", ""));
        inDTO.setTimeStamp(timeStamp);
        inDTO.setPlanTicketNo(planTicketNo);
        inDTO.setAutoPost("Y");
        inDTO.setSfea007(uniqueValue);
        log.info("---storageApplicationToErp:{}", JSON.toJSONString(inDTO));
        String docNo = null;
        try {
            ResponseCodeOutVO responseCodeOutVO = iInventoryToErpService.storageApplicationToErp(inDTO);

            // 保存记录
            Long userId = SecurityUtil.getUserId();
            taskExecutor.execute(() -> {
                ProductInterfaceRecordDTO productInterfaceRecordDTO = new ProductInterfaceRecordDTO();
                productInterfaceRecordDTO.setUserId(userId);
                productInterfaceRecordDTO.setPlanTicketNo(planTicketNo);
                productInterfaceRecordDTO.setResponse(responseCodeOutVO.getMessage());
                productInterfaceRecordDTO.setResponseCode(responseCodeOutVO.getCode());
                productOutboundService.saveProductInterfaceRecords(productInterfaceRecordDTO);
            });
            if (responseCodeOutVO.getCode().equals(BooleanEnum.FALSE.getCode())) {
                if (responseCodeOutVO.getMessage().contains("在T100已存在，不可重复，请检查")) {
                    // 去erp查询已经存在的单号
                    SfeaTPO sfeaTPO = sfeaTService.getBySfea007(uniqueValue);
                    if (sfeaTPO != null) {
                        responseCodeOutVO.setCode(BooleanEnum.TRUE.getCode());
                        responseCodeOutVO.setMessage(sfeaTPO.getSfeadocno());
                        return sfeaTPO.getSfeadocno();
                    }
                }
                throw new CommonException(responseCodeOutVO.getMessage());
            }
            docNo = responseCodeOutVO.getMessage();
        } catch (Exception e) {
            log.error("推送erp报错，lsaf表回滚，参数：" + timeStamp, e);
            iLsafTService.delectByTimestamp(planTicketNo, timeStamp);
            throw new CommonException("推送erp报错" + e.getMessage());
        }
        return docNo;
    }


    private List<BarcodeDetailDTO> getBarcodeDetailDTOS(IBoxCodeService boxCodeService,
        List<ProductStorePO> productStorePOS, String planTicketNo,String sfac006, String warehouse,Boolean isDefult) {
        log.info("---pushCaseInfoToWms:productStorePOS={}", JSON.toJSONString(productStorePOS));
        List<BarcodeDetailDTO> barcodeDetailDTOS = Lists.newArrayList();
        productStorePOS.stream().forEach(productStorePO -> {
            BarcodeDetailDTO barcodeDetailDTO = new BarcodeDetailDTO();
            barcodeDetailDTO.setPlanTicketNo(planTicketNo);
            barcodeDetailDTO.setBarcode(productStorePO.getPalletCode());
            barcodeDetailDTO.setPalletsQuantity(productStorePO.getProducedQuantity());
            barcodeDetailDTO.setItemNo(productStorePO.getProductNo());
            barcodeDetailDTO.setUnitNo(productStorePO.getUnit());
            barcodeDetailDTO.setWorkCode(SecurityUtil.getWorkcode());
            barcodeDetailDTO.setUserName(SecurityUtil.getUserName());
            barcodeDetailDTO.setCompanyCode(SecurityUtil.getCompanySite());
            barcodeDetailDTO.setIsFinalProduct(productStorePO.getIsFinalProduct());
            barcodeDetailDTO.setItemName(productStorePO.getProductName());
            barcodeDetailDTO.setItemSpec(productStorePO.getStandard());

            List<String> boxCodes = Arrays.stream(productStorePO.getCaseCode().split(",")).collect(Collectors.toList());

            // 封装wms条码信息
            List<BarcodeDetailDTO.BoxInfo> boxInfoList =
                getBoxInfos(boxCodeService, productStorePO.getProductNo(), productStorePO, boxCodes,sfac006);
            barcodeDetailDTO.setLotDate(boxInfoList.get(0).getB());
            barcodeDetailDTO.setLotNo(boxInfoList.get(0).getLotNo());
            barcodeDetailDTO.setBoxInfos(boxInfoList);

            // 保存仓库位置
            if (productStorePO.getIsFinalProduct().equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))) {
                barcodeDetailDTO.setWarehouseNo(StringUtil.isNotEmpty(warehouse) ? warehouse : "C001");
            } else {
                if (SecurityUtil.getCompanySite().equals("SITE-16")) {
                    barcodeDetailDTO.setWarehouseNo(StringUtil.isNotEmpty(warehouse) ? warehouse : "X002");
                } else {
                    barcodeDetailDTO.setWarehouseNo(StringUtil.isNotEmpty(warehouse) ? warehouse : "X001");
                }
            }
            if (isDefult) {
                if (SecurityUtil.getCompanySite().equals("SITE-16")
                        && productStorePO.getIsFinalProduct().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))
                        && barcodeDetailDTO.getWarehouseNo().equals("X002")
                ) {
                    barcodeDetailDTO.setStorageSpacesNo(StringUtil.isBlank(productStorePO.getStorage()) ? "03": productStorePO.getStorage());
                } else {
                    barcodeDetailDTO.setStorageSpacesNo(StringUtil.isBlank(productStorePO.getStorage()) ? "SC01": productStorePO.getStorage());
                }
            } else {
                if (SecurityUtil.getCompanySite().equals("SITE-16")
                        && productStorePO.getIsFinalProduct().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))
                        && barcodeDetailDTO.getWarehouseNo().equals("X002")
                ) {
                    barcodeDetailDTO.setStorageSpacesNo(StringUtil.isBlank(productStorePO.getStorage()) ? "03": productStorePO.getStorage());
                } else {
                    // 没有默认值
                    barcodeDetailDTO.setStorageSpacesNo(productStorePO.getStorage());
                }
            }
            barcodeDetailDTOS.add(barcodeDetailDTO);
        });
        log.info("---箱码信息推送至WMS:barcodeDetailDTO={}", JSON.toJSONString(barcodeDetailDTOS));
        return barcodeDetailDTOS;
    }

    private List<BarcodeDetailDTO.BoxInfo> getBoxInfos(IBoxCodeService boxCodeService, String productNo,
                                                       ProductStorePO productStorePO, List<String> boxCodes, String sfac006) {
        HandleBoxCodeDTO handleBoxCodeDTO = new HandleBoxCodeDTO();
        handleBoxCodeDTO.setBoxCodes(boxCodes);

        SaveOutboundInfoInVO saveOutboundInfoInVO = new SaveOutboundInfoInVO();
        saveOutboundInfoInVO.setPalletCode(productStorePO.getPalletCode());
        saveOutboundInfoInVO.setCaseCode(productStorePO.getCaseCode());
        saveOutboundInfoInVO.setProducedQuantity(productStorePO.getProducedQuantity());
        saveOutboundInfoInVO.setUnit(productStorePO.getUnit());
        saveOutboundInfoInVO.setIsFinalProduct(Integer.valueOf(BooleanEnum.TRUE.getCode()));

        // 箱规
        SaveOutboundInfoInVO.PalletCodeInfo palletCodeInfo = new SaveOutboundInfoInVO.PalletCodeInfo();
        palletCodeInfo.setPalletCode(productStorePO.getPalletCode());
        palletCodeInfo.setPalletCodeQuantity(productStorePO.getBoxSpace());
        List<SaveOutboundInfoInVO.PalletCodeInfo> palletCodeInfos =
             Arrays.asList(palletCodeInfo);
        saveOutboundInfoInVO.setPalletCodeInfos(palletCodeInfos);

        handleBoxCodeDTO.setInfo(saveOutboundInfoInVO);
        handleBoxCodeDTO.setProductionNo(productNo);

        List<BarcodeDetailDTO.BoxInfo> boxInfoList = com.google.common.collect.Lists.newArrayList();
        // 为了兼容旧数据，智物流的码不包含MES的认为是旧的数据，直接通过截取字符串的方式处理
        String barcodeNo = handleBoxCodeDTO.getBoxCodes().get(0);
        if (!barcodeNo.contains("MES")
            && boxCodeService.getCompanyCode().equals(CompanyBoxEnum.COMPANY_ZWL.getCode())) {
            boxInfoList.addAll(boxCodeService.oldBarcodeNO(handleBoxCodeDTO,sfac006));
        } else {
            boxInfoList.addAll(boxCodeService.handleBoxCode(handleBoxCodeDTO,sfac006));
        }
        return boxInfoList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String confirmPulled(ConfirmInboundInVO confirmInboundInVO) {
        if (CollectionUtil.isEmpty(confirmInboundInVO.getIds())) {
            throw new CommonException("入库单id不能为空");
        }
        List<ProductStorePO> productStorePOS = listByIds(confirmInboundInVO.getIds());

        Long sequence =
                OrderSequenceUtil.getAndIncrementOrderSequence(SequenceConst.PULLED_ORDER_KEY, SequenceConst.PULLED_LOCK_KEY);
        String storePulledNo = SequenceConst.PULLED_SEQUENCE_PREFIX
                + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_FORMAT)
                + String.format("%04d", sequence);

        List<ProductStoreRecordDTO> productStoreRecordDTOS = Lists.newArrayList();
        // 更新入库单状态
        for (ProductStorePO productStorePO : productStorePOS) {
            productStorePO.setStorePulledNo(storePulledNo);
            productStorePO.setStoreStatus(StoreStatusEnum.PULLED.getCode());
            productStorePO.setUpdateBy(SecurityUtil.getUserId());

            // 操作记录
            ProductStoreRecordDTO productStoreRecordDTO = new ProductStoreRecordDTO();
            productStoreRecordDTO.setProductStoreId(productStorePO.getId());
            productStoreRecordDTO.setPalletCode(productStorePO.getPalletCode());
            productStoreRecordDTO.setDocNo(storePulledNo);
            productStoreRecordDTO.setOperation(StoreOperationEnum.PULL.getCode());
            productStoreRecordDTO.setOperatorName(SecurityUtil.getUserName());
            productStoreRecordDTOS.add(productStoreRecordDTO);
        }
        updateBatchById(productStorePOS);

        // 操作记录
        productStoreRecordService.createRecords(productStoreRecordDTOS);

        return storePulledNo;
    }


    @Override
    public void saveDraftRequest(List<PalletStoreOutVO> palletStoreOutVOS) {
        String key = RedisKeyUtil.buildKey(RedisCacheConstant.STORE_REQUEST + SecurityUtil.getUserId());
        RedisUtil.set(key, JSON.toJSONString(palletStoreOutVOS), 72, TimeUnit.HOURS);
    }

    @Override
    public List<PalletStoreOutVO> getDraftRequest() {
        String key = RedisKeyUtil.buildKey(RedisCacheConstant.STORE_REQUEST + SecurityUtil.getUserId());
        return RedisUtil.get(key, List.class);
    }

    @Override
    public void saveDraftPull(List<PalletStoreOutVO> palletStoreOutVOS) {
        String key = RedisKeyUtil.buildKey(RedisCacheConstant.STORE_PULL + SecurityUtil.getUserId());
        RedisUtil.set(key, JSON.toJSONString(palletStoreOutVOS), 72, TimeUnit.HOURS);
    }

    @Override
    public List<PalletStoreOutVO> getDraftPull() {
        String key = RedisKeyUtil.buildKey(RedisCacheConstant.STORE_PULL + SecurityUtil.getUserId());
        return RedisUtil.get(key, List.class);
    }

    @Override
    public void saveDraftWarehous(List<PalletStoreOutVO> palletStoreOutVOS) {
        String key = RedisKeyUtil.buildKey(RedisCacheConstant.STORE_WAREHOUS + SecurityUtil.getUserId());
        RedisUtil.set(key, JSON.toJSONString(palletStoreOutVOS), 72, TimeUnit.HOURS);
    }

    @Override
    public List<PalletStoreOutVO> getDraftWarehous() {
        String key = RedisKeyUtil.buildKey(RedisCacheConstant.STORE_WAREHOUS + SecurityUtil.getUserId());
        return RedisUtil.get(key, List.class);
    }

    @Override
    public ProductStorePO getByPalletCode(String palletCode) {
        LambdaQueryWrapper<ProductStorePO> wrapper = Wrappers.lambdaQuery(ProductStorePO.class);
        wrapper.eq(ProductStorePO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ProductStorePO::getPalletCode, palletCode)
                .eq(ProductStorePO::getDeleted, Integer.valueOf(BooleanEnum.FALSE.getCode()));
        return getOne(wrapper);
    }

    @Override
    public Pagination<QueryPalletInfoOutVO> queryPalletInfo(QueryPalletInfoInVO queryPalletInfoInVO) {
        queryPalletInfoInVO.setCompanyCode(SecurityUtil.getCompanySite());

        if (StringUtils.isNotBlank(queryPalletInfoInVO.getBoxCode())) {
            List<ProductStoreBoxPO> byBoxCode = productStoreBoxService.getByBoxCode(queryPalletInfoInVO.getBoxCode());
            if (CollectionUtil.isNotEmpty(byBoxCode)) {
                Long productStoreId = byBoxCode.get(0).getProductStoreId();
                ProductStorePO productStorePO = getById(productStoreId);
                queryPalletInfoInVO.setPalletCode(productStorePO.getPalletCode());
            }
        }


        IPage<QueryPalletInfoOutVO> page = baseMapper.queryPalletInfo(queryPalletInfoInVO.getPage(), queryPalletInfoInVO);

        if (CollectionUtil.isEmpty(page.getRecords())) {
            return Pagination.newInstance(null);
        }

        // 收集所有需要查询的 storage
        List<String> storages = page.getRecords().stream()
                .map(QueryPalletInfoOutVO::getStorage)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询 InabTPO 对象
        Map<String, InabTPO> inabTPOMap = new HashMap<>();
        for (String storage : storages) {
            InabTPO inabTPO = inabService.getStoragesNameByCode(storage);
            if (inabTPO != null) {
                inabTPOMap.put(storage, inabTPO);
            }
        }

        page.getRecords().forEach(queryPalletInfoOutVO -> {
            String storage = queryPalletInfoOutVO.getStorage();
            if (storage != null) {
                InabTPO inabTPO = inabTPOMap.get(storage);
                if (inabTPO != null) {
                    String storageName = Optional.ofNullable(inabTPO.getInab003())
                            .orElseGet(() -> inabTPO.getInab002());
                    queryPalletInfoOutVO.setStorageName(storageName);
                }
            }
        });

        return Pagination.newInstance(page);
    }

    @Override
    public Pagination<QueryStorageInfoOutVO> queryStorageInfo(QueryPalletInfoInVO queryPalletInfoInVO) {
        queryPalletInfoInVO.setCompanyCode(SecurityUtil.getCompanySite());
        IPage<QueryStorageInfoOutVO> page = baseMapper.queryStorageInfo(queryPalletInfoInVO.getPage(), queryPalletInfoInVO);

        if (CollectionUtil.isEmpty(page.getRecords())) {
            return Pagination.newInstance(null);
        }

        List<Long> userIds = page.getRecords().stream()
                .map(productStorePO -> Long.valueOf(productStorePO.getApplyUserName())).collect(Collectors.toList());

        List<UserDTO> userDTOS = ia01Service.getUserInfoByIds(userIds);
        Map<Long, String> userMap = userDTOS.stream().collect(Collectors.toMap(UserDTO::getId, UserDTO::getName, (k1, k2) -> k1));
        page.getRecords().stream().forEach(productStorePO -> {
            productStorePO.setApplyUserName(userMap.get(Long.valueOf(productStorePO.getApplyUserName())));
        });

        return Pagination.newInstance(page);
    }

    @Override
    public String requestByPallet(ProductStoreDTO productStoreDTO) {
        Long sequence =
                OrderSequenceUtil.getAndIncrementOrderSequence(SequenceConst.STORE_ORDER_KEY, SequenceConst.STORE_LOCK_KEY);
        String storeApplyNo = SequenceConst.STORE_SEQUENCE_PREFIX
                + cn.hutool.core.date.DateUtil.format(new Date(), DatePattern.PURE_DATETIME_FORMAT)
                + String.format("%04d", sequence);

        List<ProductStorePO> productStorePOS = Lists.newArrayList();
        List<ProductStoreRecordDTO> productStoreRecordDTOS = Lists.newArrayList();


        List<InboundRequestByPalletInVO.PalletCodeInfo> palletCodeInfos = productStoreDTO.getPalletCodeInfos();

        List<String> palletCodes = palletCodeInfos.stream().map(InboundRequestByPalletInVO.PalletCodeInfo::getPalletCode).collect(Collectors.toList());
        List<ProductStoreBoxPO> productStoreBoxPOS = productStoreBoxService.getByBoxCodes(palletCodes);
        if (CollectionUtil.isNotEmpty(productStoreBoxPOS)) {
            throw new CommonException("该托盘码已存在:" + JSON.toJSONString(productStoreBoxPOS.stream().map(ProductStoreBoxPO::getBoxCode).collect(Collectors.toList())));
        }

        // 校验工单号
        verifyPlanTicketNo(productStoreDTO.getCaseCode(),productStoreDTO.getPlanTicketNo());

        for (InboundRequestByPalletInVO.PalletCodeInfo palletCodeInfo : palletCodeInfos) {
            ProductStorePO productStorePO = BeanUtil.copyProperties(productStoreDTO, ProductStorePO.class);
            productStorePO.setPalletCode(palletCodeInfo.getPalletCode());
            productStorePO.setCaseCode(palletCodeInfo.getPalletCode());
            productStorePO.setProducedQuantity(palletCodeInfo.getPalletCodeQuantity());
            productStorePO.setBoxSpace(palletCodeInfo.getPalletCodeQuantity());
            productStorePO.setStoreStatus(StoreStatusEnum.REQUESTED.getCode());
            productStorePO.setCompanyCode(SecurityUtil.getCompanySite());
            productStorePO.setCreateBy(SecurityUtil.getUserId());
            productStorePO.setUpdateBy(SecurityUtil.getUserId());
            productStorePO.setStoreApplyNo(storeApplyNo);

            // 产品名称+编号
            SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(productStoreDTO.getPlanTicketNo());

            SfacTPO sfacTPO = null;
            ImaalTVO imaalTVO = null;
            ImaalTVO imaalTByProductNo = null;
            String caseCode = palletCodeInfo.getPalletCode();
            if (StringUtil.isNotEmpty(caseCode)) {
                String[] split1 = caseCode.split("#");
                if (split1.length > 0) {
                    sfacTPO = sfacTService.getByProductNo(productStorePO.getPlanTicketNo(), split1[0]);
                    imaalTVO = iImaalTService.getImaalTByProductNo(split1[0]);
                    // 批次 和 规格
                    imaalTByProductNo = imaalTService.getImaalTByProductNo(split1[0]);
                }
            }
            if (Objects.isNull(sfacTPO)) {
                sfacTPO = sfacTService.getByProductNo(productStorePO.getPlanTicketNo(), sfaaTVO.getSfaa010());
            }
            if (Objects.isNull(imaalTVO)) {
                imaalTVO = iImaalTService.getImaalTByProductNo(sfaaTVO.getSfaa010());
            }
            if (Objects.isNull(imaalTByProductNo)) {
                // 批次 和 规格
                imaalTByProductNo = imaalTService.getImaalTByProductNo(sfaaTVO.getSfaa010());
            }

            productStorePO.setProductNo(sfacTPO.getSfac001());
            productStorePO.setProductName(imaalTVO.getImaal003());
            productStorePO.setStandard(imaalTByProductNo.getImaal004());



            // 麦当劳码
            if (palletCodeInfo.getPalletCode().contains("4DCD") || palletCodeInfo.getPalletCode().contains("MES#")) {
                ProductBoxBarcodeDetailDetailPO productBoxBarcodeDetailDetailPO = productBoxBarcodeDetailDetailService.getByBarcodeNo(palletCodeInfo.getPalletCode());
                productStorePO.setLotNo(productBoxBarcodeDetailDetailPO.getLotNo());
            } else {
                SrmBarcodeDetailPO srmBarcodeDetailPO = srmBarcodeDetailService.getSrmBarcodeDetailByBarcodeNo(palletCodeInfo.getPalletCode());
                if (srmBarcodeDetailPO != null) {
                    productStorePO.setLotNo(srmBarcodeDetailPO.getLotNo());
                } else {
                    String boxCode = palletCodeInfo.getPalletCode();
                    String[] boxCodeSpit = boxCode.split("#");
                    productStorePO.setLotNo(boxCodeSpit[1]);
                }
            }


            productStorePOS.add(productStorePO);
            save(productStorePO);

            // 操作记录
            ProductStoreRecordDTO productStoreRecordDTO = new ProductStoreRecordDTO();
            productStoreRecordDTO.setProductStoreId(productStorePO.getId());
            productStoreRecordDTO.setPalletCode(palletCodeInfo.getPalletCode());
            productStoreRecordDTO.setDocNo(storeApplyNo);
            productStoreRecordDTO.setOperation(StoreOperationEnum.REQUEST.getCode());
            productStoreRecordDTO.setOperatorName(SecurityUtil.getUserName());
            productStoreRecordDTOS.add(productStoreRecordDTO);
        }
        productStoreRecordService.createRecords(productStoreRecordDTOS);

        // 保存箱码信息
        List<ProductStoreBoxPO> productStoreBoxs = Lists.newArrayList();
        productStorePOS.stream().forEach(productStorePO -> {
                    ProductStoreBoxPO productStoreBoxPO = new ProductStoreBoxPO();
                    productStoreBoxPO.setProductStoreId(productStorePO.getId());
                    productStoreBoxPO.setBoxCode(productStorePO.getCaseCode());
                    productStoreBoxPO.setStoreType(productStorePO.getStoreType());
                    productStoreBoxPO.setCompanyCode(SecurityUtil.getCompanySite());
                    productStoreBoxPO.setProducedQuantity(productStorePO.getBoxSpace());
                    productStoreBoxPO.setUnit(productStorePO.getUnit());
                    productStoreBoxPO.setCreateBy(SecurityUtil.getUserId());
                    productStoreBoxPO.setUpdateBy(SecurityUtil.getUserId());
                    productStoreBoxPO.setCreateTime(new Date());
                    productStoreBoxPO.setUpdateTime(new Date());
                    productStoreBoxs.add(productStoreBoxPO);
                });
        productStoreBoxService.saveBatch(productStoreBoxs);

        return storeApplyNo;
    }

    private List<ProductStorePO> listByPalletCodes(List<String> palletCodes) {
        LambdaQueryWrapper<ProductStorePO> wrapper = Wrappers.lambdaQuery(ProductStorePO.class);
        wrapper.eq(ProductStorePO::getCompanyCode, SecurityUtil.getCompanySite())
                .in(ProductStorePO::getPalletCode, palletCodes);
        return list(wrapper);
    }

    @Override
    public EnumDTO getDefaultStorage() {
        String key = RedisKeyUtil.buildKey(RedisCacheConstant.DEFAULT_STORAGE + SecurityUtil.getUserId());
        return RedisUtil.get(key, EnumDTO.class);
    }

    @Override
    public void setDefaultStorage(EnumDTO enumDTO) {
        String key = RedisKeyUtil.buildKey(RedisCacheConstant.DEFAULT_STORAGE + SecurityUtil.getUserId());
        RedisUtil.set(key, JSON.toJSONString(enumDTO), 72, TimeUnit.HOURS);
    }

    @Override
    public Pagination<EnumDTO> getStorages(PageListCommonInVO pageListCommonInVO) {
        List<EnumDTO> enumDTOS = Lists.newArrayList();

        Pagination<InabTPO> mesUseList = inabService.getStoragesList(pageListCommonInVO);
        if (CollectionUtil.isNotEmpty(mesUseList.getData())) {
            enumDTOS.addAll(mesUseList.getData().stream().map(mesUse -> {
                EnumDTO dto = new EnumDTO();
                dto.setCode(mesUse.getInab002());
                dto.setName(mesUse.getInab003() == null ? mesUse.getInab002() : mesUse.getInab003());
                return dto;
            }).collect(Collectors.toList()));
        }
        return Pagination.newInstance(enumDTOS, mesUseList.getTotal(), mesUseList.getPages());
    }

    @RedisLock
    @GlobalTransactional(rollbackFor = Exception.class, timeoutMills = 300000)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String confirmInboundByPallet(String key,ConfirmInboundInVO confirmInboundInVO) {
        if (CollectionUtil.isEmpty(confirmInboundInVO.getStoreApplyNos())) {
            throw new CommonException("入库单编号不能为空");
        }
        // 查询已拉货的入库单
        LambdaQueryWrapper<ProductStorePO> wrapper = Wrappers.lambdaQuery(ProductStorePO.class);
        wrapper.eq(ProductStorePO::getCompanyCode, SecurityUtil.getCompanySite())
                .in(ProductStorePO::getStoreApplyNo, confirmInboundInVO.getStoreApplyNos())
                .ne(ProductStorePO::getStoreStatus, StoreStatusEnum.WAREHOUSED.getCode());
        List<ProductStorePO> productStorePOS = list(wrapper);
        return confirmInbound(confirmInboundInVO, productStorePOS);
    }

    @Override
    public List<ProductStorePO> getByApplyNo(List<String> applyNos) {
        if (CollectionUtil.isEmpty(applyNos)) {
            throw new CommonException("入库单编号不能为空");
        }
        // 查询已拉货的入库单
        LambdaQueryWrapper<ProductStorePO> wrapper = Wrappers.lambdaQuery(ProductStorePO.class);
        wrapper.eq(ProductStorePO::getCompanyCode, SecurityUtil.getCompanySite())
                .in(ProductStorePO::getStoreApplyNo, applyNos)
                .ne(ProductStorePO::getStoreStatus, StoreStatusEnum.WAREHOUSED.getCode());
        return list(wrapper);
    }

    @Override
    public Pagination<EnumDTO> getWarehouse(PageListCommonInVO pageListCommonInVO) {
        return inabService.getWarehouseList(pageListCommonInVO);
    }

    @Override
    public EnumDTO getDefaultWarehouse() {
        String key = RedisKeyUtil.buildKey(RedisCacheConstant.DEFAULT_WAREHOUS + SecurityUtil.getUserId());
        return RedisUtil.get(key, EnumDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteApplyNo(String storeApplyNo) {
        if (StringUtils.isBlank(storeApplyNo)) {
            throw new CommonException("入库单编号不能为空");
        }

        List<ProductStorePO> productStorePOS = getByApplyNo(Arrays.asList(storeApplyNo));
        if (CollectionUtil.isEmpty(productStorePOS)) {
            throw new CommonException("未入库的入库单号[" + storeApplyNo + "]不存在，请检查是否已经入库");
        }
        Map<String, List<ProductStorePO>> map = productStorePOS.stream()
                .collect(Collectors.groupingBy(ProductStorePO::getStoreApplyNo));

        List<ProductStorePO> pos = map.get(storeApplyNo);
        List<String> status = pos.stream().map(ProductStorePO::getStoreStatus).distinct().collect(Collectors.toList());

        if (status.size() == 1 && StoreStatusEnum.WAREHOUSED.getCode().equals(status.get(0))) {
            throw new CommonException("入库单号[" + storeApplyNo + "]已完成的入库,不可删除");
        }

        // 删除主表
        removeBatchByIds(pos);

        // 删除箱码明细表
        pos.stream().forEach(productStorePO -> {
            List<ProductStoreBoxPO> storeBoxPOS = productStoreBoxService.getByStoreId(productStorePO.getId());
            if (CollectionUtil.isNotEmpty(storeBoxPOS)) {
                productStoreBoxService.removeBatchByIds(storeBoxPOS);
            }
        });

        // 增加删除记录
        productStoreRecordService.deleteApplyNo(pos);


    }



    @Override
    public List<ProductStorePO> getByPlanTicketNO(String productTicketNo) {
        LambdaQueryWrapper<ProductStorePO> wrapper = Wrappers.lambdaQuery(ProductStorePO.class);
        wrapper.eq(ProductStorePO::getCompanyCode, SecurityUtil.getCompanySite())
                .in(ProductStorePO::getPlanTicketNo, productTicketNo);
        return list(wrapper);
    }

    @Override
    public void verifyPolletCodeFromWms(List<String> polletCodes) {
        CheckInboundRequest checkInboundRequest = new CheckInboundRequest();
        checkInboundRequest.setPalletCodeList(polletCodes);
        finishedInboundDetailService.checkInboundRequest(checkInboundRequest);
    }

    @Override
    public void verifyPalletCodeForWms(List<String> palletCodes) {
        LambdaQueryWrapper<ProductStorePO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductStorePO::getCompanyCode, SecurityUtil.getCompanySite())
                .in(ProductStorePO::getPalletCode, palletCodes);
        List<ProductStorePO> list = list(lambdaQueryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            throw new CommonException("栈板码已在入库申请表中存在[" + list.stream().map(ProductStorePO::getPalletCode)
                    .collect(Collectors.joining(",")) + "]");
        }
    }

    @Override
    public void verifyBoxtCodeForWms(List<String> boxCodes) {
        List<ProductStoreBoxPO> byBoxCodes = productStoreBoxService.getByBoxCodes(boxCodes);
        if (CollectionUtil.isNotEmpty(byBoxCodes)) {
            throw new CommonException("箱码已在入库申请表中存在[" +
                    byBoxCodes.stream().map(ProductStoreBoxPO::getBoxCode)
                            .collect(Collectors.joining(",")) + "]");
        }
    }


    private void verifyPlanTicketNo(String caseCode,String planTicketNo) {
        if (StringUtils.isNotBlank(caseCode)) {
            String[] split = caseCode.split(",");
            if (split.length > 0) {
                String boxcode = split[0];
                IBoxCodeService boxCodeService =
                        boxCodeHandlerFactory.getBoxCodeService(planTicketNo);
                VerifyCaseCodeInVO verifyCaseCodeInVO = new VerifyCaseCodeInVO();
                verifyCaseCodeInVO.setPlanTicketNo(planTicketNo);
                verifyCaseCodeInVO.setBoxCode(boxcode);
                boxCodeService.verifyBoxCode(verifyCaseCodeInVO);
            }
        }
    }






















    // =======================================================================



    @DubboReference
    private LxlTestService lxlTestService;
    @DubboReference
    private ISfecTService sfecTService;
    @Override
    public String getBarcodeLotNo(String barcode) {

         StringBuilder sb = new StringBuilder();

         String sql1 = "select t1.lot_no as lotNo1,t2.lot_no as lotNo2," +
         "t1.barcode_no as barcodeNo from srm_barcode_store t1\n" +
         "inner join srm_barcode_detail t2 on t1.barcode_no = t2.barcode_no \n" +
         "where t1.barcode_no in (\n" +
         "select code from srm_container_tree t\n" +
         "inner join \n" +
         "(select parent_id from srm_container_tree sct where sct.code = " +barcode+ ") t1 \n" +
         "where t1.parent_id = t.parent_id \n" +
         ")";

         List<Map<String, Object>> multipleRows = lxlTestService.getMultipleRows(sql1);
         Map<String, Object> map = multipleRows.get(0);
         if (map != null) {
         sb.append("srm_barcode_store 和 srm_barcode_detail 批次号为：");
         sb.append(" ");
         sb.append(map.get("barcodeNo").toString());
         sb.append(" ");
         sb.append(map.get("lotNo1").toString());
         sb.append(" ");
         sb.append(map.get("lotNo2").toString());
         sb.append(" ");
         }

         String barcodeNo = map.get("barcodeNo").toString();
         String sql2 = "select target_no from srm_barcode_change sbc where barcode_no = '" + barcodeNo + "'";
         List<Map<String, Object>> multipleRows1 = lxlTestService.getMultipleRows(sql2);

         if (CollectionUtil.isNotEmpty(multipleRows1)) {
         Map<String, Object> map1 = multipleRows1.get(0);
         sb.append("srm_store_change 入库单号为：");
         sb.append(" ");
         sb.append(map1.get("target_no").toString());
         sb.append(" ");
         String targetNo = map1.get("target_no").toString();
         SfecTPO byDocNo = sfecTService.getByDocNo(targetNo);
         sb.append("erp的批次号为：" + byDocNo.getSfec014());
         }

         return sb.toString();


        // ==============================================================
//        String targetNo = barcode;
//        StringBuilder sb = new StringBuilder();
//        String sql = "select barcode_no from srm_barcode_change sbc WHERE target_no = '" + targetNo + "'";
//        List<Map<String, Object>> multipleRows = lxlTestService.getMultipleRows(sql);
//        if (CollectionUtil.isEmpty(multipleRows)) {
//            System.out.println("+++++++++++++++++++++++++++++++++++++");
//            System.out.println(targetNo + "没异动记录");
//            System.out.println("+++++++++++++++++++++++++++++++++++++");
//            return targetNo + "没异动记录";
//        }
//        Map<String, Object> map = multipleRows.get(0);
//        String barcode_no = map.get("barcode_no").toString();
//
//        String sql1 = "select t1.lot_no as lotNo1,t2.lot_no as lotNo2,"
//            + "t1.barcode_no as barcodeNo  from srm_barcode_store t1\n"
//            + "inner join srm_barcode_detail t2 on t1.barcode_no  = t2.barcode_no \n" + "where t1.barcode_no = ('"
//            + barcode_no + "')";
//
//        List<Map<String, Object>> multipleRows1 = lxlTestService.getMultipleRows(sql1);
//        Map<String, Object> map1 = multipleRows1.get(0);
//        if (map1 != null) {
//            sb.append("srm_barcode_store 和 srm_barcode_detail 批次号为：");
//            sb.append("  ");
//            sb.append(map1.get("barcodeNo").toString());
//            sb.append("  ");
//            sb.append(map1.get("lotNo1").toString());
//            sb.append("  ");
//            sb.append(map1.get("lotNo2").toString());
//            sb.append("  ");
//
//        }
//
//        SfecTPO byDocNo = sfecTService.getByDocNo(targetNo);
//        sb.append("erp的批次号为：" + byDocNo.getSfec014());
//        if (!byDocNo.getSfec014().equals(map1.get("lotNo2").toString())) {
//            sb.append("  ");
//            sb.append(map1.get("barcodeNo").toString()+ "," + byDocNo.getSfec014());
//        }
//        System.out.println("+++++++++++++++++++++++++++++++++++++");
//        System.out.println(sb);
//        System.out.println("+++++++++++++++++++++++++++++++++++++");
//        return sb.toString();
    }

    @DubboReference
    private ISrmContainerTreeService srmContainerTreeService;

    @Transactional
    @Override
    public String updateBarcodeLotNo(String barcode, String lotNo) {
        String sql1 = "select t1.id as id,t1.barcode_no as barcodeNo  from srm_barcode_store t1\n" +
                "where t1.barcode_no in (\n" +
                "select code from srm_container_tree t\n" +
                "inner join \n" +
                "(select parent_id from srm_container_tree sct where sct.code = '"+barcode+"') t1 \n" +
                "where t1.parent_id = t.parent_id \n" +
                ")";

        List<Map<String, Object>> multipleRows = lxlTestService.getMultipleRows(sql1);
        if (multipleRows.size() > 500) {
            throw new CommonException("条码[" + barcode + "]对应的箱码数量大于500，不操作");
        }

        ObjectMapper objectMapper = new ObjectMapper();
        for (Map<String, Object> map : multipleRows) {
            SrmBarcodeStorePO srmBarcodeStorePO = objectMapper.convertValue(map, SrmBarcodeStorePO.class);
            SrmBarcodeStorePO srmBarcodeStorePO1 = new SrmBarcodeStorePO();
            srmBarcodeStorePO1.setId(srmBarcodeStorePO.getId());
            srmBarcodeStorePO1.setLotNo(lotNo);
            srmBarcodeStoreService.updateById(srmBarcodeStorePO1);
            log.info("更新箱码[" + srmBarcodeStorePO.getBarcodeNo() + "]的批次号为：" + lotNo);
        }


        String sql2 = "select t1.id as id,t1.barcode_no as barcodeNo  from srm_barcode_detail t1\n" +
                "where t1.barcode_no in (\n" +
                "select code from srm_container_tree t\n" +
                "inner join \n" +
                "(select parent_id from srm_container_tree sct where sct.code = '"+barcode+"') t1 \n" +
                "where t1.parent_id = t.parent_id \n" +
                ")";

        List<Map<String, Object>> multipleRows2 = lxlTestService.getMultipleRows(sql2);
        if (multipleRows.size() > 500) {
            throw new CommonException("条码[" + barcode + "]对应的箱码数量大于500，不操作");
        }

        ObjectMapper objectMapper2 = new ObjectMapper();
        for (Map<String, Object> map : multipleRows2) {
            SrmBarcodeDetailPO srmBarcodeDetailPO = objectMapper2.convertValue(map, SrmBarcodeDetailPO.class);
            SrmBarcodeDetailPO srmBarcodeDetailPO1 = new SrmBarcodeDetailPO();
            srmBarcodeDetailPO1.setId(srmBarcodeDetailPO.getId());
            srmBarcodeDetailPO1.setLotNo(lotNo);
            srmBarcodeDetailService.updateById(srmBarcodeDetailPO1);
            log.info("更新箱码[" + srmBarcodeDetailPO.getBarcodeNo() + "]的批次号为：" + lotNo);
        }

        return "success";
    }


}
