package cn.jihong.mes.production.app.service;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.production.api.model.dto.ReportSalaryInfoDTO;
import cn.jihong.mes.production.api.service.IProductReportSalaryService;
import cn.jihong.oa.erp.api.model.vo.in.ReportSalaryInfoInVO;
import cn.jihong.oa.erp.api.model.vo.out.ReportSalaryInfoOutVO;
import cn.jihong.oa.erp.api.service.ISffbTService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class ProductReportSalaryServiceImpl implements IProductReportSalaryService {

    @DubboReference
    private ISffbTService sffbTService;

    @Override
    public ReportSalaryInfoDTO getReportSalary() {
        ReportSalaryInfoInVO reportSalaryInfoInVO = new ReportSalaryInfoInVO();
        reportSalaryInfoInVO.setWorkCode(SecurityUtil.getWorkcode());
        Date date = new Date();
        DateTime begin = DateUtil.beginOfMonth(date);
        // 当月第一天
        reportSalaryInfoInVO.setReportStartDate(DateUtil.format(begin, DatePattern.NORM_DATE_PATTERN));
        reportSalaryInfoInVO.setReportEndDate(DateUtil.format(date, DatePattern.NORM_DATE_PATTERN));
        List<ReportSalaryInfoOutVO> reportSalaryInfo = sffbTService.getReportSalaryInfo(reportSalaryInfoInVO);


        return null;
    }
}
