package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.text.StrBuilder;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.jihong.calibre.review.server.api.model.enums.DefectDetermineStatusEnum;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.vo.ResponseCodeOutVO;
import cn.jihong.common.util.AssertUtil;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.api.model.po.ProductionMachineLogisticsConfigPO;
import cn.jihong.mes.api.model.po.ProductionPlanPO;
import cn.jihong.mes.api.model.vo.*;
import cn.jihong.mes.api.service.*;
import cn.jihong.mes.production.api.model.constant.LogisticsConst;
import cn.jihong.mes.production.api.model.dto.*;
import cn.jihong.mes.production.api.model.enums.*;
import cn.jihong.mes.production.api.model.po.*;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.in.logistics.CallMaterialInVO;
import cn.jihong.mes.production.api.model.vo.in.logistics.StartCallMaterialInVO;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.service.*;
import cn.jihong.mes.production.api.service.logistics.ILogisticsService;
import cn.jihong.mes.production.app.aspect.RedisLock;
import cn.jihong.mes.production.app.config.JinshanLogisticsConfig;
import cn.jihong.mes.production.app.config.PushSwitchConfig;
import cn.jihong.mes.production.app.mapper.ProductTicketMapper;
import cn.jihong.mes.production.app.service.boxCode.BoxCodeHandlerFactory;
import cn.jihong.mes.production.api.service.IBoxCodeService;
import cn.jihong.mes.production.app.util.CollectorsUtil;
import cn.jihong.message.api.service.IEnterpriseWeChatService;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.ehr.api.model.dto.UserDTO;
import cn.jihong.oa.ehr.api.service.IA01Service;
import cn.jihong.oa.erp.api.model.dto.*;
import cn.jihong.oa.erp.api.model.dto.webservice.ReportDefectiveRequestInDTO;
import cn.jihong.oa.erp.api.model.enums.ErpOrderTypeEnum;
import cn.jihong.oa.erp.api.model.po.EcaaucTPO;
import cn.jihong.oa.erp.api.model.po.EcbnucTPO;
import cn.jihong.oa.erp.api.model.po.OoabTPO;
import cn.jihong.oa.erp.api.model.vo.*;
import cn.jihong.oa.erp.api.model.vo.portrait.CorporationGroupOutVO;
import cn.jihong.oa.erp.api.service.*;
import cn.jihong.oa.erp.api.service.webservice.IStaffingCostsToErpService;
import cn.jihong.tms.api.model.dto.TicketDTO;
import cn.jihong.tms.api.model.dto.request.CreateAndAssignTicketRequest;
import cn.jihong.tms.api.model.enums.TicketStatusEnum;
import cn.jihong.tms.api.model.enums.TicketTypeEnum;
import cn.jihong.tms.api.service.ITicketService;
import cn.jihong.wms.api.model.dto.RevokeBarcodeStoreDTO;
import cn.jihong.wms.api.model.dto.SrmBarcodeStoreDTO;
import cn.jihong.wms.api.model.po.SrmBarcodeStorePO;
import cn.jihong.wms.api.service.ISrmBarcodeStoreService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import io.seata.core.context.RootContext;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 生产工单信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Slf4j
@DubboService
public class ProductTicketServiceImpl extends JiHongServiceImpl<ProductTicketMapper, ProductTicketPO>
    implements IProductTicketService {

    private static final String BIZNAME = "SCGD";
    private static final String TICKET_NAME = "生产工单";
    private static final String ORDER_KEY = "order:sequence";
    private static final String LOCK_KEY = "order:lock";


    private static final String ALL_BILL_DELETE = "AllBillDelete";

    private static Pattern chinesePattern = Pattern.compile("[\\u4e00-\\u9fa5]");

    @Resource
    private PushSwitchConfig pushSwitchConfig;

    @DubboReference(check = false)
    private ITicketService iTicketService;
    @DubboReference
    private IProductionPlanService productionPlanService;
    @DubboReference
    private IA01Service ia01Service;
    @Resource
    private IProductShiftService productShiftService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private IProductionMachineService productionMachineService;
    @Resource
    private IProductMachineTicketService productMachineTicketService;
    @Resource
    private IProductMachineTicketRelationService productMachineTicketRelationService;
    @Resource
    private IProductTicketService productTicketService;
    @Resource
    private IProductFinishOrderService productFinishOrderService;
    @Resource
    private IProductDefectiveProductsCallbackService productDefectiveProductsCallbackService;
    @Resource
    private IProductDefectiveProductsService productDefectiveProductsService;
    @Resource
    private IProductOutboundService productOutboundService;
    @Resource
    private IProductConfigService productConfigService;
    @DubboReference
    private IProductionMachineConfigService productionMachineConfigService;
    @Resource
    private IProductMachineDayService productMachineDayService;
    @Resource
    private IProductMachineTaskService productMachineTaskService;
    @Resource
    private IProductMachinePartsService productMachinePartsService;
    @Resource
    private IProductMachineMaterialApportionmentService productMachineMaterialApportionmentService;
    @Resource
    private IProductMachineMaterialRecordService productMachineMaterialRecordService;
    @Resource
    private IProductMaterialService productMaterialService;
    @DubboReference(timeout = 30000,retries = 0)
    private IWorkReportInformationPushToErpService workReportInformationPushToErpService;
    @DubboReference
    private ISfaaTService sfaaTService;
    @DubboReference
    private IImaalTService iImaalTService;
    @DubboReference
    private IProductionShiftSetService productionShiftSetService;
    @DubboReference
    private IProductionShiftDetailService productionShiftDetailService;
    @DubboReference
    private IEnterpriseWeChatService iEnterpriseWeChatService;
    @DubboReference
    private IEcbnucTService ecbnucTService;
    @DubboReference
    private IMaterialsInfoService materialsInfoService;

    @Resource
    private IProductInterfaceRecordsService productInterfaceRecordsService;
    @Resource
    private BoxCodeHandlerFactory boxCodeHandlerFactory;
    @Resource
    private JinshanLogisticsConfig jinshanLogisticsConfig;
    @Resource
    private IProductStoreService productStoreService;

    @DubboReference
    private ISfbaTService sfbaTService;
    @DubboReference
    private ISfcbTService sfcbTService;
    @DubboReference
    private IOoabTService ooabTService;
    @DubboReference
    private IReportUnitConvertService reportUnitConvertService;
    @DubboReference
    private IOocqlTService iOocqlTService;
    @DubboReference
    private IXmamTService xmamTService;
    @DubboReference
    private IPmaaTService pmaaTService;
    @DubboReference
    private IEcaaucTService ecaaucTService;
    @DubboReference(timeout = 300000,retries = 0)
    private ISrmBarcodeStoreService srmBarcodeStoreService;
    @DubboReference
    private IInagTService iInagTService;
    @DubboReference
    private IProductionMachineLogisticsConfigService productionMachineLogisticsConfigService;
    @DubboReference
    private IStaffingCostsToErpService staffingCostsToErpService;

    @Resource
    private ILogisticsService iLogisticsService;

    @Override
    public List<ProductionPlanDTO> getProductPlanList(GetProductPlanListVO vo) {
        // List<ProductionPlanPO> productionPlanPOS = productionPlanService
        // .getListByNameAndDateAndSerialNo(vo.getDeviceName(), vo.getProductionPlanDate(), vo.getSerialNo());
//        // 查询机台和工程单的关系
//        ProductMachineTicketPO productMachineTicketPO =
//            productMachineTicketService.getByMachineName(vo.getDeviceName());
        List<ProductionPlanPO> productionPlanPOS = productionPlanService.getListByMachineTicket(vo.getDeviceName(),
            vo.getProductionPlanDate(), vo.getSerialNo(),vo.getWorkerOrderNo());

        // 过滤 plannedProductionCapacity 为0 的数据
        if (CollectionUtil.isNotEmpty(productionPlanPOS)) {
            productionPlanPOS = productionPlanPOS.stream()
                    .filter(po -> {
                        Number capacity = po.getPlannedProductionCapacity();
                        return capacity != null && capacity.doubleValue() != 0;
                    })
                    .collect(Collectors.toList());
        }

        if (CollectionUtil.isEmpty(productionPlanPOS)) {
            if (SecurityUtil.getCompanySite().equals(SiteEnum.AHGC.getCode())
                    || SecurityUtil.getCompanySite().equals(SiteEnum.HSXC.getCode())
                    ) {
                if (StringUtils.isNotBlank(vo.getWorkerOrderNo())) {
                    log.error(String.format("机台正在生产%s工程单，当前选择的日期和班次中无对应生产计划", vo.getWorkerOrderNo()));
                } else {
                    log.error("当前选择的日期和班次中无对应生产计划，进行提前生产操作");
                }
                List<ProductionPlanDTO> productionPlanDTOList = createProductionPlanDTO(vo);
                if (CollectionUtil.isEmpty(productionPlanDTOList)) {
                    return null;
                }
                return productionPlanDTOList;
            }
            throw new CommonException("当前机台" + vo.getDeviceName() + "\n日期"
                + DateUtil.format(vo.getProductionPlanDate(), DatePattern.NORM_DATE_PATTERN) + "\n班次" + vo.getSerialNo()
                + "\n未排产此工单号" + (StringUtil.isBlank(vo.getWorkerOrderNo()) ? "" : vo.getWorkerOrderNo())
                + "\n请联系生产计划排产并导入后台");

        }

        // 机台名称给erp机台名称
        List<ProductionPlanDTO> productionPlanDTOS = BeanUtil.copyToList(productionPlanPOS.stream().map(productionPlanPO -> {
            productionPlanPO.setProductionMachine(productionPlanPO.getErpMachineName());
            return productionPlanPO;
        }).collect(Collectors.toList()), ProductionPlanDTO.class);

        productionPlanDTOS.stream().forEach(productionPlanDTO -> {
            ProductionMachineOutVO productionMachineOutVO = productionMachineService.getProductionMachineByErpName(vo.getDeviceName(), productionPlanDTO.getProductionProcess());
            if (productionMachineOutVO != null) {
                productionPlanDTO.setProductionProcessCode(productionMachineOutVO.getProcessCode());
            }
            productionPlanDTO.setPlannedProductionCapacityText(productionPlanDTO.getPlannedProductionCapacity().toString());
        });

        return productionPlanDTOS;
    }

    private List<ProductionPlanDTO> createProductionPlanDTO(GetProductPlanListVO getProductPlanListVO){
        if (StringUtils.isBlank(getProductPlanListVO.getWorkerOrderNo())) {
            return null;
        }

        List<ProductionPlanPO> productionPlanPOS =
                productionPlanService.getListByNameAndTicketNo(getProductPlanListVO.getDeviceName(),
                        getProductPlanListVO.getWorkerOrderNo());
        List<ProductionPlanDTO> planDTOList;
        if(CollectionUtil.isEmpty(productionPlanPOS)) {
            // 无历史生产计划信息，使用erp中的 工序和工序类型
        //    throw new CommonException("该机台无历史生产计划信息，无法确认其工序和工序类型，不可直接创建工单");
            List<GetProcessListByTickNoOutVO> erpTicketProcessInfoOutVOList = sfcbTService.getProcessListByTickNo(getProductPlanListVO.getWorkerOrderNo(),getProductPlanListVO.getDeviceName());
            planDTOList = erpTicketProcessInfoOutVOList.stream().map(t->{
                ProductionPlanDTO productionPlanDTO = new ProductionPlanDTO();
                productionPlanDTO.setProductionName(t.getProductName());
                productionPlanDTO.setProductionProcess(t.getProcess());
                productionPlanDTO.setProductionProcessCode(t.getProcessCode());
                productionPlanDTO.setProductionProcessType(t.getProcessType());
                productionPlanDTO.setWorkerOrderNo(t.getProductTickNo());
                productionPlanDTO.setPlannedProductionCapacityText("未排产");
                return productionPlanDTO;
            }).collect(Collectors.toList());

            // 厦门工厂 分生产一部还有生产二部 , 根据机台查询历史计划所属部门
            if("SITE-22".equals(SecurityUtil.getCompanySite())){
                ProductionPlanPO oneByName = productionPlanService.getOneByName(getProductPlanListVO.getDeviceName());
                if(oneByName!=null && StringUtils.isNotBlank(oneByName.getDeptName())){
                    planDTOList.forEach(t->{
                        t.setDeptNo(oneByName.getDeptNo());
                        t.setDeptName(oneByName.getDeptName());
                    });
                }
            }

        }else {
            ProductionPlanPO productionPlanPO = productionPlanPOS.get(0);
            ProductionPlanDTO productionPlanDTO = new ProductionPlanDTO();
            productionPlanDTO.setProductionProcess(productionPlanPO.getProductionProcess());
            productionPlanDTO.setProductionProcessType(productionPlanPO.getProductionProcessType());
            productionPlanDTO.setDeptNo(productionPlanPO.getDeptNo());
            productionPlanDTO.setDeptName(productionPlanPO.getDeptName());
            productionPlanDTO.setPlannedProductionCapacityText("0");

            ProductionMachineOutVO productionMachineOutVO = productionMachineService.getProductionMachineByErpName(getProductPlanListVO.getDeviceName(), productionPlanDTO.getProductionProcess());
            AssertUtil.isNotNull(productionMachineOutVO,"查询不到该机台的工序");
            productionPlanDTO.setProductionProcessCode(productionMachineOutVO.getProcessCode());
            planDTOList = List.of(productionPlanDTO);
        }
        // 产品编号
        String productNo = sfaaTService.getSfaaTByTicket(getProductPlanListVO.getWorkerOrderNo());
        ImaalTVO imaal = iImaalTService.getImaalTByProductNo(productNo);

        planDTOList.forEach(t->{
            t.setProductionMachine(getProductPlanListVO.getDeviceName());
            t.setProductionPlanDate(getProductPlanListVO.getProductionPlanDate());
            t.setSerialNo(getProductPlanListVO.getSerialNo());
            t.setWorkerOrderNo(getProductPlanListVO.getWorkerOrderNo());
            t.setProductionName(imaal.getImaal003());
            t.setPlannedProductionCapacity(BigDecimal.ZERO);
            // 设置计划开始结束时间
            setPlanDateTime(t);
        });
        return planDTOList;
    }

    /**
     * 设置计划开始结束时间
     * @param productionPlanDTO
     */
    private void setPlanDateTime(ProductionPlanDTO productionPlanDTO) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String currentDate = sdf.format(new Date());
        ProductionShiftSetVO shiftSetVO = productionShiftSetService.getOneByDate(currentDate.split(" ")[0]);
        if (Objects.isNull(shiftSetVO)) {
            throw new CommonException("生成系统尚未设置当前时间段排班");
        }
        LocalTime nowTime = LocalTime.parse(currentDate.split(" ")[1]);
        List<ProductionShiftDetailVO> detailList =
            productionShiftDetailService.getDetailList(shiftSetVO.getId().toString());
        ProductionShiftDetailVO detailVO = detailList.stream()
            .filter(t -> t.getSerialNo().equals(productionPlanDTO.getSerialNo()))
            .findFirst().orElse(null);
        if (Objects.isNull(detailVO)) {
            throw new CommonException("当前时间点不在生产系统的早晚班时间内");
        }
        List<TimeVO> timeSlot = detailVO.getTimeSlot();
        String startTime = timeSlot.get(0).getStartTime();
        String endTime = timeSlot.get(timeSlot.size() - 1).getEndTime();
        // 晚班结束时间日期+1天
        Integer serialNo = productionPlanDTO.getSerialNo();
        String date = DateUtil.format(productionPlanDTO.getProductionPlanDate(), "yyyy/MM/dd");
        if (serialNo != null && "2".equals(serialNo.toString())) {
            productionPlanDTO.setProductionPlanStartTime(
                cn.jihong.common.util.DateUtil.parseStringToDateCustom(date + " " + startTime, "yyyy/MM/dd HH:mm"));
            productionPlanDTO.setProductionPlanEndTime(cn.jihong.common.util.DateUtil.addDays(
                cn.jihong.common.util.DateUtil.parseStringToDateCustom(date + " " + endTime, "yyyy/MM/dd HH:mm"), 1));
        } else {
            productionPlanDTO.setProductionPlanStartTime(
                cn.jihong.common.util.DateUtil.parseStringToDateCustom(date + " " + startTime, "yyyy/MM/dd HH:mm"));
            productionPlanDTO.setProductionPlanEndTime(
                cn.jihong.common.util.DateUtil.parseStringToDateCustom(date + " " + endTime, "yyyy/MM/dd HH:mm"));
        }
    }


    private ProductTicketPO getLastPlanTicketNo(String workerOrderNo, String deviceName) {
        LambdaQueryWrapper<ProductTicketPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductTicketPO::getPlanTicketNo,workerOrderNo)
                .eq(ProductTicketPO::getMachineName,deviceName)
                .orderByDesc(ProductTicketPO::getProduceDate);
        // 这里我们用分页的方式取第一页，只有一条数据，也就是最新的一条数据
        Page<ProductTicketPO> page = page(new Page<>(1, 1), lambdaQueryWrapper);

        if (page.getRecords().size() > 0) {
            return page.getRecords().get(0);
        } else {
            return null; 
        }
    }


    /**
     * 获得生产工单信息
     */
    @Override
    public Pagination<ProductionTicketInfoOutVO>
        getProductTicketList(GetProductTicketListInVO getProductTicketListInVO) {
        if (StringUtils.isBlank(getProductTicketListInVO.getCompanyCode())) {
            getProductTicketListInVO.setCompanyCode(SecurityUtil.getCompanySite());
        }

        Page<ProductionTicketInfoOutVO> page = baseMapper.selectProductTicketPage(getProductTicketListInVO.getPage(), getProductTicketListInVO);

        List<Long> userIds =
            page.getRecords().stream().map(ProductionTicketInfoOutVO::getCreateBy).distinct().collect(Collectors.toList());
        Map<Long, String> userMap =
            ia01Service.getUserInfoByIds(userIds).stream().collect(Collectors.toMap(UserDTO::getId, UserDTO::getName));

        List<Long> ids =
                page.getRecords().stream().map(ProductionTicketInfoOutVO::getId).distinct().collect(Collectors.toList());
        List<ProductInterfaceRecordsPO> productInterfaceRecordsPOS = productInterfaceRecordsService.getByProductTicketIds(ids);
        Map<Long, List<ProductInterfaceRecordsPO>> productTicketIdMap =
                productInterfaceRecordsPOS.stream().collect(Collectors.groupingBy(ProductInterfaceRecordsPO::getProductTicketId));

        return Pagination.newInstance(page.getRecords().stream().map(ptpv -> {
            ProductionTicketInfoOutVO productionTicketInfoOutVO =
                BeanUtil.copyProperties(ptpv, ProductionTicketInfoOutVO.class);
            // 获得结单类型
            MachineConfigInVO machineConfigInVO = MachineConfigInVO.builder()
                    .machineName(ptpv.getMachineName())
                    .build();
            Integer billingType = productionMachineConfigService.getBillingType(machineConfigInVO);
            productionTicketInfoOutVO.setBillingType(billingType);
            // 有一个未完成的，则认为这个工单还没完成
            Boolean isFinish = false;
            // 一日一节需要增加分摊判断
            if (billingType.equals(ProductConfigBillingTypeEnum.ONE_DAY.getIntCode())) {
                isFinish = productionTicketInfoOutVO.getIsPourMaterial().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))
                        || productionTicketInfoOutVO.getIsSignUp().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))
                        || productionTicketInfoOutVO.getIsDefectionSignUp().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))
                        || productionTicketInfoOutVO.getIsDeductMaterial().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()));
            } else {
                isFinish = productionTicketInfoOutVO.getIsPourMaterial().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))
                        || productionTicketInfoOutVO.getIsSignUp().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))
                        || productionTicketInfoOutVO.getIsDefectionSignUp().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()));
                productionTicketInfoOutVO.setIsDeductMaterial(null);
            }
            productionTicketInfoOutVO.setShow(isFinish ? Integer.valueOf(BooleanEnum.TRUE.getCode()) : Integer.valueOf(BooleanEnum.FALSE.getCode()));

            productionTicketInfoOutVO.setCreaterName(userMap.get(ptpv.getCreateBy()));
            productionTicketInfoOutVO
                .setStatusName(TicketStatusEnum.getTicketStatusEnum(ptpv.getStatus()).getCnName());

            // 关联到调用erp的记录信息
            List<ProductInterfaceRecordsPO> pos = productTicketIdMap.get(ptpv.getId());
            if (CollectionUtil.isNotEmpty(pos)) {
                Map<Integer, String> map = pos.stream().sorted(Comparator.comparing(ProductInterfaceRecordsPO::getCreateTime).reversed())
                        .collect(Collectors.toMap(ProductInterfaceRecordsPO::getBusinessType,
                                ProductInterfaceRecordsPO::getResponse, (v1, v2) -> v1));

                String goodReportDoNo = map.get(InterfaceBusinessEnum.GOOD_REPORT.getCode());
                String defectionReportDoNo = map.get(InterfaceBusinessEnum.DEFECTION_REPORT.getCode());
                String updateMaterialDoNo = map.get(InterfaceBusinessEnum.UPDATE_MATERIAL.getCode());

                productionTicketInfoOutVO.setGoodReportDoNo(goodReportDoNo);
                productionTicketInfoOutVO.setDefectionReportDoNo(defectionReportDoNo);
                productionTicketInfoOutVO.setUpdateMaterialDoNo(updateMaterialDoNo);
            }


            return productionTicketInfoOutVO;
        }).collect(Collectors.toList()), page);
    }


    @Override
    public synchronized ProductTicketPO verify(Long id) {
        ProductTicketPO productTicketPO = getById(id);
        if (productTicketPO == null) {
            throw new CommonException("工单id不存在" + id);
        }
        if (!Integer.valueOf(TicketStatusEnum.IN_PROGRESS.getCode()).equals(productTicketPO.getStatus())) {
            throw new CommonException("工单不是处理中状态，工单" + productTicketPO.getTicketRequestId());
        }

        if (!SecurityUtil.getCompanySite().equals(SiteEnum.HSXC.getCode())) {
            GetMachineTaskByNameInVO inVO = new GetMachineTaskByNameInVO();
            inVO.setMachineName(productTicketPO.getMachineName());
            GetMachineTaskByNameOutVO machineTask = productMachineTaskService.getMachineTaskByName(inVO);
            if(Objects.isNull(machineTask) || !Objects.equals(machineTask.getType(), MachineTaskTypeEnum.PRODUCTION.getCode())){
                throw new CommonException("当前机台未处于生产中状态，请先在报工开启生产状态");
            }
        }

        return productTicketPO;
    }

    /**
     * 校验只能生成一个工单
     * @param vo
     * @return: void
     * <AUTHOR>
     * @date: 2023/12/19 15:13
     */
    public synchronized void verifyCreateProductTicket(CreateProductTicketInVO vo) {

        /*GetMachineTaskByNameInVO inVO = new GetMachineTaskByNameInVO();
        inVO.setMachineName(vo.getMachineName());
        GetMachineTaskByNameOutVO machineTask = productMachineTaskService.getMachineTaskByName(inVO);
        if(Objects.nonNull(machineTask) && !Objects.equals(machineTask.getType(), MachineTaskTypeEnum.PRODUCTION.getCode())){
            throw new CommonException("当前机台未处于生产中状态，请先在报工开启生产状态");
        }*/
        String productNo = sfaaTService.getSfaaTByTicket(vo.getPlanTicketNo());
        if(productNo == null){
            throw new CommonException("erp中不存在工单号：" + vo.getPlanTicketNo());
        }

        ProductTicketPO productionTicket = getProductionTicket(vo.getMachineName());
        if (ObjectUtil.isNotNull(productionTicket)) {
            throw new CommonException(String.format("%s已存在工单：日期%s班次%s请先结束对应工单", vo.getMachineName(),
                    DateUtil.format(productionTicket.getProduceDate(), DatePattern.NORM_DATE_PATTERN), ProductShitEnum.getProductShitEnum(productionTicket.getShift()).getName()));
        }
    }

    @RedisLock
    @Transactional
    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public Long createAndProcessor(String redisLockKey, CreateProductTicketInVO vo) {
        if (SecurityUtil.getCompanySite().equals(SiteEnum.AHGC.getCode())
                || SecurityUtil.getCompanySite().equals(SiteEnum.HSXC.getCode())
        ) {
            // 安徽工厂不校验
        } else {
            // 其他工厂校验
            List<ProductionPlanPO> productionPlanPOS = productionPlanService.getListByMachineTicket(vo.getMachineName(),
                vo.getProduceDate(), vo.getShift(), vo.getPlanTicketNo());
            if (CollectionUtil.isEmpty(productionPlanPOS)) {
                throw new CommonException("当前机台" + vo.getMachineName() + "\n日期"
                    + DateUtil.format(vo.getProduceDate(), DatePattern.NORM_DATE_PATTERN) + "\n班次" + vo.getShift()
                    + "\n未排产此工单号" + (StringUtil.isBlank(vo.getPlanTicketNo()) ? "" : vo.getPlanTicketNo())
                    + "\n请联系生产计划排产并导入后台");
            }
        }


        log.info("Seata全局事务id=================>{}", RootContext.getXID());
        // 校验只能生成一个工单
        verifyCreateProductTicket(vo);
        // 校验  需要去erp校验工单状态

        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(vo.getPlanTicketNo());
/*        if (StringUtils.isNotBlank(sfaaTVO.getSfaastus()) && !sfaaTVO.getSfaastus().toUpperCase().equals("F")) {
            throw new CommonException("查询工单在ERP不是生产中状态，不允许创建任务，工单号：" + vo.getPlanTicketNo());
        }*/
        // 校验机位信息
        MachineConfigInVO machineConfigInVO = new MachineConfigInVO();
        machineConfigInVO.setMachineName(vo.getMachineName());
        Integer isp = productionMachineConfigService.getMachineParts(machineConfigInVO);
        if (isp.equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))) {
            if (CollectionUtil.isEmpty(vo.getPartList())) {
                throw new CommonException("当前机台需要机位信息，请选择机位信息");
            }
            Set<String> partsSet = Sets.newConcurrentHashSet();
            Set<String> placeSet = Sets.newConcurrentHashSet();
            vo.getPartList().forEach(part -> {
                if (StringUtils.isBlank(part.getParts()) || part.getPlace().length() < 1 ) {
                    throw new CommonException("机位或者用途存在空的值，请检查");
                }
                boolean add = partsSet.add(part.getParts());
                boolean add1 = placeSet.add(part.getPlace());
                if (!add || !add1) {
                    throw new CommonException("工单中存在重复的机位或者部位信息，请检查" + part.getParts() + " " + part.getPlace());
                }
            });
        }


        // 保存到本地工单数据
        vo.setTicketType(TicketTypeEnum.PRODUCTION.getCode());
        ProductTicketPO productTicketPO = saveProductTicket(vo);

        ProductionMachineOutVO productionMachineOutVO = productionMachineService
                .getProductionMachineByErpName(productTicketPO.getMachineName(), productTicketPO.getProcess());
        if (productionMachineOutVO == null) {
            throw new CommonException("查无此设备，请在机台管理中维护，机台名称：" + productTicketPO.getMachineName() + "工序：" + productTicketPO.getProcess());
        }
        productTicketPO.setCompanyCode(productionMachineOutVO.getEnterpriseNumbers());
        productTicketPO.setProcessCode(productionMachineOutVO.getProcessCode());


        // 日结后 不可再次创建工单
        GetMaterialDailySettlementInVO getMaterialDailySettlementInVO = new GetMaterialDailySettlementInVO();
        getMaterialDailySettlementInVO.setMachineName(productTicketPO.getMachineName());
        getMaterialDailySettlementInVO.setProduceDate(productTicketPO.getProduceDate());
        getMaterialDailySettlementInVO.setShift(productTicketPO.getShift());
        ProductMachineDayOutVO productMachineDay = productMachineDayService.getProductMachineDay(getMaterialDailySettlementInVO);
        if (ObjectUtil.isNotNull(productMachineDay) && productMachineDay.getIsFinish().equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))) {
            throw new CommonException("该机台在" + DateUtil.format(vo.getProduceDate(), DatePattern.NORM_DATE_PATTERN) + "已日结，不可再次创建任务");
        }

        SfcbTVO sfcbTVO = sfcbTService.getByTickNoAndProcess(productTicketPO.getPlanTicketNo(), productTicketPO.getProcessCode());
        if (sfcbTVO == null) {
            throw new CommonException("工单" + productTicketPO.getPlanTicketNo() + "不存在工序" + productTicketPO.getProcessCode() + ",请联系管理员");
        }

        if (StringUtils.isBlank(productTicketPO.getUnit())) {
            // 获得工序的单位
            EcaaucTPO ecaaucTPO =
                    ecaaucTService.getByCompanyProcess(productTicketPO.getCompanyCode(), productTicketPO.getProcessCode());
            if (ecaaucTPO != null) {
                if (StringUtils.isBlank(ecaaucTPO.getEcaauc009())) {
                    throw new CommonException("EcaaucT未找到对应工序的单位,工序编号:" + productTicketPO.getProcessCode());
                }
                productTicketPO.setUnit(UnitEnum.getUnitEnum(ecaaucTPO.getEcaauc009()).getName());
            }
        }
        // 调用工单中心，创建工单
        TicketDTO ticketDTO = pushDataToTicket(productTicketPO);
        // 更新工单信息
        updateProductTicket(productTicketPO,ticketDTO);

        // 保存班组信息
        saveTeamUsers(vo, productTicketPO);

        // 机台绑定工程单号
        bindMachineAndTicket(productTicketPO);

        // 创建机台和工单的关系
        createRelation(productTicketPO);

        // 创建一单一结
        setMachineDay(productTicketPO);

        // 保存机位信息
        saveTicketPartsInfo(productTicketPO,vo);
        return productTicketPO.getId();
    }

    @Override
    public String callMaterial(CallMaterialInVO vo) {
        ProductTicketPO productTicketPO = getById(vo.getProductTicketId());
        if (jinshanLogisticsConfig.getCompanyCodes().contains(SecurityUtil.getCompanySite())
                && !LogisticsConst.TILE_WIRE_PROCESS.contains(productTicketPO.getProcessCode())
                && !LogisticsConst.SLITTER_PROCESS.contains(productTicketPO.getProcessCode())
                && !LogisticsConst.GENERAL_PRINTING_PROCESS.contains(productTicketPO.getProcessCode())) {
            // 呼市新厂 接入物流 排除首道瓦线工序
            AssertUtil.isNotEmpty(vo.getErpOrderIdList(), "ERP订单编号不能为空");
            StringBuilder errorSb = new StringBuilder();
            vo.getErpOrderIdList().forEach(erpOrderId -> {
                String errorMessage = iLogisticsService.callForMaterial(productTicketPO.getId(), erpOrderId);
                errorSb.append(errorMessage);
                log.info("工单:{} ,erpOrderId:{},叫料结果:{}", productTicketPO.getPlanTicketNo(), erpOrderId, errorMessage);
            });
            // 启动供料任务
            log.info("启动供料计划");
            StartCallMaterialInVO startCallMaterialInVO = new StartCallMaterialInVO();
            startCallMaterialInVO.setProductTicketId(productTicketPO.getId());
            iLogisticsService.startCallMaterial(startCallMaterialInVO);

            if(StringUtils.isNotBlank(errorSb.toString())){
                return errorSb.toString();
            }
        }
        return "";

    }

    private void saveTicketPartsInfo(ProductTicketPO productTicketPO, CreateProductTicketInVO vo) {
        if (CollectionUtil.isEmpty(vo.getPartList())) {
            return;
        }

        List<ProductMachinePartsPO> productMachinePartsPOs = vo.getPartList().stream().map(part -> {
            ProductMachinePartsPO productMachinePartsPO = new ProductMachinePartsPO();
            productMachinePartsPO.setMachineName(productTicketPO.getMachineName());
            productMachinePartsPO.setProductTicketId(productTicketPO.getId());
            productMachinePartsPO.setProcessCode(productTicketPO.getProcessCode());
            productMachinePartsPO.setProduceDate(productTicketPO.getProduceDate());
            productMachinePartsPO.setShift(productTicketPO.getShift());
            productMachinePartsPO.setCompanyCode(SecurityUtil.getCompanySite());
            productMachinePartsPO.setCreateBy(productTicketPO.getCreateBy());
            productMachinePartsPO.setUpdateBy(productTicketPO.getCreateBy());
            productMachinePartsPO.setParts(part.getParts());
            productMachinePartsPO.setPartsName(part.getPartsName());
            productMachinePartsPO.setPlace(part.getPlace());
            productMachinePartsPO.setPlaceName(part.getPlaceName());
            return productMachinePartsPO;
        }).collect(Collectors.toList());
        productMachinePartsService.saveBatch(productMachinePartsPOs);
    }

    private void setMachineDay(ProductTicketPO productTicketPO) {
//        // 获得结单类型
//        Integer billingType = productConfigService.getBillingType();
//        // 并且需要时一单一结，才需要调用erp接口，推送消耗数量
//        if (ProductConfigBillingTypeEnum.ONE_DAY.getIntCode().equals(billingType)) {
            ProductMachineDayDTO productMachineDayDTO = new ProductMachineDayDTO();
            productMachineDayDTO.setMachineName(productTicketPO.getMachineName());
            productMachineDayDTO.setProduceDate(productTicketPO.getProduceDate());
            productMachineDayDTO.setShift(productTicketPO.getShift());
            productMachineDayDTO.setProcessCode(productTicketPO.getProcessCode());
            productMachineDayService.saveProductMachineDay(productMachineDayDTO);
//        }
    }

    private void saveTeamUsers(CreateProductTicketInVO vo, ProductTicketPO productTicketPO) {
        ProductShiftInVO productShiftInVO = new ProductShiftInVO();
        productShiftInVO.setProductTicketId(productTicketPO.getId());
        List<ProductShiftInVO.TeamUser> teamUsers = vo.getTeamUsers().stream().map(teamUser -> {
            ProductShiftInVO.TeamUser teamUser1 = new ProductShiftInVO.TeamUser();
            teamUser1.setUserId(teamUser.getUserId());
            teamUser1.setRoleCode(teamUser.getRoleCode());
            teamUser1.setRoleName(teamUser.getRoleName());
            return teamUser1;
        }).collect(Collectors.toList());
        productShiftInVO.setTeamUsers(teamUsers);
        productShiftService.saveProductShift(productShiftInVO);
    }

    private void bindMachineAndTicket(ProductTicketPO productTicketPO) {
        ProductMachineTicketPO productMachineTicketPO =
            productMachineTicketService.getByMachineName(productTicketPO.getMachineName());
        if (ObjectUtil.isNotNull(productMachineTicketPO)) {
            if (productMachineTicketPO.getPlanTicketNo().equals(productTicketPO.getPlanTicketNo())) {
                // 相同工程单不处理
                return;
            }
            MachineConfigInVO machineConfigInVO = MachineConfigInVO.builder()
                    .machineName(productTicketPO.getMachineName())
//                    .processCode(productTicketPO.getProcessCode())
                    .build();
            Integer billingType = productionMachineConfigService.getBillingType(machineConfigInVO);
            if (ProductConfigBillingTypeEnum.ONE_ORDER.getIntCode().equals(billingType)) {
                // 工程单不同，则需要结单后再操作
                throw new CommonException("机台正在生产工程单{}，与选择的工程单不同，请将{}结单后再创建工单", productMachineTicketPO.getPlanTicketNo(),
                        productTicketPO.getPlanTicketNo(),productMachineTicketPO.getPlanTicketNo());
            } else if (ProductConfigBillingTypeEnum.ONE_DAY.getIntCode().equals(billingType)) {
                // 解绑工程单号
                productMachineTicketService.updateByMachineName(productMachineTicketPO.getMachineName(),productMachineTicketPO.getPlanTicketNo());
            }
        }
        // 创建一条新的绑定信息
        ProductMachineTicketDTO productMachineTicketDTO = new ProductMachineTicketDTO();
        productMachineTicketDTO.setPlanTicketNo(productTicketPO.getPlanTicketNo());
        productMachineTicketDTO.setMachineName(productTicketPO.getMachineName());
        productMachineTicketDTO.setStatus("1");
        productMachineTicketService.save(productMachineTicketDTO);
    }

    private void createRelation(ProductTicketPO productionTicket) {
        ProductMachineTicketRelationDTO productMachineTicketRelationDTO = new ProductMachineTicketRelationDTO();
        productMachineTicketRelationDTO.setCompanyCode(productionTicket.getCompanyCode());
        productMachineTicketRelationDTO.setMachineName(productionTicket.getMachineName());
        productMachineTicketRelationDTO.setCurrentTicketId(productionTicket.getId());
        productMachineTicketRelationService.createRelation(productMachineTicketRelationDTO);
    }


    private void updateProductTicket(ProductTicketPO productTicketPO, TicketDTO ticketDTO) {
        log.info("productTicketPO return:{}", JSON.toJSONString(ticketDTO));
            // 设置实际生产时间
            productTicketPO.setStartDate(new java.util.Date());
            productTicketPO.setTicketRequestId(Objects.nonNull(ticketDTO)?ticketDTO.getRequestId():null);
            productTicketPO.setStatus(TicketStatusEnum.IN_PROGRESS.getCode());
            productTicketPO.setProcessorId(productTicketPO.getCreateBy());
            updateById(productTicketPO);
    }

    /**
     * 获得当前机台的工单
     *
     * @param machineName
     * @return {@link String}
     */
    @Override
    public ProductTicketPO getProductionTicket(String machineName) {
        LambdaQueryWrapper<ProductTicketPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductTicketPO::getMachineName, machineName)
                .eq(ProductTicketPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ProductTicketPO::getStatus,Integer.valueOf(TicketStatusEnum.IN_PROGRESS.getCode()))
                ;
        List<ProductTicketPO> productTicketPOs =list(lambdaQueryWrapper);
        if (CollectionUtil.isEmpty(productTicketPOs)) {
            return null;
        }
        return productTicketPOs.get(0);
    }

    @Override
    public ProductTicketPO getLastProductionTicket(String machineName) {
        LambdaQueryWrapper<ProductTicketPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductTicketPO::getMachineName, machineName)
                .eq(ProductTicketPO::getCompanyCode, SecurityUtil.getCompanySite())
        ;
        lambdaQueryWrapper.orderByDesc(ProductTicketPO::getId);
        List<ProductTicketPO> productTicketPOs =list(lambdaQueryWrapper);
        if (CollectionUtil.isEmpty(productTicketPOs)) {
            return null;
        }
        return productTicketPOs.get(0);
    }

    /**
     * 获得当前机台的工单
     *
     * @param machineName
     * @return {@link String}
     */
    @Override
    public Long getProductionTicketNo(String machineName) {
        // 校验只能生成一个工单
        ProductTicketPO productTicketPO = getProductionTicket(machineName);
        return ObjectUtil.isNotNull(productTicketPO) ? productTicketPO.getId() : null;
    }

    /**
     * 获得当前机台的工单
     *
     * @param machineName
     * @return {@link String}
     */
    @Override
    public Long getProductionMachineTicket(String machineName,Integer ticketType) {
        // 查询机台和工程单的关系
        ProductMachineTicketPO productMachineTicketPO = productMachineTicketService.getByMachineName(machineName);
        if (Integer.valueOf(TicketTypeEnum.CHANGE_PRODUCTION.getCode()).equals(ticketType)) {
            if (productMachineTicketPO == null) {
                // 如果没绑定  直接返回null
                return null;
            }
        } else {
            if (productMachineTicketPO == null) {
                // 如果没绑定  直接返回null
                throw new CommonException("当前机台无待产的工程单，需要先去转产后再开始生产");
            }
        }
        // 校验只能生成一个工单
        ProductTicketPO productionTicket = getProductionTicket(machineName);
        if (ObjectUtil.isNotNull(productionTicket) && !productionTicket.getTicketType().equals(ticketType)) {
            throw new CommonException(String.format("%s已存在工单：日期%s班次%s请先结束对应工单", machineName,
                    DateUtil.format(productionTicket.getProduceDate(), DatePattern.NORM_DATE_PATTERN), ProductShitEnum.getProductShitEnum(productionTicket.getShift()).getName()));
        }


        // 绑定了，根据机台名称和工程单号 查询工单id
        LambdaQueryWrapper<ProductTicketPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductTicketPO::getMachineName, machineName)
                .eq(ProductTicketPO::getPlanTicketNo,productMachineTicketPO.getPlanTicketNo())
                .eq(ProductTicketPO::getStatus,Integer.valueOf(TicketStatusEnum.IN_PROGRESS.getCode()))
                .eq(ticketType != null,ProductTicketPO::getTicketType,ticketType);
        ProductTicketPO productTicketPO = getOne(lambdaQueryWrapper);
        return ObjectUtil.isNotNull(productTicketPO) ? productTicketPO.getId() : null;
    }

    /**
     * 获得当前机台的详情
     * 
     * @param id
     * @return {@link ProductionTicketInfoOutVO}
     */
    @Override
    public ProductionTicketInfoOutVO getProductionTicketInfo(Long id) {
        return getProductionTicketInfoOutVO(id,null);
    }

    /**
     * 获得工单详情
     * @param id
     * @return
     */
    private ProductionTicketInfoOutVO getProductionTicketInfoOutVO(Long id,Integer status) {
        LambdaQueryWrapper<ProductTicketPO> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(ProductTicketPO::getId, id)
                .eq(ProductTicketPO::getCompanyCode, SecurityUtil.getCompanySite())
                .eq(ObjectUtil.isNotNull(status),ProductTicketPO::getStatus,status);
        ProductTicketPO productTicketPO = getOne(lambdaQueryWrapper);
        if (ObjectUtil.isNull(productTicketPO)) {
            log.error("不存在生产中的工单，工单id：" + id);
            if (status == null) {
                throw new CommonException("当前据点不存在此任务，任务id：" + id);
            } else {
                throw new CommonException("当前据点不存在状态为" + TicketStatusEnum.getTicketStatusEnum(status).getName() + "的任务，任务id：" + id);
            }
        }
        if (StringUtils.isBlank(productTicketPO.getUnit())) {
            // 获得工序的单位
            EcaaucTPO ecaaucTPO =
                    ecaaucTService.getByCompanyProcess(productTicketPO.getCompanyCode(), productTicketPO.getProcessCode());
            if (ecaaucTPO != null) {
                productTicketPO.setUnit(UnitEnum.getUnitEnum(ecaaucTPO.getEcaauc009()).getName());
            }
        }
        ProductionTicketInfoOutVO productionTicketInfoOutVO =
            BeanUtil.copyProperties(productTicketPO, ProductionTicketInfoOutVO.class);

        // 获得报工数量
        List<ProductMachineTaskPO> productMachineTaskPOS = productMachineTaskService.getListByProductId(id);
        if (CollectionUtil.isNotEmpty(productMachineTaskPOS)) {
            BigDecimal reportedQuantity = productMachineTaskPOS.stream().map(ProductMachineTaskPO::getReportedQuantity)
                    .filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            productionTicketInfoOutVO.setReportedQuantity(reportedQuantity);
        }

        // 获得未判定的不良品数量
        List<ProductDefectiveProductsPO> productMachineDefectivePOS =
                productDefectiveProductsService.getListByProductTicketId(id);
        if (CollectionUtil.isNotEmpty(productMachineDefectivePOS)) {
            BigDecimal unconfirmedQuantity = productMachineDefectivePOS.stream().map(ProductDefectiveProductsPO::getDefectiveProductsQuantity)
                    .filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            productionTicketInfoOutVO.setUnconfirDefectiveQuantity(unconfirmedQuantity);
        }


        if (StringUtils.isNotBlank(productionTicketInfoOutVO.getTeamUsers()) && !chinesePattern.matcher(productionTicketInfoOutVO.getTeamUsers()).find()) {
            String[] ids = productionTicketInfoOutVO.getTeamUsers().split(",");
            List<UserDTO> userInfoByIds =
                ia01Service.getUserInfoByIds(Arrays.stream(ids).map(i -> Long.valueOf(i)).collect(Collectors.toList()));
            productionTicketInfoOutVO
                .setTeamUsersName(userInfoByIds.stream().map(UserDTO::getName).collect(Collectors.joining(",")));
        } else {
            productionTicketInfoOutVO.setTeamUsersName(productionTicketInfoOutVO.getTeamUsers());
        }

        // 各个工序开单损耗率
        SfcbTVO sfcbTVO = sfcbTService.getByTickNoAndProcess(productTicketPO.getPlanTicketNo(), productTicketPO.getProcessCode());
        if (sfcbTVO == null) {
            throw new CommonException("工单" + productTicketPO.getPlanTicketNo() + "不存在工序" + productTicketPO.getProcessCode() + ",请联系管理员");
        }
        productionTicketInfoOutVO.setBillingPcsQuantity(sfcbTVO != null?sfcbTVO.getSfcbud013():null);

        // 汇总这个工程单 这个机台的  所以已报工数量
        List<Long> ids =
                getListByPlanTicketNoAndProcess(productTicketPO.getPlanTicketNo(),productTicketPO.getProcessCode()).stream().map(ProductTicketPO::getId).collect(Collectors.toList());
        List<ProductMachineTaskPO> productMachineTasks = productMachineTaskService.getByProductTicketIds(ids);
        if (CollectionUtil.isNotEmpty(productMachineTasks)) {
            BigDecimal produceTotalQuantity = productMachineTasks.stream().map(ProductMachineTaskPO::getReportedQuantity)
                    .filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            productionTicketInfoOutVO.setProduceTotalQuantity(produceTotalQuantity);
        }

        productionTicketInfoOutVO.setPlannedProductUnit(productTicketPO.getUnit());
        productionTicketInfoOutVO.setReportedQuantityUnit(productTicketPO.getUnit());
        productionTicketInfoOutVO.setProduceTotalQuantityUnit(productTicketPO.getUnit());
        try {
            productionTicketInfoOutVO.setBillingPcsQuantityUnit(UnitEnum.getUnitEnum(sfcbTVO.getSfcb020()).getName());
        } catch (Exception e) {
            log.error("单位转换异常", sfcbTVO.getSfcb020());
        }

        if (jinshanLogisticsConfig.getCompanyCodes().contains(SecurityUtil.getCompanySite())) {
            // 查询机台物流配置信息
            ProductionMachineLogisticsConfigPO machineConfig = productionMachineLogisticsConfigService.getMachineConfig(
                productionTicketInfoOutVO.getMachineName(), productionTicketInfoOutVO.getProcessCode());
            productionTicketInfoOutVO.setWorkshopCode(machineConfig.getWorkshopCode());
            productionTicketInfoOutVO.setSupplyMeshCode(machineConfig.getSupplyMeshCode());
            productionTicketInfoOutVO.setExitMeshCode(machineConfig.getExitMeshCode());
            productionTicketInfoOutVO.setPrepareMeshCount(machineConfig.getPrepareMeshCount());
            productionTicketInfoOutVO.setOutboundType(machineConfig.getOutboundType());
        }

        // 获得结单类型
        MachineConfigInVO machineConfigInVO = MachineConfigInVO.builder()
                .machineName(productTicketPO.getMachineName())
                .build();
        Integer billingType = productionMachineConfigService.getBillingType(machineConfigInVO);
        // 有一个未完成的，则认为这个工单还没完成
        Boolean isFinish = false;
        // 一日一节需要增加分摊判断
        if (billingType.equals(ProductConfigBillingTypeEnum.ONE_DAY.getIntCode())) {
            isFinish = productionTicketInfoOutVO.getIsPourMaterial().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))
                    || productionTicketInfoOutVO.getIsSignUp().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))
                    || productionTicketInfoOutVO.getIsDefectionSignUp().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))
                    || productionTicketInfoOutVO.getIsDeductMaterial().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()));
        } else {
            isFinish = productionTicketInfoOutVO.getIsPourMaterial().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))
                    || productionTicketInfoOutVO.getIsSignUp().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))
                    || productionTicketInfoOutVO.getIsDefectionSignUp().equals(Integer.valueOf(BooleanEnum.FALSE.getCode()));
            productionTicketInfoOutVO.setIsDeductMaterial(null);
        }
        productionTicketInfoOutVO.setShow(isFinish ? Integer.valueOf(BooleanEnum.TRUE.getCode()) : Integer.valueOf(BooleanEnum.FALSE.getCode()));

        return productionTicketInfoOutVO;
    }

    /**
     * 获得当前机台的设备止码
     */
    @Override
    public Integer getMachineStopNo(Long id) {
        LambdaQueryWrapper<ProductTicketPO> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(ProductTicketPO::getId, id);
        ProductTicketPO productTicketPO = getOne(lambdaQueryWrapper);
        if (ObjectUtil.isNull(productTicketPO)) {
            throw new CommonException("未查询到对应工单");
        }
        return productTicketPO.getMachineStopNo();
    }

    @Override
    public void updateMachineStopNo(Long id, Integer stopNo) {
        /*if (stopNo == null) {
            throw new CommonException("设备止码不能为空");
        }*/
        LambdaUpdateWrapper<ProductTicketPO> lambdaQueryWrapper = new LambdaUpdateWrapper();
        lambdaQueryWrapper.eq(ProductTicketPO::getId, id).set(ProductTicketPO::getMachineStopNo, stopNo);
        update(lambdaQueryWrapper);
    }

    @Override
    public void updateStatus(ProductTicketPO productTicketPO, int status, BigDecimal producedQuantity) {
        // 设置实际生产时间 及 数量
        productTicketPO.setStatus(status);
        // 结束时间和实际生产数量
        productTicketPO.setEndDate(new java.util.Date());

        productTicketPO.setRealProduct(
            (productTicketPO.getRealProduct() == null ? BigDecimal.ZERO : productTicketPO.getRealProduct())
                .add(producedQuantity));
        productTicketPO.setUpdateBy(SecurityUtil.getUserId());

        updateById(productTicketPO);
    }

    @Override
    public ProductTicketShiftOutVO getPlanTicketNoList(ProductTicketPO productTicketPO) {
        return baseMapper.getPlanTicketNoList(productTicketPO.getMachineName(), productTicketPO.getProduceDate(),
            productTicketPO.getShift(), productTicketPO.getPlanTicketNo(), SecurityUtil.getCompanySite(),
            productTicketPO.getProcessCode(), productTicketPO.getProcessType());
    }

    @Override
    public Pagination<ProductionTicketInfoOutVO> getTicketByTicketBase(IPage page,
        ProductTicketBaseDTO productTicketBaseDTO) {
        Page<ProductionTicketInfoOutVO> iPage =
            baseMapper.getTicketByTicketBase(page, productTicketBaseDTO);
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return Pagination.newInstance(null);
        }

        String userIdsStr = iPage.getRecords().stream().map(p -> p.getTeamUsers()).filter(StringUtil::isNotBlank)
            .collect(Collectors.joining(","));
        List<Long> userIds = Lists.newArrayList();
        Map<Long, String> userMap = Maps.newConcurrentMap();
        if (StringUtils.isNotBlank(userIdsStr) && !chinesePattern.matcher(userIdsStr).find()) {
            userIds = Arrays.stream(userIdsStr.split(",")).map(u -> Long.valueOf(u)).collect(Collectors.toList());
            userMap.putAll(ia01Service.getUserInfoByIds(userIds).stream()
                    .collect(Collectors.toMap(UserDTO::getId, UserDTO::getName)));
        }

        return Pagination.newInstance(iPage.getRecords().stream().map(productTicketPO -> {
            ProductionTicketInfoOutVO productionTicketInfoOutVO =
                    BeanUtil.copyProperties(productTicketPO, ProductionTicketInfoOutVO.class);
            if (chinesePattern.matcher(productTicketPO.getTeamUsers()).find()) {
                productionTicketInfoOutVO.setTeamUsersName(productTicketPO.getTeamUsers());
            } else {
                String userName = Arrays.stream(productTicketPO.getTeamUsers().split(",")).filter(StringUtil::isNotBlank).map(id -> {
                    return userMap.get(Long.valueOf(id));
                }).collect(Collectors.joining(","));
                productionTicketInfoOutVO.setTeamUsersName(userName);
            }

            if (productionTicketInfoOutVO.getStatus() != null) {
                productionTicketInfoOutVO.setStatusName(TicketStatusEnum
                    .getTicketStatusEnum(productionTicketInfoOutVO.getStatus()).getCnName());
            }
            return productionTicketInfoOutVO;
        }).collect(Collectors.toList()), page);
    }


    /**
     * 保存到本地工单数据
     * 
     * @param vo
     */
    private ProductTicketPO saveProductTicket(CreateProductTicketInVO vo) {
        ProductTicketPO productTicketPO = BeanUtil.copyProperties(vo, ProductTicketPO.class);
        // 设置生产工单号
        String bizNo = generateBizNo(BIZNAME);
        productTicketPO.setBusinessNo(bizNo);
        List<Long> userIds = vo.getTeamUsers().stream().map(teamUser -> Long.valueOf(teamUser.getUserId())).distinct().collect(Collectors.toList());
        Map<Long, String> userMap =
                ia01Service.getUserInfoByIds(userIds).stream().collect(Collectors.toMap(UserDTO::getId, UserDTO::getName));
        vo.setTeamUsers(vo.getTeamUsers().stream().sorted(Comparator.comparing(CreateProductTicketInVO.TeamUser::getRoleCode))
                .collect(Collectors.toList()));
        String teamUserName = vo.getTeamUsers().stream().map(teamUser -> {
            return userMap.get(Long.valueOf(teamUser.getUserId())) + "(" + teamUser.getRoleName() + ")";
        }).collect(Collectors.joining(","));
        productTicketPO.setTeamUsers(teamUserName);
        productTicketPO.setCreateBy(SecurityUtil.getUserId());
        save(productTicketPO);
        return productTicketPO;
    }

    private String generateBizNo(String preCode) {
        StringBuilder sb = new StringBuilder();
        sb.append(preCode);
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        sb.append(format.format(System.currentTimeMillis()));
        // 获取订单号递增数
        Long orderSequence = getAndIncrementOrderSequence();
        String rad = String.format("%04d", orderSequence);
        sb.append(rad);
        return sb.toString();
    }



    private Long getAndIncrementOrderSequence() {
        // 使用Redis加锁
        boolean lockAcquired = acquireLock();
        if (!lockAcquired) {
            throw new CommonException("系统繁忙，请稍后再试");
        }

        try {
            // 获取当前订单号递增数
            ValueOperations<String, String> valueOps = redisTemplate.opsForValue();
            String orderSequenceStr = valueOps.get(ORDER_KEY);
            Long orderSequence = (orderSequenceStr != null) ? Long.parseLong(orderSequenceStr) : 0;

            // 递增订单号递增数
            orderSequence++;
            if (orderSequence>=10000) {
                orderSequence = 1L;
            }

            // 更新订单号递增数到Redis
            valueOps.set(ORDER_KEY, orderSequence.toString());

            return orderSequence;
        } finally {
            // 释放锁
            releaseLock();
        }
    }

    private boolean acquireLock() {
        // 使用Redis的SET命令实现分布式锁，并设置过期时间
        Boolean lockAcquired = redisTemplate.opsForValue().setIfAbsent(LOCK_KEY, "locked", 10, TimeUnit.SECONDS);
        return lockAcquired != null && lockAcquired;
    }

    private void releaseLock() {
        // 释放锁
        redisTemplate.delete(LOCK_KEY);
    }


    /**
     * 调用工单中心，创建工单
     * 
     * @return {@link TicketDTO}
     * @param vo
     */
    private TicketDTO pushDataToTicket(ProductTicketPO vo) {
        CreateAndAssignTicketRequest createTicketRequest = new CreateAndAssignTicketRequest();
        createTicketRequest.setBizRequestId(vo.getBusinessNo());
        createTicketRequest.setTicketTitle(TICKET_NAME);
        createTicketRequest.setTicketDesc(vo.getMachineName());
        createTicketRequest.setTicketTypeCode(TicketTypeEnum.PRODUCTION.getCode());
        createTicketRequest.setOwnerUserId(SecurityUtil.getUserId());
        createTicketRequest.setOwner(SecurityUtil.getUserName());
        log.info("pushDataToTicket request:{}", JSON.toJSONString(createTicketRequest));
        return iTicketService.createAndDoWork(createTicketRequest);
    }

    @Override
    public List<GetListByPlanTicketNoOutVO> getListByPlanTicketNo(String planTicketNo, Integer status) {
        LambdaQueryWrapper<ProductTicketPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StrUtil.isNotBlank(planTicketNo),ProductTicketPO::getPlanTicketNo,planTicketNo)
                .eq(Objects.nonNull(status),ProductTicketPO::getStatus,status);
        wrapper.orderByAsc(ProductTicketPO::getStartDate).orderByAsc(ProductTicketPO::getShift);
        return BeanUtil.copyToList(list(wrapper), GetListByPlanTicketNoOutVO.class);
    }

    @Override
    public List<ProductTicketPO> getListByPlanTicketNo(String planTicketNo,String machineName) {
        LambdaQueryWrapper<ProductTicketPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StrUtil.isNotBlank(planTicketNo),ProductTicketPO::getPlanTicketNo,planTicketNo)
                .eq(StringUtils.isNotBlank(machineName),ProductTicketPO::getMachineName,machineName);
        return list(wrapper);
    }

    @Override
    public List<ProductTicketPO> getListByPlanTicketNo(String planTicketNo) {
        LambdaQueryWrapper<ProductTicketPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StrUtil.isNotBlank(planTicketNo),ProductTicketPO::getPlanTicketNo,planTicketNo);
        return list(wrapper);
    }

    @Override
    public List<ProductTicketPO> getListByPlanTicketNoAndProcess(String planTicketNo, String process) {
        LambdaQueryWrapper<ProductTicketPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StrUtil.isNotBlank(planTicketNo),ProductTicketPO::getPlanTicketNo,planTicketNo)
                .eq(StrUtil.isNotBlank(process),ProductTicketPO::getProcessCode,process);
        return list(wrapper);
    }

    @Override
    public List<ProductChangeProdutionOutVO> getChangeProductTicketList(ProductChangeProductionDTO productChangeProductionDTO) {
        return getProductChangeProdutionOutVOS(productChangeProductionDTO.getPlanTicketNo(),
                productChangeProductionDTO.getMachineName(), TicketTypeEnum.CHANGE_PRODUCTION.getCode());
    }

    @Override
    public Long createFinishProduction(CreateFinishProductionInVO vo) {
        // 校验只能生成一个工单
        ProductTicketPO productionTicket = getProductionTicket(vo.getMachineName());
        if (ObjectUtil.isNotNull(productionTicket)) {
            throw new CommonException(String.format("%s已存在工单：日期%s班次%s请先结束对应工单", vo.getMachineName(),
                    DateUtil.format(productionTicket.getProduceDate(), DatePattern.NORM_DATE_PATTERN), ProductShitEnum.getProductShitEnum(productionTicket.getShift()).getName()));
        }
        // 保存到本地工单数据
        vo.setTicketType(TicketTypeEnum.FINISH_PRODUCTION.getCode());
        CreateProductTicketInVO createProductTicketInVO = new CreateProductTicketInVO();
        BeanUtil.copyProperties(vo,createProductTicketInVO);
        ProductTicketPO productTicketPO = saveProductTicket(createProductTicketInVO);

        // 调用工单中心，创建工单
       // TicketDTO ticketDTO = pushDataToTicket(productTicketPO);
        // 更新工单信息
        ProductionMachineOutVO productionMachineOutVO = productionMachineService
                .getProductionMachineByErpName(productTicketPO.getMachineName(), productTicketPO.getProcess());
        productTicketPO.setCompanyCode(productionMachineOutVO.getEnterpriseNumbers());
        productTicketPO.setProcessCode(productionMachineOutVO.getProcessCode());
        updateProductTicket(productTicketPO,null);

        // 创建机台和工单的关系
        createRelation(productTicketPO);

        return productTicketPO.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<ProductChangeProdutionOutVO>
        getFinishProductTicketList(ProductChangeProductionDTO productChangeProductionDTO) {
        return getProductChangeProdutionOutVOS(productChangeProductionDTO.getPlanTicketNo(),
            productChangeProductionDTO.getMachineName(), TicketTypeEnum.FINISH_PRODUCTION.getCode());
    }

    @RedisLock
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String signingUpForWork(String redisLockKey,Long id) {
        ProductTicketPO productTicketPO = getById(id);
        // 校验  需要去erp校验工单状态
        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(productTicketPO.getPlanTicketNo());
        if (StringUtils.isNotBlank(sfaaTVO.getSfaastus()) && !sfaaTVO.getSfaastus().toUpperCase().equals("F")) {
            throw new CommonException("查询erp中工单不是生产中状态：" + productTicketPO.getPlanTicketNo());
        }


        String errInfo = "";
        // 扣料
        try {
            // 分摊扣料
            ProductTicketServiceImpl bean = SpringUtil.getBean(ProductTicketServiceImpl.class);
            bean.deductMaterial(id);
        } catch (Exception e) {
            log.error("分摊扣料失败", e);
            errInfo = errInfo + "分摊扣料失败:" + e.getMessage() + "\n";
        }
        try {
            // 倒扣料
            ProductTicketServiceImpl bean = SpringUtil.getBean(ProductTicketServiceImpl.class);
            bean.pourMaterial(id);
        } catch (Exception e) {
            log.error("倒扣料失败", e);
            errInfo = errInfo + "倒扣料失败:" + e.getMessage() + "\n";
        }

        // 报工
        try {
            ProductTicketServiceImpl bean = SpringUtil.getBean(ProductTicketServiceImpl.class);
            String reportMsg = bean.report(id);
            if (StringUtils.isNotBlank(reportMsg)) {
                errInfo = errInfo + reportMsg + "\n";
            }
        } catch (Exception e) {
            log.error("报工失败", e);
            errInfo = errInfo + "报工失败:" + e.getMessage() + "\n";
        }

        if (errInfo.length() > 0) {
            if (!errInfo.contains("失败")) {
                return errInfo;
            }
            throw new CommonException(errInfo);
        }

        return StringUtils.isBlank(errInfo) ? "上报成功" : errInfo;
    }

    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void pourMaterial(Long id) {
        // 呼市不倒扣料
        if(SiteEnum.HSXC.getCode().equals(SecurityUtil.getCompanySite())) {
            return;
        }

        ProductTicketPO productTicketPO = productTicketService.getById(id);

        if (productTicketPO.getIsPourMaterial().equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))) {
            log.info("物料已分摊扣料，无需再次扣料");
            return;
        }


        // 倒扣料
        List<ProductMachineMaterialApportionmentPO> pos = Lists.newArrayList();
        List<ProductMachineMaterialRecordPO> productMachineMaterialRecordPOS = productMachineMaterialRecordService.getUndercutByProductTicketId(id);
        if (CollectionUtil.isNotEmpty(productMachineMaterialRecordPOS)) {

            InagTDTO inagTDTO = new InagTDTO();
            inagTDTO.setInag001(productMachineMaterialRecordPOS.stream()
                .map(ProductMachineMaterialRecordPO::getMaterialCode).distinct().collect(Collectors.toList()));
            inagTDTO.setInagsite(SecurityUtil.getCompanySite());
            String storageNo = productMachineMaterialRecordPOS.get(0).getStorageNo();
            // 包含库位信息，目前只有安徽需要根据库位去查询库存
            if (StrUtil.isNotBlank(storageNo)) {
                inagTDTO.setInag005(storageNo);
            }
            if (SecurityUtil.getCompanySite().equals("SITE-16")) {
                inagTDTO.setInag004( "X002");
            } else {
                inagTDTO.setInag004("X001");
            }

            List<InagTVO> iInagTList = iInagTService.getIInagTList(inagTDTO);
            List<InagTVO> inagTVOS = iInagTList.stream().filter(i -> i.getInag008().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());


            productMachineMaterialRecordPOS.forEach(p -> {
                // 已扣除的物料
                Map<String, BigDecimal> hadConsumptionMap = new HashMap<>();
                log.info("==========================开始倒扣料：{}", JSON.toJSONString(p.getMaterialCode()));

                BigDecimal requiredQuantity = p.getConsumptionQuantity(); // 需要扣除的总量
                List<String> lotNos = new ArrayList<>(); // 批次号列表

                // 遍历批次物料
                for (InagTVO inagTVO : inagTVOS) {
                    if (!inagTVO.getInag001().equals(p.getMaterialCode())) {
                        continue; // 跳过不同物料
                    }

                    String key = p.getMaterialCode() + "_" + inagTVO.getInag006(); // 物料+批次的唯一标识
                    BigDecimal remainingInBatch = inagTVO.getInag008(); // 当前批次的剩余量
                    BigDecimal alreadyDeducted = hadConsumptionMap.getOrDefault(key, BigDecimal.ZERO); // 获取该批次已扣量

                    // 计算实际剩余量
                    remainingInBatch = remainingInBatch.subtract(alreadyDeducted);
                    if (remainingInBatch.compareTo(BigDecimal.ZERO) <= 0) {
                        log.info("物料{}批次{}的剩余数量为0，跳过此批次", p.getMaterialCode(), inagTVO.getInag006());
                        continue;
                    }

                    log.info("物料{}批次{}的真实剩余数量为：{}", p.getMaterialCode(), inagTVO.getInag006(), remainingInBatch);

                    // 如果当前批次足够扣完所需数量
                    if (remainingInBatch.compareTo(requiredQuantity) >= 0) {
                        BigDecimal deducted = requiredQuantity;
                        hadConsumptionMap.put(key, alreadyDeducted.add(deducted)); // 更新已扣除量
                        log.info("物料{}批次{}扣除数量：{}", p.getMaterialCode(), inagTVO.getInag006(), deducted);
                        requiredQuantity = BigDecimal.ZERO; // 已扣除完毕
                    } else {
                        // 当前批次不足，全部扣除并继续下一批次
                        BigDecimal deducted = remainingInBatch;
                        hadConsumptionMap.put(key, alreadyDeducted.add(deducted)); // 更新已扣除量
                        log.info("物料{}批次{}扣除数量：{}", p.getMaterialCode(), inagTVO.getInag006(), deducted);
                        requiredQuantity = requiredQuantity.subtract(deducted); // 更新剩余需要扣除量
                    }

                    lotNos.add(inagTVO.getInag006()); // 记录已扣批次号

                    // 如果已扣除完毕，结束循环
                    if (requiredQuantity.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                }

                if (requiredQuantity.compareTo(BigDecimal.ZERO) > 0) {
                    throw new CommonException("物料"+ p.getMaterialCode()+"扣除失败，剩余未扣数量：" + requiredQuantity);
                } else {
                    log.info("物料{}扣除成功，批次号：{}", p.getMaterialCode(), lotNos);
                }

                // 后续逻辑：调用ERP接口扣除批次库存，记录到分配列表
                lotNos.forEach(lotNo -> {
                    BigDecimal deductedQuantity = hadConsumptionMap.get(p.getMaterialCode() + "_" + lotNo);
                    log.info("开始扣除批次{}的物料{}，数量：{}", lotNo, p.getMaterialCode(), deductedQuantity);

                    // 调用ERP接口扣料
                    SrmBarcodeStoreDTO srmBarcodeStoreDTO = new SrmBarcodeStoreDTO();
                    srmBarcodeStoreDTO.setItemNo(p.getMaterialCode());
                    srmBarcodeStoreDTO.setCurStorNum(deductedQuantity);
                    srmBarcodeStoreDTO.setLotNos(Collections.singletonList(lotNo));
                    // 包含库位信息，目前只有安徽需要根据库位去查询库存
                    if (StrUtil.isNotBlank(storageNo)) {
                        srmBarcodeStoreDTO.setWarehouseNoId(storageNo);
                    }
                    List<SrmBarcodeStorePO> firstMaterialInfo = srmBarcodeStoreService.getFirstMaterialInfo(srmBarcodeStoreDTO);

                    if (CollectionUtil.isEmpty(firstMaterialInfo)) {
                        log.warn("物料{}批次{}扣除失败：未查询到相关信息", p.getMaterialCode(), lotNo);
                        return;
                    }

                    firstMaterialInfo.forEach(f -> {
                        ProductMachineMaterialApportionmentPO record = BeanUtil.copyProperties(p, ProductMachineMaterialApportionmentPO.class);
                        record.setMaterialBarcodeNo(f.getBarcodeNo());
                        record.setConsumptionQuantity(f.getCurStorNum());
                        record.setPurchaseBatch(f.getLotNo());
                        record.setId(null);
                        record.setMaterialSeq(p.getMaterialSeq());
                        record.setWarehouseNo(f.getWarehouseNo());
                        record.setWarehouseName(f.getWarehouseName());
                        record.setStorageNo(f.getWarehouseNoId());
                        record.setStorageName(f.getStorageSpacesName());
                        pos.add(record);
                    });
                });
            });

        }
        if (CollectionUtil.isEmpty(pos)) {
            // 更新工单
            productTicketPO.setIsPourMaterial(Integer.valueOf(BooleanEnum.TRUE.getCode()));
            updateById(productTicketPO);
            return;
        }

        productMaterialService.batchDeductionMaterial(pos,productTicketPO,"DKL");
        productMachineMaterialApportionmentService.saveBatch(pos);

        // 更新工单
        productTicketPO.setIsPourMaterial(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        updateById(productTicketPO);
    }




    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void deductMaterial(Long id) {
        ProductTicketPO productTicketPO = productTicketService.getById(id);

        if (productTicketPO.getIsDeductMaterial().equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))) {
            log.info("物料已扣料，无需再次扣料");
            return;
        }
        // 查询任务的用料信息
        List<ProductMachineMaterialApportionmentPO> productMachineMaterialApportionmentPOS =
            productMachineMaterialApportionmentService.getByProductTicketId(id);

        Map<Integer, List<ProductMachineMaterialApportionmentPO>> map = productMachineMaterialApportionmentPOS.stream()
            .collect(Collectors.groupingBy(ProductMachineMaterialApportionmentPO::getUseType));

        List<ProductMachineMaterialApportionmentPO> pos = Lists.newArrayList();
        // 正扣料
        List<ProductMachineMaterialApportionmentPO> positiveMaterialPOS = map.get(MaterialUseTypeEnum.POSITIVE_MATERIAL.getIntCode());
        if (CollectionUtil.isNotEmpty(positiveMaterialPOS)) {
            // 正扣料  已经扣料了 无需处理
        }

        // 分摊
        List<ProductMachineMaterialApportionmentPO> apportionmentMaterialPOS = map.get(MaterialUseTypeEnum.APPORTIONMENT_MATERIAL.getIntCode());
        if (CollectionUtil.isNotEmpty(apportionmentMaterialPOS)) {
            pos.addAll(apportionmentMaterialPOS);
        }
        if (CollectionUtil.isEmpty(pos)) {
            return;
        }
        productMaterialService.batchDeductionMaterial(pos,productTicketPO,"FTKL");

        // 更新工单
        productTicketPO.setIsDeductMaterial(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        updateById(productTicketPO);
    }

    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public String report(Long id) {
        ResponseCodeOutVO responseCodeOutVO = new ResponseCodeOutVO();
        ProductTicketPO productTicketPO = productTicketService.getById(id);
        if (Integer.valueOf(BooleanEnum.TRUE.getCode()).equals(productTicketPO.getIsSignUp())
                && Integer.valueOf(BooleanEnum.TRUE.getCode()).equals(productTicketPO.getIsDefectionSignUp()) ) {
//            throw new CommonException("工单已报工，不允许重复报工");
            return null;
        }



        ProductFinishOrderPO productFinishOrderPO =
                productFinishOrderService.getByProductTicketId(id);

        if (productFinishOrderPO == null) {
            throw new CommonException("工单未结单，不允许报工操作");
        }

        // 必须所有不良品都回调了才可以报工
        List<ProductDefectiveProductsPO> productDefectiveProductsPOS =
                productDefectiveProductsService.getListByProductTicketId(id);
        if (CollectionUtil.isNotEmpty(productDefectiveProductsPOS)) {
            List<ProductDefectiveProductsPO> pos = productDefectiveProductsPOS.stream()
                    .filter(p -> Integer.valueOf(BooleanEnum.FALSE.getCode()).equals(p.getIsCallback()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(pos)) {
                throw new CommonException("存在未判定的不良品，不允许报工");
            }
        }

        // 减去上一个工单是数据
        log.info("本工单数量：" + productTicketPO.getRealProduct());

        // 良品数量
        WorkReportInformationPushToErpDTO workReportInformationPushToErpDTO =
                getWorkReportInformationPushToErpDTO(productTicketPO, productFinishOrderPO);
        // 不良品数量
        ReportDefectiveRequestInDTO reportDefectiveRequestInDTO =
                getReportDefectiveRequestInDTO(productTicketPO, productFinishOrderPO);

        // 良品和不良品的数量 要小于等于 上道工序的汇入数量
        if (BooleanEnum.TRUE.getCode().equals(pushSwitchConfig.getReportVerify())) {
            log.info("校验上道工序的汇入数量");
            verifyReportQuantity(productTicketPO, workReportInformationPushToErpDTO, reportDefectiveRequestInDTO);
        }

        // 修改时间
        updateReportDate(productTicketPO,workReportInformationPushToErpDTO,reportDefectiveRequestInDTO);

        StrBuilder message = new StrBuilder();
        Boolean flag = true;

        if (BooleanEnum.TRUE.getCode().equals(pushSwitchConfig.getErpReport())) {
            // 良品和不良品报工
            flag = signGoodAndDefective(productTicketPO, workReportInformationPushToErpDTO, reportDefectiveRequestInDTO, message, flag);
            productTicketService.updateById(productTicketPO);
        }
        if (flag) {
            responseCodeOutVO.setCode(BooleanEnum.TRUE.getCode());
        } else {
            responseCodeOutVO.setCode(BooleanEnum.FALSE.getCode());
        }
        responseCodeOutVO.setMessage(message.toString());
        return message.toString();
    }

    /**
     * 良品和不良品报工
     * @param productTicketPO
     * @param workReportInformationPushToErpDTO
     * @param reportDefectiveRequestInDTO
     * @param message
     * @param flag
     * @return
     */
    private Boolean signGoodAndDefective( ProductTicketPO productTicketPO, WorkReportInformationPushToErpDTO workReportInformationPushToErpDTO, ReportDefectiveRequestInDTO reportDefectiveRequestInDTO, StrBuilder message, Boolean flag) {
        if (Integer.valueOf(BooleanEnum.FALSE.getCode()).equals(productTicketPO.getIsSignUp())) {
            flag = signGood(productTicketPO, workReportInformationPushToErpDTO, message, flag);
        }
        if (Integer.valueOf(BooleanEnum.FALSE.getCode()).equals(productTicketPO.getIsDefectionSignUp())) {
            flag = signDefective(productTicketPO, reportDefectiveRequestInDTO, message, flag);
        }
        return flag;
    }

    private Boolean signDefective( ProductTicketPO productTicketPO, ReportDefectiveRequestInDTO reportDefectiveRequestInDTO, StrBuilder message, Boolean flag) {
        // 报不良
        log.info("调用erp,开始不良报工");
        ProductInterfaceRecordsPO productInterfaceRecordsPO = new ProductInterfaceRecordsPO();
        productInterfaceRecordsPO.setProductTicketId(productTicketPO.getId());
        productInterfaceRecordsPO.setCompanyCode(productTicketPO.getCompanyCode());
        productInterfaceRecordsPO.setMainInfo(productTicketPO.getBusinessNo());
        productInterfaceRecordsPO.setBusinessType(InterfaceBusinessEnum.DEFECTION_REPORT.getCode());
        if (CollectionUtil.isNotEmpty(reportDefectiveRequestInDTO.getReports())) {
            ResponseCodeOutVO response = workReportInformationPushToErpService.reportDefectiveRequest(reportDefectiveRequestInDTO);
            if (BooleanEnum.TRUE.getCode().equals(response.getCode())) {
                message.append("不良品报工成功");
            } else if (response.getMessage().contains("单号已经存在")) {
                // 认为是已成功
                message.append("不良品报工成功,");
                response.setCode(BooleanEnum.TRUE.getCode());
            } else {
                flag = false;
                message.append("不良品报工失败:" + response.getMessage());
            }
            productInterfaceRecordsPO.setResponse(response.getMessage());
            productInterfaceRecordsPO.setResult(Integer.valueOf(response.getCode()));
            productTicketPO.setIsDefectionSignUp(Integer.valueOf(response.getCode()));
        } else{
            message.append("没有不良品，不去报不良");
            productInterfaceRecordsPO.setResponse("没有不良品，不去报不良");
            productTicketPO.setIsDefectionSignUp(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        }
        // 报工记录
        productInterfaceRecordsPO.setCreateBy(SecurityUtil.getUserId());
        productInterfaceRecordsPO.setUpdateBy(SecurityUtil.getUserId());
        productInterfaceRecordsService.save(productInterfaceRecordsPO);
        return flag;
    }

    private Boolean signGood(ProductTicketPO productTicketPO, WorkReportInformationPushToErpDTO workReportInformationPushToErpDTO, StrBuilder message, Boolean flag) {
        log.info("调用erp,开始报工");
        ProductInterfaceRecordsPO productInterfaceRecordsPO = new ProductInterfaceRecordsPO();
        productInterfaceRecordsPO.setProductTicketId(productTicketPO.getId());
        productInterfaceRecordsPO.setCompanyCode(productTicketPO.getCompanyCode());
        productInterfaceRecordsPO.setMainInfo(productTicketPO.getBusinessNo());
        productInterfaceRecordsPO.setBusinessType(InterfaceBusinessEnum.GOOD_REPORT.getCode());
        if (workReportInformationPushToErpDTO.getQuantity() != null && workReportInformationPushToErpDTO.getQuantity().compareTo(BigDecimal.ZERO) > 0) {
            ResponseCodeOutVO response = workReportInformationPushToErpService.pushToErp(workReportInformationPushToErpDTO);
            if (BooleanEnum.TRUE.getCode().equals(response.getCode())) {
                message.append("良品报工成功,");
            } else if (response.getMessage().contains("单号已经存在")) {
                // 认为是已成功
                message.append("良品报工成功,");
                response.setCode(BooleanEnum.TRUE.getCode());
            } else {
                flag = false;
                message.append("良品报工失败:"+response.getMessage());
            }
            productInterfaceRecordsPO.setResponse(response.getMessage());
            productInterfaceRecordsPO.setResult(Integer.valueOf(response.getCode()));
            productTicketPO.setIsSignUp(Integer.valueOf(response.getCode()));
        } else {
            message.append("没有良品，不去报良品报工,");
            productInterfaceRecordsPO.setResponse("没有良品，不去报良品报工");
            productTicketPO.setIsSignUp(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        }
        // 报工记录
        productInterfaceRecordsPO.setCreateBy(SecurityUtil.getUserId());
        productInterfaceRecordsPO.setUpdateBy(SecurityUtil.getUserId());
        productInterfaceRecordsService.save(productInterfaceRecordsPO);
        return flag;
    }

    private void updateReportDate(ProductTicketPO productTicketPO, WorkReportInformationPushToErpDTO workReportInformationPushToErpDTO, ReportDefectiveRequestInDTO reportDefectiveRequestInDTO) {
        // 有多个地方使用到类似的。注意看其他地方要不要改，按照方法名取搜索
        Date produceDate = productTicketPO.getProduceDate();
        OoabTPO stopDate = ooabTService.getStopDate();
        if (stopDate != null) {
            String stopDateStr = stopDate.getOoab002();
            stopDateStr = stopDateStr.replace("/", "-");
            Date stopDateDate = DateUtil.parse(stopDateStr, DatePattern.NORM_DATE_PATTERN);
            // 比较日期
//            Date currentDate = DateUtil.parse(DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN), DatePattern.NORM_DATE_PATTERN);
            log.info("截止日期为：" + stopDateStr);
            log.info("生产日期为：" + produceDate);
            int result = DateUtil.compare(stopDateDate, produceDate);
            if (result >= 0) {
                log.info("截止日期在生产日期之后，日期加一天");
                // 截止日期在生产日期之后，日期加一天
                if (workReportInformationPushToErpDTO != null) {
                    workReportInformationPushToErpDTO.setCreateDate(DateUtil.offsetDay(stopDateDate, 1));
                    workReportInformationPushToErpDTO.setCompleteDate(DateUtil.offsetDay(stopDateDate, 1));
                }
                if (reportDefectiveRequestInDTO != null && CollectionUtil.isNotEmpty(reportDefectiveRequestInDTO.getReports())) {
                    reportDefectiveRequestInDTO.getReports().stream().forEach(report -> {
                        if (report.getFieldDTO() != null) {
                            report.getFieldDTO().setCreateDate(DateUtil.format(DateUtil.offsetDay(stopDateDate, 1),DatePattern.PURE_DATETIME_PATTERN));
                            report.getFieldDTO().setReportDatetimeE(DateUtil.format(DateUtil.offsetDay(stopDateDate, 1),"yyyy/MM/dd HH:mm:ss"));
                        }
                    });
                }
            } else {
                log.info("截止日期不在生产日期之后");
                if (workReportInformationPushToErpDTO != null) {
                    workReportInformationPushToErpDTO.setCreateDate(produceDate);
                    workReportInformationPushToErpDTO.setCompleteDate(produceDate);
                }
                if (reportDefectiveRequestInDTO != null && CollectionUtil.isNotEmpty(reportDefectiveRequestInDTO.getReports())) {
                    reportDefectiveRequestInDTO.getReports().stream().forEach(report -> {
                        if (report.getFieldDTO() != null) {
                            report.getFieldDTO().setCreateDate(DateUtil.format(produceDate,DatePattern.PURE_DATETIME_PATTERN));
                            report.getFieldDTO().setReportDatetimeE(DateUtil.format(produceDate,"yyyy/MM/dd HH:mm:ss"));
                        }
                    });
                }
            }
        }
    }

    private void verifyReportQuantity(ProductTicketPO productTicketPO, WorkReportInformationPushToErpDTO workReportInformationPushToErpDTO, ReportDefectiveRequestInDTO reportDefectiveRequestInDTO) {
        // 良品和不良品的数量 要小于等于 上道工序的汇入数量
        SfcbTVO sfcbTVO = sfcbTService.getByTickNoAndProcess(productTicketPO.getPlanTicketNo(), productTicketPO.getProcessCode());
        BigDecimal sumOfGoodProducts = sfcbTVO.getSfcb033();
        log.info("之前上报的良品数量：" + sumOfGoodProducts);
        if (workReportInformationPushToErpDTO != null
                && workReportInformationPushToErpDTO.getQuantity() != null
                && workReportInformationPushToErpDTO.getQuantity().compareTo(BigDecimal.ZERO) > 0
                && Integer.valueOf(BooleanEnum.FALSE.getCode()).equals(productTicketPO.getIsSignUp())
        ) {
            log.info("良品信息：" + JSON.toJSONString(workReportInformationPushToErpDTO));
            log.info("单位转换前参数：数量{},工序{}" ,workReportInformationPushToErpDTO.getQuantity(),workReportInformationPushToErpDTO.getUnit());
            ReportUnitConvertDTO reportUnitConvertDTO = new ReportUnitConvertDTO();
            reportUnitConvertDTO.setPlanTicketNo(productTicketPO.getPlanTicketNo());
            reportUnitConvertDTO.setProcessCode(productTicketPO.getProcessCode());
            reportUnitConvertDTO.setQuantity(workReportInformationPushToErpDTO.getQuantity());
            reportUnitConvertDTO.setSourceUnit(workReportInformationPushToErpDTO.getUnit());
            reportUnitConvertDTO.setTargetUnit(workReportInformationPushToErpDTO.getTicketUnit());
            BigDecimal goodProducts = reportUnitConvertService.execute(reportUnitConvertDTO);
            log.info("本工单良品数量：" + goodProducts);
            sumOfGoodProducts = sumOfGoodProducts.add(goodProducts == null ? BigDecimal.ZERO : goodProducts);
        }
        log.info("本工单良品总数量：" + sumOfGoodProducts);

        // 不良品
        BigDecimal sumOfDefectiveProducts = sfcbTVO.getSfcb036();
        BigDecimal defectiveProducts = BigDecimal.ZERO;
        log.info("之前上报的不良品数量：" + sumOfDefectiveProducts);
        List<String> processUnit = Lists.newArrayList();
        if (reportDefectiveRequestInDTO != null
                && CollectionUtil.isNotEmpty(reportDefectiveRequestInDTO.getReports())
                && Integer.valueOf(BooleanEnum.FALSE.getCode()).equals(productTicketPO.getIsDefectionSignUp())
        ) {
            log.info("不良品信息：" + JSON.toJSONString(reportDefectiveRequestInDTO));
            List<ReportDefectiveRequestInDTO.Report> reports = reportDefectiveRequestInDTO.getReports();
            reports.stream().forEach(report -> {
                List<ReportDefectiveRequestInDTO.ReportDetail> reportDetails = report.getReportDetails();
                reportDetails.stream().forEach(reportDetail -> {
                    if (reportDetail.getResOpNo().equals(productTicketPO.getProcessCode())) {
                        defectiveProducts.add(reportDetail.getResQty());
                        processUnit.add(reportDetail.getProcessUnit());
                    }
                });
            });
            if (defectiveProducts.compareTo(BigDecimal.ZERO) != 0 && processUnit.size() > 1) {
                log.info("单位转换前参数：数量{},工序{}" ,defectiveProducts,processUnit.get(0));
                ReportUnitConvertDTO reportUnitConvertDTO = new ReportUnitConvertDTO();
                reportUnitConvertDTO.setPlanTicketNo(productTicketPO.getPlanTicketNo());
                reportUnitConvertDTO.setProcessCode(productTicketPO.getProcessCode());
                reportUnitConvertDTO.setQuantity(defectiveProducts);
                reportUnitConvertDTO.setSourceUnit(processUnit.get(0));
                reportUnitConvertDTO.setTargetUnit(workReportInformationPushToErpDTO.getTicketUnit());
                BigDecimal defect = reportUnitConvertService.execute(reportUnitConvertDTO);
                log.info("本工单不良品数量：" + defect);
                sumOfDefectiveProducts = sumOfDefectiveProducts.add(defect == null ? BigDecimal.ZERO : defect);
            }
        }
        log.info("本工单不良品总数量：" + sumOfDefectiveProducts);


        // 数量校验
        if (sumOfGoodProducts.add(sumOfDefectiveProducts).compareTo(sfcbTVO.getSfcb028().multiply(new BigDecimal(105)).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP)) > 0) {
            // 收到工序
            if ("INIT".equals(sfcbTVO.getSfcb007())) {
                throw new CommonException(String.format("本工单已报工良品数量%s加上已报工不良品数量%s超过工单开单数量%s的105%%", sumOfGoodProducts,
                        sumOfDefectiveProducts, sfcbTVO.getSfcb028().multiply(new BigDecimal(105)).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP)));
            }
            List<CorporationGroupOutVO> corporationGroupOutVOS = iOocqlTService.getOocqlTByType("221", sfcbTVO.getSfcb007());// 221 工序
            if (CollectionUtil.isNotEmpty(corporationGroupOutVOS)) {
                throw new CommonException(String.format("本工单已报工良品数量%s加上已报工不良品数量%s超过上道工序的报工数量%s的105%%，请先上报上道工序%s", sumOfGoodProducts,
                        sumOfDefectiveProducts, sfcbTVO.getSfcb028().multiply(new BigDecimal(105)).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP),sfcbTVO.getSfcb007() + ":" + corporationGroupOutVOS.get(0).getOocql004()));
            } else {
                throw new CommonException(String.format("本工单已报工良品数量%s加上已报工不良品数量%s超过上道工序的报工数量%s的105%%，请先上报上道工序%s", sumOfGoodProducts,
                        sumOfDefectiveProducts, sfcbTVO.getSfcb028().multiply(new BigDecimal(105)).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP),sfcbTVO.getSfcb007()));
            }
        }
    }


    private ReportDefectiveRequestInDTO getReportDefectiveRequestInDTO(ProductTicketPO productTicketPO, ProductFinishOrderPO productFinishOrderPO) {
        ReportDefectiveRequestInDTO reportDefectiveRequestInDTO = new ReportDefectiveRequestInDTO();
        Snowflake snowflake = new Snowflake(1,1);
        reportDefectiveRequestInDTO.setKey(snowflake.nextIdStr());

        SfcbTVO sfcbTVO = sfcbTService.getByTickNoAndProcess(productTicketPO.getPlanTicketNo(), productTicketPO.getProcessCode());
        if (sfcbTVO == null) {
            throw new CommonException("报不良获取不到该sfcb_t工单制程单身档信息，请检查工序是否正确，工单：" + productTicketPO.getPlanTicketNo()
                    + "工序：" + productTicketPO.getProcessCode());
        }
        // 获得机台的工序，所属工厂信息
        ProductionMachineOutVO productionMachineOutVO = productionMachineService
                .getProductionMachineByErpName(productTicketPO.getMachineName(), productTicketPO.getProcess());
        ReportDefectiveRequestInDTO.DataKeyDTO dataKeyDTO = new ReportDefectiveRequestInDTO.DataKeyDTO();
        dataKeyDTO.setEntId(productionMachineOutVO.getFactoryCode());
        dataKeyDTO.setCompanyId(productionMachineOutVO.getEnterpriseNumbers());
        reportDefectiveRequestInDTO.setDataKey(dataKeyDTO);

        // 获得不良品数量
        List<ProductDefectiveProductsCallbackPO> callbackPOS =
                productDefectiveProductsCallbackService.getListByProductTicketId(productTicketPO.getId());
        if (CollectionUtil.isNotEmpty(callbackPOS)) {
            Map<String, List<ProductDefectiveProductsCallbackPO>> collect = callbackPOS.stream()
                    .filter(c -> DefectDetermineStatusEnum.HEAVY_PROCESSING.getCode().equals(c.getCheckResult())
                            || DefectDetermineStatusEnum.SCRAP_STORAGE.getCode().equals(c.getCheckResult()))
                    .collect(Collectors.groupingBy(ProductDefectiveProductsCallbackPO::getDefectiveProductsSource));
            List<ReportDefectiveRequestInDTO.Report> reports = Lists.newArrayList();
            AtomicInteger i = new AtomicInteger();
            collect.entrySet().stream().forEach(p->{
                if (p.getKey() == null) {
                    throw new CommonException("不良品来源为空");
                }
                BigDecimal defectiveProductsQuantity = p.getValue().stream()
                        .filter(c -> DefectDetermineStatusEnum.HEAVY_PROCESSING.getCode().equals(c.getCheckResult())
                                || DefectDetermineStatusEnum.SCRAP_STORAGE.getCode().equals(c.getCheckResult()))
                        .collect(CollectorsUtil.summingBigDecimal(ProductDefectiveProductsCallbackPO::getDefectiveProductsQuantity));
                log.info("不良品数量：" + defectiveProductsQuantity);

                ReportDefectiveRequestInDTO.Report report = new ReportDefectiveRequestInDTO.Report();
                // 基础信息
                ReportDefectiveRequestInDTO.FieldDTO fieldDTO = new ReportDefectiveRequestInDTO.FieldDTO();
                // 设置唯一值
                fieldDTO.setSourceNo("BLPBG-" + productTicketPO.getId() + "-" + i.get());
                i.addAndGet(1);
                fieldDTO.setDocTypeNo("S331");
                fieldDTO.setCreateDate(DateUtil.format(new Date(),DatePattern.PURE_DATETIME_PATTERN));
                fieldDTO.setWorkstationNo(productTicketPO.getProcessCode());
                fieldDTO.setFactoryNo(productionMachineOutVO.getEnterpriseNumbers());
                fieldDTO.setReporter(SecurityUtil.getWorkcode());
                fieldDTO.setShiftNo(ShiftNoEnum.getShiftNoEnum(productTicketPO.getShift()).getName());
                fieldDTO.setWoNo(productTicketPO.getPlanTicketNo());
                fieldDTO.setLaborNours(BigDecimal.ZERO);
                fieldDTO.setMachineHours(BigDecimal.ZERO);
                fieldDTO.setOpNo(productTicketPO.getProcessCode());
                SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(productTicketPO.getPlanTicketNo());
                if (ObjectUtil.isNotNull(sfaaTVO)) {
                    fieldDTO.setUnitNo(sfaaTVO.getSfaa013());
                }

                fieldDTO.setReportDatetimeE(DateUtil.format(new Date(),"yyyy/MM/dd HH:mm:ss"));
                fieldDTO.setMachineNo(productionMachineOutVO.getMachineNumber());
                report.setFieldDTO(fieldDTO);
                // 物料列表
                List<ReportDefectiveRequestInDTO.ReportDetail> reportDetails = Lists.newArrayList();

                // 不良品总数要大于 0
                BigDecimal totalDefectiveProductsQuantity = p.getValue().stream().collect(CollectorsUtil.summingBigDecimal(ProductDefectiveProductsCallbackPO::getDefectiveProductsQuantity));
                if (totalDefectiveProductsQuantity != null && totalDefectiveProductsQuantity.compareTo(BigDecimal.ZERO) < 0) {
                    throw new CommonException("工序" + productTicketPO.getProcessCode() + "不良品总数：" + totalDefectiveProductsQuantity + "不能小于0");
                }

                if (totalDefectiveProductsQuantity != null && totalDefectiveProductsQuantity.compareTo(BigDecimal.ZERO) > 0) {
                    p.getValue().forEach(productDefectiveProductsCallbackPO -> {
                        ReportDefectiveRequestInDTO.ReportDetail reportDetail = new ReportDefectiveRequestInDTO.ReportDetail();
                        reportDetail.setResOpNo(p.getKey());
                        reportDetail.setResQty(productDefectiveProductsCallbackPO.getDefectiveProductsQuantity());
                        reportDetail.setResErrorno(productDefectiveProductsCallbackPO.getDefectiveProductsReason());
                        reportDetail.setResErrorname(productDefectiveProductsCallbackPO.getDefectiveProductsReasonName());
                        reportDetail.setProcessUnit(productDefectiveProductsCallbackPO.getUnit());
                        if (ObjectUtil.isNotNull(sfcbTVO)) {
                            reportDetail.setMoldNum(sfcbTVO.getSfcbud016());
                        }
                        reportDetail.setResWeight(BigDecimal.ZERO);
                        reportDetails.add(reportDetail);
                    });
                    report.setReportDetails(reportDetails);
                    reports.add(report);
                }
            });
            reportDefectiveRequestInDTO.setReports(reports);
        }
        return reportDefectiveRequestInDTO;
    }

    @Override
    public List<UnfinishedOrderListOutVO> unfinishedOrderList(UnfinishedOrderListInVO inVO) {
        LambdaQueryWrapper<ProductTicketPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProductTicketPO::getMachineName,inVO.getMachineName())
                .lt(ProductTicketPO::getStatus,TicketStatusEnum.COMPLETED.getCode());
        List<ProductTicketPO> list = list(wrapper);
        return BeanUtil.copyToList(list,UnfinishedOrderListOutVO.class);
    }

    @Override
    public ProductionTicketInfoOutVO getProductingTicketInfo(Long id) {
        return getProductionTicketInfoOutVO(id, Integer.valueOf(TicketStatusEnum.IN_PROGRESS.getCode()));
    }

    @Override
    public String getPalnTicketNo(String machineName) {
        MachineConfigInVO machineConfigInVO = MachineConfigInVO.builder()
                .machineName(machineName)
                .build();
        Integer billingType = productionMachineConfigService.getBillingType(machineConfigInVO);
        if (ProductConfigBillingTypeEnum.ONE_DAY.getIntCode().equals(billingType)) {
            return null;
        }
        // 查询机台和工程单的关系
        ProductMachineTicketPO productMachineTicketPO =
                productMachineTicketService.getByMachineName(machineName);
        if (productMachineTicketPO == null) {
            return null;
        }
        return productMachineTicketPO.getPlanTicketNo();
    }

    @Override
    public List<String> getPalnTicketNoList(String planTicketNo) {
        List<SfaaTVO> listByProductTicket = sfaaTService.getListByProductTicket(planTicketNo);
        if (CollectionUtil.isNotEmpty(listByProductTicket)) {
            return listByProductTicket.stream().map(SfaaTVO::getSfaadocno).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<GetProductTicketInfoListOutVO> getProductTicketInfoList(String machineName, String produceDate, Integer shift) {
        return baseMapper.getProductTicketInfoList(machineName,produceDate,shift);
    }

    @Override
    public Page<GetProductReportByFactoryOutVO>
        getReportByFactory(GetProductReportByFactoryInVO getProductReportByFactoryInVO) {
        return baseMapper.getReportByFactory(getProductReportByFactoryInVO.getPage(), getProductReportByFactoryInVO);
    }

    @Override
    public String saveChangeInfo(ProductPlanTicketVO productPlanTicketVO) {
//        ProductTicketPO productTicketPO = productTicketService.verify(productTicketVO.getProductTicketId());
        // 校验
        productFinishOrderService.verifyStaging(productPlanTicketVO.getMachineName());

        return productPlanTicketVO.getPlanTicketNo();
    }

    private List<ProductChangeProdutionOutVO> getProductChangeProdutionOutVOS(String planTicketNo, String machineName,
        Integer ticketType) {
        // 获得机台对应的工厂
        List<ProductionPlanPO> productionPlanPOS = productionPlanService.getListByNameAndTicketNo(machineName, planTicketNo);
        if (CollectionUtil.isEmpty(productionPlanPOS)) {
            return null;
        }
        ProductionPlanPO productionPlanPO = productionPlanPOS.get(0);
        ProductionMachineOutVO productionMachineOutVO = productionMachineService.getProductionMachineByErpName(
            productionPlanPO.getErpMachineName(), productionPlanPO.getProductionProcess());

        LambdaQueryWrapper<ProductTicketPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductTicketPO::getPlanTicketNo, planTicketNo);
        queryWrapper.eq(ProductTicketPO::getCompanyCode, productionMachineOutVO.getEnterpriseNumbers());
        queryWrapper.eq(ProductTicketPO::getMachineName, machineName);
        queryWrapper.eq(ProductTicketPO::getTicketType, ticketType);
        queryWrapper.eq(ProductTicketPO::getStatus, TicketStatusEnum.IN_PROGRESS.getCode());
        queryWrapper.orderByDesc(ProductTicketPO::getCreateTime);
        List<ProductTicketPO> productTicketPOS = list(queryWrapper);
        List<ProductChangeProdutionOutVO> productChangeProdutionOutVOS =
            BeanUtil.copyToList(productTicketPOS, ProductChangeProdutionOutVO.class);
        for (ProductChangeProdutionOutVO productChangeProdutionOutVO:productChangeProdutionOutVOS){
            productChangeProdutionOutVO.setStatusName(TicketStatusEnum.getTicketStatusEnum(Integer.valueOf(productChangeProdutionOutVO.getStatus())).getCnName());
            productChangeProdutionOutVO.setCreaterName(ia01Service.getUserInfoById(productChangeProdutionOutVO.getCreateBy()).getName());
        }
        return productChangeProdutionOutVOS;
    }

    private WorkReportInformationPushToErpDTO getWorkReportInformationPushToErpDTO(ProductTicketPO productTicketPO,
                                                                                   ProductFinishOrderPO productFinishOrderPO) {
        WorkReportInformationPushToErpDTO workReportInformationPushToErpDTO = new WorkReportInformationPushToErpDTO();

        // 设置唯一值
        workReportInformationPushToErpDTO.setSourceNo("LPBG-" + productTicketPO.getId());

        // 获得机台的工序，所属工厂信息
        ProductionMachineOutVO productionMachineOutVO = productionMachineService
            .getProductionMachineByErpName(productTicketPO.getMachineName(), productTicketPO.getProcess());
        workReportInformationPushToErpDTO.setEntId(productionMachineOutVO.getFactoryCode());
        workReportInformationPushToErpDTO.setFactoryNo(productionMachineOutVO.getEnterpriseNumbers());
        workReportInformationPushToErpDTO.setWorkerOrderNo(productTicketPO.getPlanTicketNo());
        workReportInformationPushToErpDTO.setProcessNo(productionMachineOutVO.getProcessCode());
        // 报工数量
        List<ProductMachineTaskPO> productMachineTaskPOS = productMachineTaskService.getListByProductId(productTicketPO.getId());
        if (CollectionUtil.isNotEmpty(productMachineTaskPOS)) {
            workReportInformationPushToErpDTO.setQuantity(
                productMachineTaskPOS.stream().map(ProductMachineTaskPO::getReportedQuantity)
                    .filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            Long hoursDifference = getTimes(productMachineTaskPOS);
            workReportInformationPushToErpDTO.setMachineHours(BigDecimal.valueOf(hoursDifference));
        }
        // 生产状态的数据
        List<ProductMachineTaskPO> productTaskPOS = productMachineTaskService
                .getListByProductId(productTicketPO.getId(), MachineTaskTypeEnum.PRODUCTION.getCode());
        if (CollectionUtil.isNotEmpty(productTaskPOS)) {
            Long productTaskhours = getTimes(productTaskPOS);
            workReportInformationPushToErpDTO
                    .setLaborHours(BigDecimal.valueOf(productTaskhours));
        }

        workReportInformationPushToErpDTO.setUnit(productFinishOrderPO.getUnit());
        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(productTicketPO.getPlanTicketNo());
        SfcbTVO sfcbTVO = sfcbTService.getByTickNoAndProcess(productTicketPO.getPlanTicketNo(), productionMachineOutVO.getProcessCode());
        if (ObjectUtil.isNotNull(sfaaTVO)) {
            workReportInformationPushToErpDTO.setTicketUnit(sfaaTVO.getSfaa013());
            workReportInformationPushToErpDTO.setMoldNum(sfcbTVO.getSfcbud016());
        }

        // 获得人的工号
        workReportInformationPushToErpDTO.setReporter(SecurityUtil.getWorkcode());
        workReportInformationPushToErpDTO.setEquipmentNo(productionMachineOutVO.getMachineNumber());
      //  workReportInformationPushToErpDTO.setShiftNo(ShiftNoEnum.getShiftNoEnum(productTicketPO.getShift()).getName());
        workReportInformationPushToErpDTO.setShiftNo(productTicketPO.getReportShift()!=null?ReportShiftNoEnum.getReportShiftNoEnum(productTicketPO.getReportShift()).getName():ShiftNoEnum.getShiftNoEnum(productTicketPO.getShift()).getName());
        workReportInformationPushToErpDTO.setReturnType("0");
        workReportInformationPushToErpDTO.setPrice(BigDecimal.ZERO);
        workReportInformationPushToErpDTO.setWages(BigDecimal.ZERO);

        workReportInformationPushToErpDTO.setGoodQuantity(BigDecimal.ZERO);
        workReportInformationPushToErpDTO.setScrapQuantity(BigDecimal.ZERO);
        workReportInformationPushToErpDTO.setGoodSheets(BigDecimal.ZERO);
        workReportInformationPushToErpDTO.setScrapSheets(BigDecimal.ZERO);
        workReportInformationPushToErpDTO.setGoodMetre(BigDecimal.ZERO);
        workReportInformationPushToErpDTO.setScrapMetre(BigDecimal.ZERO);
        workReportInformationPushToErpDTO.setWageNum(BigDecimal.ZERO);

//        workReportInformationPushToErpDTO.setSourceNo(productTicketPO.getTicketRequestId());

        workReportInformationPushToErpDTO.setType(StringUtils.isNotBlank(productTicketPO.getPieceType())?productTicketPO.getPieceType():"ALL");
        workReportInformationPushToErpDTO.setDocTypeNo("S331");

        // 包含中文，说明是新的，包含角色的信息
        if (chinesePattern.matcher(productTicketPO.getTeamUsers()).find()) {
            List<ProductShiftOutVO> productShifts = productShiftService.getProductShift(Arrays.asList(productTicketPO.getId()));
            List<Long> userIds = productShifts.stream().map(productShiftOutVO -> Long.valueOf(productShiftOutVO.getTeamUsers()))
                    .collect(Collectors.toList());
            Map<Long, String> workCodeMap = ia01Service.getUserInfoByIds(userIds).stream().collect(Collectors.toMap(UserDTO::getId, UserDTO::getWorkcode));
            List<Map<String, String>> teamList = productShifts.stream().map(productShiftOutVO -> {
                Map<String, String> teamRoleMap = Maps.newHashMap();
                teamRoleMap.put("workcode", workCodeMap.get(Long.valueOf(productShiftOutVO.getTeamUsers())));
                teamRoleMap.put("roleCode", productShiftOutVO.getRoleCode());
                return teamRoleMap;
            }).collect(Collectors.toList());
            workReportInformationPushToErpDTO.setTeamList(teamList);
        } else {
            // 兼容旧的数据，后续没有这些数据，过段时间就可以删除
            List<Long> userIds = Arrays.stream(productTicketPO.getTeamUsers().split(",")).map(user -> Long.valueOf(user))
                    .collect(Collectors.toList());
            List<Map<String, String>> teamList = ia01Service.getUserInfoByIds(userIds).stream().map(userDTO -> {
                Map<String, String> teamRoleMap = Maps.newHashMap();
                teamRoleMap.put("workcode", userDTO.getWorkcode());
                teamRoleMap.put("roleCode", "02"); // 默认为02 大助
                return teamRoleMap;
            }).collect(Collectors.toList());
            workReportInformationPushToErpDTO.setTeamList(teamList);
        }

        // 换版报工
        List<ProductMachineTaskPO> taskPOS = productMachineTaskService.getListByProductId(productTicketPO.getId(), MachineTaskTypeEnum.TRANSFER_OF_ORDER.getCode());
        if (CollectionUtil.isNotEmpty(taskPOS)) {
            ProductMachineTaskPO productMachineTaskPO = taskPOS.get(0);
            if (productMachineTaskPO != null) {
                workReportInformationPushToErpDTO.setGoodbb(productMachineTaskPO.getChangeVersionNewOld());
                workReportInformationPushToErpDTO.setGoodnum(String.valueOf(productMachineTaskPO.getChangeVersionQuantity()));
                workReportInformationPushToErpDTO.setGoodlx(productMachineTaskPO.getChangeVersionType());
            }
        }

        return workReportInformationPushToErpDTO;
    }

    private Long getTimes(List<ProductMachineTaskPO> productTaskPOS) {
        // 获取最早开始时间
        Optional<java.util.Date> startTime = productTaskPOS.stream()
                .map(ProductMachineTaskPO::getStartTime)
                .filter(Objects::nonNull)
                .min(Comparator.naturalOrder());
        // 获取最晚结束时间
        Optional<java.util.Date> endTime = productTaskPOS.stream()
                .map(ProductMachineTaskPO::getEndTime)
                .filter(Objects::nonNull)
                .max(Comparator.naturalOrder());
        // 时长
        return getHoursDifference(
                startTime.orElse(new Date()),
                endTime.orElse(new Date()));
    }

    /**
     * 两个日期之间的小时数
     * @param startDate
     * @param endDate
     * @return
     */
    private long getHoursDifference(java.util.Date startDate, java.util.Date endDate) {
        if (startDate != null && endDate != null) {
            // 将Date对象转换为Calendar对象
            Calendar calendar1 = Calendar.getInstance();
            calendar1.setTime(startDate);

            Calendar calendar2 = Calendar.getInstance();
            calendar2.setTime(endDate);

            // 获取两个Calendar对象之间的小时数差异
            long hoursDifference = calendar2.getTimeInMillis() - calendar1.getTimeInMillis();
            hoursDifference = hoursDifference / (60 * 60 * 1000); // 转换为小时

            log.info("两个日期的小时数差异: " + hoursDifference + " 小时");
            return hoursDifference;
        } else {
            return 0;
        }

    }

    @Override
    public List<GetProduceDetailsOutVO> getProduceDetailsCalibreReview(String planTicketNo) {
        return baseMapper.getProduceDetailsCalibreReview(planTicketNo);
    }

    @Override
    public List<GetMaterialDetailsOutVO> getMaterialDetailsCalibreReview(String planTicketNo) {
        return baseMapper.getMaterialDetailsCalibreReview(planTicketNo);
    }

    @Override
    public Page<GetMaterialUseDetailsOutVO> getMaterialUseDetails(IPage page, GetMaterialUseDetailsInVO getMaterialUseDetailsInVO) {
        return baseMapper.getMaterialUseDetails(page,getMaterialUseDetailsInVO);
    }

    @Override
    public List<GetMachineGroupReportOutVO> getMachineGroupReport(GetMachineGroupReportInVO getMachineGroupReportInVO) {
        return baseMapper.getMachineGroupReport(getMachineGroupReportInVO,SecurityUtil.getCompanySite());
    }

    @Override
    public GetErpTicketInfoOutVO getTicketInfo(String planTicketNo) {
        GetErpTicketInfoOutVO getErpTicketInfoOutVO = new GetErpTicketInfoOutVO();
        // 获得工程单
        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(planTicketNo);
        if (sfaaTVO == null) {
            throw new CommonException("工程单不存在");
        }
        getErpTicketInfoOutVO.setPlanTicketNo(sfaaTVO.getSfaadocno());
        getErpTicketInfoOutVO.setProductNo(sfaaTVO.getSfaa010());
        if(sfaaTVO != null) {
            XmamTVO xmamTVO = xmamTService.getXmamTByProductionNo(sfaaTVO.getSfaa010(), SecurityUtil.getCompanySite());
            if (xmamTVO != null) {
                getErpTicketInfoOutVO.setBoxSpece(xmamTVO.getXmam008());
            }
        }
        ImaalTVO imaalTVO = iImaalTService.getImaalTByProductNo(sfaaTVO.getSfaa010());
        if (imaalTVO != null) {
            getErpTicketInfoOutVO.setProductName(imaalTVO.getImaal003());
        }

        return getErpTicketInfoOutVO;
    }

    @Override
    public Page<GetProductReportByFactoryOutVO> getProductPlanReport(GetProductReportByFactoryInVO getProductReportByFactoryInVO) {
        Page<GetProductReportByFactoryOutVO> productPlanReport = baseMapper.getProductPlanReport(getProductReportByFactoryInVO.getPage(), getProductReportByFactoryInVO);
        return productPlanReport;
    }

    @Override
    public List<GetValuationTypeOutVO> getValuationType(GetValuationTypeInVO getValuationTypeInVO) {
        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(getValuationTypeInVO.getPlanTicketNo());
        if (sfaaTVO == null) {
            throw new CommonException("工程单不存在");
        }

        ProductionMachineOutVO productionMachineOutVO = productionMachineService
            .getProductionMachineByErpName(getValuationTypeInVO.getMachineName(), getValuationTypeInVO.getProcess());
        if (productionMachineOutVO == null) {
            throw new CommonException("查无此设备，请在机台管理中维护，机台名称：" + getValuationTypeInVO.getMachineName() + "工序：" + getValuationTypeInVO.getProcess());
        }

        EcbnucDTO ecbnucDTO = new EcbnucDTO();
        ecbnucDTO.setMaterialCode(sfaaTVO.getSfaa010());
        ecbnucDTO.setMachineCode(productionMachineOutVO.getMachineNumber());
        ecbnucDTO.setProcess(productionMachineOutVO.getProcessCode());
        List<EcbnucTPO> ecbnucTList = ecbnucTService.getEcbnucTList(ecbnucDTO);
        if (CollectionUtil.isNotEmpty(ecbnucTList)) {
            return ecbnucTList.stream().map(ecbnucTPO -> {
                GetValuationTypeOutVO getValuationTypeOutVO = new GetValuationTypeOutVO();
                getValuationTypeOutVO.setPieceType(ecbnucTPO.getEcbnuc004());
                return getValuationTypeOutVO;
            }).distinct().collect(Collectors.toList());
        }
        GetValuationTypeOutVO getValuationTypeOutVO = new GetValuationTypeOutVO();
        getValuationTypeOutVO.setPieceType("ALL");
        // 查不到就给默认ALL
        return Collections.singletonList(getValuationTypeOutVO);
    }

    @Override
    public GetErpTicketInfoOutVO getTicketDetailInfo(String planTicketNo) {
        GetErpTicketInfoOutVO getErpTicketInfoOutVO = productTicketService.getTicketInfo(planTicketNo);

        MaterialsInfoDTO materialsInfoDTO = materialsInfoService.getByItemNo(getErpTicketInfoOutVO.getProductNo());
        if (materialsInfoDTO != null) {
            getErpTicketInfoOutVO.setSkuCode(materialsInfoDTO.getImaaua017());
            getErpTicketInfoOutVO.setSkuName(materialsInfoDTO.getImaaua018());
        }

        IBoxCodeService boxCodeService = boxCodeHandlerFactory.getBoxCodeService(planTicketNo);
        if (boxCodeService != null) {
            getErpTicketInfoOutVO.setCustomerNo(boxCodeService.getCompanyCode());
        }
        return getErpTicketInfoOutVO;
    }

    @Override
    public Boolean updatePieceType(Long productTicketId,String pieceType) {
        ProductTicketPO productTicketPO = getById(productTicketId);
        productTicketPO.setPieceType(pieceType);
        return updateById(productTicketPO);
    }

    @Override
    public GetLastTaskInfoOutVO getLastTaskInfo(GetLastTaskInfoInVO vo) {
        LambdaQueryWrapper<ProductTicketPO> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(ProductTicketPO::getMachineName, vo.getMachineName())
            .eq(ProductTicketPO::getShift, vo.getShift())
            .eq(ProductTicketPO::getCompanyCode, SecurityUtil.getCompanySite()).orderByDesc(ProductTicketPO::getId)
            .last("limit 1");
        ProductTicketPO productTicketPO = getOne(lambdaQueryWrapper);
        if (ObjectUtil.isNull(productTicketPO)) {
            log.error("未找到上一个任务信息");
            return null;
        }

        GetLastTaskInfoOutVO getLastTaskInfoOutVO = new GetLastTaskInfoOutVO();
        getLastTaskInfoOutVO.setMachineName(productTicketPO.getMachineName());
        getLastTaskInfoOutVO.setProduceDate(productTicketPO.getProduceDate());
        getLastTaskInfoOutVO.setShift(productTicketPO.getShift());
        List<ProductShiftOutVO> productShift = productShiftService.getProductShift(Arrays.asList(productTicketPO.getId()));

        List<UserDTO> userDTOS = ia01Service.getUserInfoByIds(productShift.stream().map(productShiftOutVO -> Long.valueOf(productShiftOutVO.getTeamUsers())).collect(Collectors.toList()));
        Map<Long, String> userMap = userDTOS.stream().collect(Collectors.toMap(UserDTO::getId, UserDTO::getName, (k1, k2) -> k1));
        List<GetDefaultRoleListsOutVO> defaultRoleLists = Lists.newArrayList();
        for (ProductShiftOutVO productShiftOutVO : productShift) {
            GetDefaultRoleListsOutVO getDefaultRoleListsOutVO = new GetDefaultRoleListsOutVO();
            getDefaultRoleListsOutVO.setUserId(Long.valueOf(productShiftOutVO.getTeamUsers()));
            getDefaultRoleListsOutVO.setUserName(userMap.get(Long.valueOf(productShiftOutVO.getTeamUsers())));
            getDefaultRoleListsOutVO.setRoleCode(productShiftOutVO.getRoleCode());
            getDefaultRoleListsOutVO.setRoleName(productShiftOutVO.getRoleName());
            defaultRoleLists.add(getDefaultRoleListsOutVO);
        }
        getLastTaskInfoOutVO.setRoleLists(defaultRoleLists);

        return getLastTaskInfoOutVO;
    }

    @Override
    public List<GetOneDayTaskSummaryOutVO> getOneDayTaskSummary(String machineName, Date produceDate, Integer shift) {
        return baseMapper.getOneDayTaskSummary(machineName, produceDate, shift,SecurityUtil.getCompanySite());
    }

    @Override
    public List<GetOneDayTaskSummaryOutVO> getOneDayTaskSummaryDetail(String machineName, Date produceDate, Integer shift) {
        return baseMapper.getOneDayTaskSummaryDetail(machineName, produceDate, shift,SecurityUtil.getCompanySite());
    }

    @Override
    public List<GetReportShiftNoOutVO> getReportShit(Long productTicketId) {
        ProductTicketPO productTicketPO = getById(productTicketId);
        boolean existReportShit = existReportShit(productTicketPO);
        if(existReportShit) {
            if (productTicketPO.getReportShift() == null) {
                return ReportShiftNoEnum.getReportShiftListEnumByParentCode(productTicketPO.getShift()).stream().map(t->{
                    GetReportShiftNoOutVO outVO = new GetReportShiftNoOutVO();
                    outVO.setCode(t.getCode());
                    outVO.setDescription(t.getDescription());
                    return outVO;
                }).collect(Collectors.toList());
            } else {
                ReportShiftNoEnum reportShiftNoEnum = ReportShiftNoEnum.getReportShiftNoEnum(productTicketPO.getReportShift());
                GetReportShiftNoOutVO outVO = new GetReportShiftNoOutVO();
                outVO.setCode(reportShiftNoEnum.getCode());
                outVO.setDescription(reportShiftNoEnum.getDescription());
                return Collections.singletonList(outVO);
            }
        }
        return null;
    }


    @Override
    public List<ProductTicketPO> getListByMachineNameAndProduceDate(String machineName, String processName, int year, int month) {
        // 使用 year 和 month 函数提取 create_time 的年份和月份
        LambdaQueryWrapper<ProductTicketPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProductTicketPO::getCompanyCode, SecurityUtil.getCompanySite())
                .like(StringUtil.isNotBlank(machineName),ProductTicketPO::getMachineName,machineName)
                .like(StringUtil.isNotBlank(processName),ProductTicketPO::getProcess,processName)
                .apply("YEAR(produce_date) = {0}", year)  // 匹配年份
                .apply("MONTH(produce_date) = {0}", month); // 匹配月份

        return list(wrapper);
    }

    @Override
    public List<DeviceOEEReportDTO> deviceOEEReportShow(String companyCode,String machineName, String processName, String produceYearMonth) {
        return baseMapper.deviceOEEReportShow(companyCode,machineName,processName,produceYearMonth);
    }

    /**
     *  是否存在报工班次
     * @param productTicketPO
     * @return: boolean
     * <AUTHOR>
     * @date: 2024-12-25 17:00
     */
    private boolean existReportShit(ProductTicketPO productTicketPO){
        if(!"SITE-16".equals(productTicketPO.getCompanyCode())){
            return false;
        }
        List<String> containProcessList = Lists.newArrayList("成型(外贴)","成型(内杯)","成型(纸套)","成型(铁圈)");
        return containProcessList.contains(productTicketPO.getProcess());
    }

    public static void main(String[] args) {
//        String stopDateStr = "2024-06-30";
//        Date stopDateDate = DateUtil.parse(stopDateStr, DatePattern.NORM_DATE_PATTERN);
//        // 比较日期
//        log.info("截止日期为：" + stopDateStr);
//        Date currentDate = DateUtil.parse(DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN), DatePattern.NORM_DATE_PATTERN);
//        log.info("截止日期为：" + stopDateStr);
//        log.info("当前日期为：" + currentDate);
//        int result = DateUtil.compare(stopDateDate, currentDate);
//        if (result >= 0) {
//            log.info(DateUtil.format(DateUtil.offsetDay(stopDateDate, 1),DatePattern.PURE_DATETIME_PATTERN));
//            log.info(DateUtil.format(DateUtil.offsetDay(stopDateDate, 1),"yyyy/MM/dd HH:mm:ss"));
//            System.out.println("截止日期已过");
//        }
        BigDecimal bigDecimal = new BigDecimal(105);
        String s = new BigDecimal("10").multiply(bigDecimal).divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP).toPlainString();
        System.out.println(String.format("本工单已报工良品数量%s加上已报工不良品数量%s超过工单开单数量%s的105%%,", 1000,
                10000, s));
    }

    @Override
    public List<ProductionPlanPO> getTileWireProductPlanList(GetTileWireProductPlanListInVO inVO) {
        return productionPlanService.getListByMachineTicket(inVO.getErpMachineName(), inVO.getProductionPlanDate(), inVO.getSerialNo(), null);
    }

    @Override
    public Boolean tileWirePostOrder(List<Long> productionPlanIdList) {
        AssertUtil.isNotEmpty(productionPlanIdList,"选择的计划不能为空");
        if(SiteEnum.HSXC.getCode().equals(SecurityUtil.getCompanySite())) {
            // 呼市物流 需要同步订单计划给瓦线
            List<ProductionPlanPO> productionPlanPOS = productionPlanService.getListByIds(productionPlanIdList);
            log.info("tileWirePostOrder  {}",JSON.toJSONString(productionPlanPOS));
            iLogisticsService.postOrder(productionPlanPOS);
        }
        return true;
    }

    @Override
    public List<GetProcessQuantityByPlanTicketNo> getProcessQuantityByPlanTicketNo(String planTicketNo) {
        return baseMapper.getProcessQuantityByPlanTicketNo(planTicketNo,SecurityUtil.getCompanySite());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cancelGoodsReport(Long id) {
        ProductTicketPO productTicketPO = getById(id);
        if (Integer.valueOf(BooleanEnum.FALSE.getCode()).equals(productTicketPO.getIsSignUp())) {
            throw new CommonException("工单良品未报工，无法撤销");
        }
        List<ProductInterfaceRecordsPO> productInterfaceRecordsPOS = productInterfaceRecordsService.getByProductTicketId(id);
        Map<Integer, String> map = productInterfaceRecordsPOS.stream().sorted(Comparator.comparing(ProductInterfaceRecordsPO::getCreateTime).reversed())
                .collect(Collectors.toMap(ProductInterfaceRecordsPO::getBusinessType,
                        ProductInterfaceRecordsPO::getResponse, (v1, v2) -> v1));

        String goodReportDoNo = map.get(InterfaceBusinessEnum.GOOD_REPORT.getCode());
        if (StringUtils.isBlank(goodReportDoNo)) {
            throw new CommonException("未查询到良品报工记录，请联系管理人员");
        }

        productTicketPO.setIsSignUp(Integer.valueOf(BooleanEnum.FALSE.getCode()));
        updateById(productTicketPO);

        // 更新erp信息
        staffingCostsToErpService.changeErpOrderStatus(goodReportDoNo, ErpOrderTypeEnum.REPORT.getErpOrderType(), ALL_BILL_DELETE);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean cancelBadGoodsReport(Long id) {
        ProductTicketPO productTicketPO = getById(id);
        if (Integer.valueOf(BooleanEnum.FALSE.getCode()).equals(productTicketPO.getIsDefectionSignUp())) {
            throw new CommonException("工单不良品未报工，无法撤销");
        }
        List<ProductInterfaceRecordsPO> productInterfaceRecordsPOS = productInterfaceRecordsService.getByProductTicketId(id);
        Map<Integer, String> map = productInterfaceRecordsPOS.stream().sorted(Comparator.comparing(ProductInterfaceRecordsPO::getCreateTime).reversed())
                .collect(Collectors.toMap(ProductInterfaceRecordsPO::getBusinessType,
                        ProductInterfaceRecordsPO::getResponse, (v1, v2) -> v1));

        String defectionReportDoNo = map.get(InterfaceBusinessEnum.DEFECTION_REPORT.getCode());
        if (StringUtils.isBlank(defectionReportDoNo)) {
            throw new CommonException("未查询到不良品报工记录，请联系管理人员");
        }

        productTicketPO.setIsDefectionSignUp(Integer.valueOf(BooleanEnum.FALSE.getCode()));
        updateById(productTicketPO);

        // 更新erp信息
        staffingCostsToErpService.changeErpOrderStatus(defectionReportDoNo, ErpOrderTypeEnum.REPORT.getErpOrderType(), ALL_BILL_DELETE);
        return true;
    }

    @GlobalTransactional(rollbackFor = Exception.class,timeoutMills = 300000)
    @Override
    public Boolean cancelMaterialReport(Long id) {
        ProductTicketPO productTicketPO = getById(id);
        if (Integer.valueOf(BooleanEnum.FALSE.getCode()).equals(productTicketPO.getIsPourMaterial())) {
            throw new CommonException("工单未倒扣料，无法撤销");
        }
        List<ProductMachineMaterialApportionmentPO> productMachineMaterialApportionmentPOS = productMachineMaterialApportionmentService.getByProductTicketId(id);
        if (CollectionUtils.isEmpty(productMachineMaterialApportionmentPOS)) {
            throw new CommonException("工单查询到扣料信息，无法撤销");
        }
        List<ProductMachineMaterialApportionmentPO> pos = productMachineMaterialApportionmentPOS.stream().filter(productMachineMaterialApportionmentPO -> {
            //  10 正扣料  20 分摊  30 倒扣料
            if (Integer.valueOf(30).equals(productMachineMaterialApportionmentPO.getUseType())) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pos)) {
            throw new CommonException("工单查询到倒扣料信息，无法撤销");
        }
        ProductMachineMaterialApportionmentPO productMachineMaterialApportionmentPO = pos.get(0);
        String deductionNo = productMachineMaterialApportionmentPO.getDeductionNo();
        if (StringUtils.isBlank(deductionNo)) {
            throw new CommonException("未查询到倒扣料记录，请联系管理人员");
        }

        productTicketPO.setIsDeductMaterial(Integer.valueOf(BooleanEnum.FALSE.getCode()));
        updateById(productTicketPO);

        // 去wms撤销物料变更
        ISrmBarcodeStoreService srmBarcodeStoreService = SpringUtil.getBean(ISrmBarcodeStoreService.class);
        List<RevokeBarcodeStoreDTO> revokeBarcodeStoreDTOs = Lists.newArrayList();
        // 封装revokeBarcodeStoreDTOs
        pos.forEach(po -> {
            RevokeBarcodeStoreDTO revokeBarcodeStoreDTO = new RevokeBarcodeStoreDTO();
            revokeBarcodeStoreDTO.setDocNo(deductionNo);
            revokeBarcodeStoreDTO.setSourceNo(po.getDeductionNo());
            revokeBarcodeStoreDTO.setBarcodeNo(po.getMaterialBarcodeNo());
            revokeBarcodeStoreDTO.setConsumptionQuantity(po.getConsumptionQuantity());
            revokeBarcodeStoreDTO.setChagType(-1);
            revokeBarcodeStoreDTOs.add(revokeBarcodeStoreDTO);
        });
        srmBarcodeStoreService.revokeBarcodeStores(revokeBarcodeStoreDTOs);

        // 更新erp信息
        staffingCostsToErpService.changeErpOrderStatus(deductionNo, ErpOrderTypeEnum.DEDUCTION.getErpOrderType(), ALL_BILL_DELETE);
        return true;
    }

    @Override
    public String checkProcessNum(CheckProcessNumInVO checkProcessNumInVO) {
        ProductTicketPO productTicketPO = getById(checkProcessNumInVO.getProductTicketId());

        // 汇总这个工程单 这个机台的 已报工数量
        BigDecimal produceTotalQuantity = BigDecimal.ZERO;
        List<Long> ids =
                getListByPlanTicketNoAndProcess(productTicketPO.getPlanTicketNo(),productTicketPO.getProcessCode()).stream().map(ProductTicketPO::getId).collect(Collectors.toList());
        List<ProductMachineTaskPO> productMachineTasks = productMachineTaskService.getByProductTicketIds(ids);
        if (CollectionUtil.isNotEmpty(productMachineTasks)) {
            produceTotalQuantity = productMachineTasks.stream().map(ProductMachineTaskPO::getReportedQuantity)
                    .filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        // 单位转换后的数量
        ReportUnitConvertDTO reportUnitConvertDTO = new ReportUnitConvertDTO();
        reportUnitConvertDTO.setPlanTicketNo(productTicketPO.getPlanTicketNo());
        reportUnitConvertDTO.setProcessCode(productTicketPO.getProcessCode());
        reportUnitConvertDTO.setQuantity(produceTotalQuantity);
        reportUnitConvertDTO.setSourceUnit(productTicketPO.getUnit());

        SfaaTVO sfaaTVO = sfaaTService.getInfoByTicket(productTicketPO.getPlanTicketNo());
        reportUnitConvertDTO.setTargetUnit(sfaaTVO.getSfaa013());
        BigDecimal goodProducts = reportUnitConvertService.execute(reportUnitConvertDTO);

        // erp工序开单数量
        SfcbTVO sfcbTVO = sfcbTService.getByTickNoAndProcess(productTicketPO.getPlanTicketNo(), productTicketPO.getProcessCode());
        if (sfcbTVO == null) {
            throw new CommonException("工单" + productTicketPO.getPlanTicketNo() + "不存在工序" + productTicketPO.getProcessCode() + ",请联系管理员");
        }
        BigDecimal billingPcsQuantity = sfcbTVO.getSfcbud013();

        // 比较
        if (goodProducts.compareTo(billingPcsQuantity) == 0) {
            log.info("已报工数量[" + produceTotalQuantity +"]单位转换后的报工数量["+ goodProducts +"]等于实际开单数量[" + billingPcsQuantity + "]");
        } else if (goodProducts.compareTo(billingPcsQuantity) > 0) {
            return "已报工数量[" + produceTotalQuantity +"]单位转换后的报工数量["+ goodProducts +"]大于实际开单数量[" + billingPcsQuantity + "],请注意及时调节生产!";
        } else if (goodProducts.compareTo(billingPcsQuantity) < 0) {
            log.info("已报工数量[" + produceTotalQuantity +"]单位转换后的报工数量["+ goodProducts +"]小于实际开单数量[" + billingPcsQuantity + "]");
        }

        return "SUCESS";
    }

    @Override
    public String checkTicketInNum(CheckTicketInNumInVO checkTicketInNumInVO) {
        // 查询已入库的数量
        List<ProductStorePO> productStorePOS = productStoreService.getByPlanTicketNO(checkTicketInNumInVO.getProductTicketNo());
        BigDecimal inBountNum = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(productStorePOS)) {
            inBountNum = productStorePOS.stream().map(ProductStorePO::getProducedQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        // erp的开单数量
        GetInboundQuantityOutVO inboundQuantity = productOutboundService.getInboundQuantity(checkTicketInNumInVO.getProductTicketNo());
        BigDecimal billingQuantity = inboundQuantity.getBillingQuantity();
        // 比较
        if (inBountNum.compareTo(billingQuantity) == 0) {
            log.info("已入库申请数量[" + inBountNum +"]等于实际开单数量[" + billingQuantity + "]");
        } else if (inBountNum.compareTo(billingQuantity) > 0) {
            return "已入库申请数量[" + inBountNum +"]大于实际开单数量[" + billingQuantity + "],请注意及时调节生产!";
        } else if (inBountNum.compareTo(billingQuantity) < 0) {
            log.info("已入库申请数量[" + inBountNum +"]小于实际开单数量[" + billingQuantity + "]");
        }
        return "SUCESS";
    }
}
