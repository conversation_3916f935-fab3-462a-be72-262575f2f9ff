package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.jihong.common.convertor.PageConvertor;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.vo.ResponseCodeOutVO;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.mes.api.model.vo.MachineConfigInVO;
import cn.jihong.mes.api.model.vo.ProductionMachineOutVO;
import cn.jihong.mes.api.service.IProductionMachineConfigService;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.production.api.model.constant.BasePaperConst;
import cn.jihong.mes.production.api.model.constant.RedisCacheConstant;
import cn.jihong.mes.production.api.model.dto.*;
import cn.jihong.mes.production.api.model.enums.*;
import cn.jihong.mes.production.api.model.po.*;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mes.production.api.service.*;
import cn.jihong.mes.production.app.aspect.RedisLock;
import cn.jihong.mes.production.app.config.PushSwitchConfig;
import cn.jihong.mes.production.app.controller.oa.DownMaterialFlowController;
import cn.jihong.mes.production.app.mapper.ProductMaterialMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.ehr.api.model.dto.UserDTO;
import cn.jihong.oa.ehr.api.service.IA01Service;
import cn.jihong.oa.erp.api.model.dto.BmbaTMaterialDTO;
import cn.jihong.oa.erp.api.model.dto.webservice.InventoryUpdateToErpInDTO;
import cn.jihong.oa.erp.api.model.po.OoabTPO;
import cn.jihong.oa.erp.api.model.po.SfdaTPO;
import cn.jihong.oa.erp.api.model.po.SfdcTPO;
import cn.jihong.oa.erp.api.model.vo.BmbaTInVO;
import cn.jihong.oa.erp.api.model.vo.RtaxlTVO;
import cn.jihong.oa.erp.api.model.vo.SfbaTVO;
import cn.jihong.oa.erp.api.service.*;
import cn.jihong.oa.erp.api.service.webservice.IInventoryToErpService;
import cn.jihong.tms.api.model.enums.TicketTypeEnum;
import cn.jihong.wms.api.model.dto.SrmBarcodeStoreDTO;
import cn.jihong.wms.api.model.dto.UpdateBarcodeStoreDTO;
import cn.jihong.wms.api.model.po.SrmBarcodeDetailPO;
import cn.jihong.wms.api.service.ISrmBarcodeDetailService;
import cn.jihong.wms.api.service.ISrmBarcodeStoreService;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <p>
 * 上料信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Slf4j
@DubboService
public class ProductMaterialServiceImpl extends JiHongServiceImpl<ProductMaterialMapper, ProductMaterialPO>
    implements IProductMaterialService {

    @Resource
    private PushSwitchConfig pushSwitchConfig;

    private static final String DEPT_NAME = "新MES";

    @Resource
    private IProductTicketService productTicketService;
    @Resource
    private IProductMaterialOperationRecordsService productMaterialOperationRecordsService;

    @DubboReference(timeout = 300000,retries = 0)
    private ISrmBarcodeStoreService srmBarcodeStoreService;

    @DubboReference
    private IA01Service ia01Service;

    @DubboReference
    private IProductionMachineService productionMachineService;


    @DubboReference
    private ISfbaTService sfbaTService;

    @DubboReference
    private ISfaaTService sfaaTService;
    @DubboReference
    private IRtaxlTService rtaxlTService;
    @DubboReference(timeout = 300000,retries = 0)
    private IInventoryToErpService iInventoryToErpService;
    @DubboReference(timeout = 300000,retries = 0)
    private ISfdaTService sfdaTService;
    @DubboReference(timeout = 300000,retries = 0)
    private ISfdcTService sfdcTService;

    @Resource
    private IProductMachineTicketService productMachineTicketService;

    @Resource
    private IProductMaterialCountService ProductMaterialCountService;
    @Resource
    private IProductInterfaceRecordsService productInterfaceRecordsService;

    @Autowired
    private IProductMaterialService productMaterialService;
    @Autowired
    private IProductMachineMaterialRecordService productMachineMaterialRecordService;
    @Autowired
    private IProductConfigService productConfigService;
    @DubboReference
    private IProductionMachineConfigService productionMachineConfigService;
    @Autowired
    private IProductMachineMaterialApportionmentService productMachineMaterialApportionmentService;
    @Autowired
    private IProductMachineDayService productMachineDayService;
    @Resource
    private Executor taskExecutor;
    @Resource
    private ISrmBarcodeDetailService srmBarcodeDetailService;

    @DubboReference
    private IOoabTService ooabTService;

    @Resource
    private DownMaterialFlowController downMaterialFlowController;
    /**
     * 根据物料编号查询物料信息
     */
    @Override
    public MaterialInfoOutVO getMaterialByBarcodeNo(MaterialByBarcodeNoInVO materialByBarcodeNoInVO) {
        if (materialByBarcodeNoInVO.getProductTicketId() == null) {
            throw new CommonException("productTicketId不能为空");
        }
        SrmBarcodeStoreDTO srmBarcodeStoreDTO = srmBarcodeStoreService.getSrmBarcodeByBarcodeNo(materialByBarcodeNoInVO.getBarcodeNo());
        if (ObjectUtil.isNull(srmBarcodeStoreDTO)) {
            throw new CommonException("未查询到" + materialByBarcodeNoInVO.getBarcodeNo() + "物料编号对应物料信息");
        }
        MaterialInfoOutVO materialInfoOutVO = new MaterialInfoOutVO();
        materialInfoOutVO.setMaterialBarcodeNo(srmBarcodeStoreDTO.getBarcodeNo());
        materialInfoOutVO.setMaterialCode(srmBarcodeStoreDTO.getItemNo());
        materialInfoOutVO.setMaterialName(srmBarcodeStoreDTO.getItemName());
        materialInfoOutVO.setMaterialUnit(srmBarcodeStoreDTO.getStockUnitNo());
        materialInfoOutVO.setMaterialWarehousingTime(srmBarcodeStoreDTO.getLotDate());
        materialInfoOutVO.setLoadingQuantity(srmBarcodeStoreDTO.getCurStorNum());
        materialInfoOutVO.setPurchaseBatch(srmBarcodeStoreDTO.getLotNo());
        materialInfoOutVO.setMaterialUnitName(srmBarcodeStoreDTO.getStockUnitName());
        materialInfoOutVO.setTransferNo(srmBarcodeStoreDTO.getTransferNo());

        materialInfoOutVO.setWarehouseNo(srmBarcodeStoreDTO.getWarehouseNo());
        materialInfoOutVO.setWarehouseName(srmBarcodeStoreDTO.getWarehouseName());
        materialInfoOutVO.setStorageNo(srmBarcodeStoreDTO.getWarehouseNoId());
        materialInfoOutVO.setStorageName(srmBarcodeStoreDTO.getStorageSpacesName());

        // 获得物料的类别
        ProductTicketPO productTicketPO = productTicketService.getById(materialByBarcodeNoInVO.getProductTicketId());
        BmbaTMaterialDTO materialDTOS =
            getBmbaTMaterialDTO(productTicketPO, srmBarcodeStoreDTO.getItemNo());
        if (ObjectUtil.isNull(materialDTOS)) {
            log.error("查询到的物料类别为空");
            throw new CommonException("工单:" + productTicketPO.getPlanTicketNo() + " \n工序:" + productTicketPO.getProcess() + " \n物料:"
                + materialByBarcodeNoInVO.getBarcodeNo() + "未开料");
        } else {
            // 物料类别对应的部件列表
            List<MaterialTypeoutVO> materialTypeoutVOS = getMaterialTypeList(materialByBarcodeNoInVO.getProductTicketId());
            Map<String, List<MaterialTypeoutVO.MaterialPlace>> materialPlaceMap = materialTypeoutVOS.stream()
                    .collect(Collectors.toMap(MaterialTypeoutVO::getMaterialType, MaterialTypeoutVO::getMaterialPlaces,(v1,v2)->v2));
            List<MaterialTypeoutVO.MaterialPlace> materialPlaces = materialPlaceMap.get(materialDTOS.getType());
            materialInfoOutVO.setMaterialPlaces(materialPlaces);

            materialInfoOutVO.setProcess(materialDTOS.getProcess());
            materialInfoOutVO.setMaterialType(materialDTOS.getType());
        }

        return materialInfoOutVO;
    }

    /**
     * 获得物料的类别
     * @param productTicketPO
     * @param itemNo
     * @return
     */
    private BmbaTMaterialDTO getBmbaTMaterialDTO(ProductTicketPO productTicketPO, String itemNo) {

        ProductionMachineOutVO productionMachineOutVO = productionMachineService
            .getProductionMachineByErpName(productTicketPO.getMachineName(), productTicketPO.getProcess());

        if (productionMachineOutVO == null) {
            throw new CommonException("查询不到机台信息："+ productTicketPO.getMachineName() + "请到机台管理中维护机台信息");
        }
        if (StringUtils.isBlank(productionMachineOutVO.getProcessCode())) {
            throw new CommonException("查询不到机台的工序信息："+ productTicketPO.getMachineName() + "请到机台管理中维护机台信息");
        }

        // 获得物料对应的物料类别 工厂，产品，工序，原件
        BmbaTInVO bmbaTInVO = new BmbaTInVO();
        bmbaTInVO.setCompanyCode(productionMachineOutVO.getEnterpriseNumbers());
        bmbaTInVO.setProducts(productTicketPO.getPlanTicketNo());
        bmbaTInVO.setProcess(productionMachineOutVO.getProcessCode());
        bmbaTInVO.setOriginal(itemNo);
        List<BmbaTMaterialDTO> list = sfbaTService.getByMaterial(bmbaTInVO);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        BmbaTMaterialDTO bmbaTMaterialDTO = list.get(0);

        Set<String> inkTypes = BasePaperConst.getInkTypes();

        // 获取需要比较的值并进行空值检查
        String imaaua004 = bmbaTMaterialDTO.getImaaua004();
        if (imaaua004 != null && !imaaua004.isEmpty() && inkTypes.contains(imaaua004)) {
            throw new CommonException(
                "物料：" + bmbaTMaterialDTO.getOriginal() + " ，类别：" + bmbaTMaterialDTO.getImaaua004() + " 是油墨或胶水不需要上料");
        }
        
        return bmbaTMaterialDTO;
    }



    /**
     * 获得物料的类别列表
     * @param productTicketId
     * @return
     */
    private List<BmbaTMaterialDTO> getBmbaTMaterialList(Long productTicketId) {
        // 获得所在工序 process
        ProductTicketPO productTicketPO = productTicketService.getById(productTicketId);
        ProductionMachineOutVO productionMachineOutVO = productionMachineService
                .getProductionMachineByErpName(productTicketPO.getMachineName(), productTicketPO.getProcess());

        // 获得物料对应的物料类别 工厂，产品，工序，原件
        BmbaTInVO bmbaTInVO = new BmbaTInVO();
        bmbaTInVO.setCompanyCode(productionMachineOutVO.getEnterpriseNumbers());
        bmbaTInVO.setProducts(productTicketPO.getPlanTicketNo());
        bmbaTInVO.setProcess(productionMachineOutVO.getProcessCode());
        List<BmbaTMaterialDTO> materialList = sfbaTService.getByMaterial(bmbaTInVO);

        if (CollectionUtil.isEmpty(materialList)) {
            throw new CommonException("工单" + productTicketPO.getPlanTicketNo()  + "，在工厂" + productionMachineOutVO.getEnterpriseNumbers() + "的工序 "
                + productionMachineOutVO.getProcessCode() + "下无物料信息");
        }

        return materialList;
    }

    /**
     * 根据物料类型查询当前用料信息
     */
    @Override
    public MaterialInfoOutVO getMaterialInfoByCode(GetMaterialInfoByCodeInVO getMaterialInfoByCodeInVO) {
        LambdaQueryWrapper<ProductMaterialPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductMaterialPO::getMachineName,getMaterialInfoByCodeInVO.getMachineName())
                .eq(ProductMaterialPO::getProductTicketId,getMaterialInfoByCodeInVO.getProductTicketId())
                .eq(ProductMaterialPO::getMaterialType,getMaterialInfoByCodeInVO.getMaterialType())
                .eq(ProductMaterialPO::getStatus,MaterialEnum.IN_PRODUCTION.getCode());
        return BeanUtil.copyProperties(getOne(lambdaQueryWrapper),MaterialInfoOutVO.class);
    }

    /**
     * 根据物料类型查询当前用料信息
     */
    private List<MaterialInfoOutVO> getMaterialInfoByCode(String machineName,String materialType) {
        LambdaQueryWrapper<ProductMaterialPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductMaterialPO::getMachineName,machineName)
//                .eq(ProductMaterialPO::getProductTicketId,productTicketId)
                .eq(ProductMaterialPO::getMaterialType,materialType)
                .eq(ProductMaterialPO::getStatus,MaterialEnum.IN_PRODUCTION.getCode());
        return BeanUtil.copyToList(list(lambdaQueryWrapper),MaterialInfoOutVO.class);
    }

    /**
     * 保存物料信息
     */
    @RedisLock
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveMaterialInfo(String redisLockKey, SaveMaterialInfoInVO saveMaterialInfoInVO) {
        if (saveMaterialInfoInVO.getUpMaterialInfo() == null) {
            throw new CommonException("请扫码获得物料信息");
        }
        MaterialByBarcodeNoInVO materialByBarcodeNoInVO = new MaterialByBarcodeNoInVO();
        materialByBarcodeNoInVO.setMachineName(saveMaterialInfoInVO.getMachineName());
        materialByBarcodeNoInVO.setProductTicketId(saveMaterialInfoInVO.getProductTicketId());
        materialByBarcodeNoInVO.setBarcodeNo(saveMaterialInfoInVO.getUpMaterialInfo().getMaterialBarcodeNo());
        MaterialInfoOutVO materialInfoOutVO = getMaterialByBarcodeNo(materialByBarcodeNoInVO);
        if (saveMaterialInfoInVO.getUpMaterialInfo().getLoadingQuantity()
            .compareTo(materialInfoOutVO.getLoadingQuantity()) != 0) {
            throw new CommonException("上料数量和查询是数量不同：" + saveMaterialInfoInVO.getUpMaterialInfo().getLoadingQuantity() + ":"
                + materialInfoOutVO.getLoadingQuantity());
        }
        if (BigDecimal.ZERO.compareTo(saveMaterialInfoInVO.getUpMaterialInfo().getLoadingQuantity()) >=0 ) {
            throw new CommonException("上料数量不能小于等于0");
        }
        if (saveMaterialInfoInVO.getUpMaterialInfo().getMaterialPlace() == null ||
                saveMaterialInfoInVO.getUpMaterialInfo().getMaterialPlace().length() == 0) {
            throw new CommonException("物料部件（用途）不能为空");
        }

        ProductTicketPO productTicketPO = productTicketService.verify(saveMaterialInfoInVO.getProductTicketId());

        GetByMachineNameInVO getByMachineNameInVO = new GetByMachineNameInVO();
        getByMachineNameInVO.setMachineName(saveMaterialInfoInVO.getMachineName());
        // 校验是否有暂存物料 需要在转产优先使用
        List<MaterialInfoOutVO> materialList = getStagingMaterialListByMachineName(getByMachineNameInVO);
        if (CollectionUtil.isNotEmpty(materialList)) {
            String materialName = materialList.stream().map(MaterialInfoOutVO::getMaterialName).collect(Collectors.joining(","));
            throw new CommonException("存在暂存的物料" + materialName + ",请先在转产中上料");
        }

        // 校验
        verify(saveMaterialInfoInVO.getUpMaterialInfo().getMaterialType(), saveMaterialInfoInVO.getProductTicketId(),
            saveMaterialInfoInVO.getMachineName(), saveMaterialInfoInVO.getUpMaterialInfo().getMaterialBarcodeNo(),
            saveMaterialInfoInVO.getUpMaterialInfo().getMaterialPlace(),saveMaterialInfoInVO.getUpMaterialInfo().getMaterialCode(),
                productTicketPO.getProcessType(),productTicketPO.getProcessCode());

        // 校验机位信息
        MachineConfigInVO machineConfigInVO = new MachineConfigInVO();
        machineConfigInVO.setMachineName(saveMaterialInfoInVO.getMachineName());
        Integer isp = productionMachineConfigService.getMachineParts(machineConfigInVO);
        if (isp.equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))) {
            if (StringUtils.isBlank(saveMaterialInfoInVO.getUpMaterialInfo().getParts())) {
                throw new CommonException("当前机台需要机位信息，请选择机位信息");
            }
        }

        // 更新设备止码
        // productTicketService.updateMachineStopNo(saveMaterialInfoInVO.getProductTicketId(),saveMaterialInfoInVO.getMachineStopNo());

        // 将上料信息保存为使用中状态
        ProductMaterialPO productMaterialPO = saveProductMaterialPO(materialInfoOutVO,productTicketPO,
                saveMaterialInfoInVO.getUpMaterialInfo(), MaterialEnum.IN_PRODUCTION.getCode());

        // 保存栈板操作记录
        saveMaterialOperationRecords(productMaterialPO, productTicketPO, MaterialOperateTypeEnum.UP_MATERIAL.getCode(),
            saveMaterialInfoInVO.getUpMaterialInfo().getMaterialBarcodeNo(), saveMaterialInfoInVO.getMachineStopNo(),
            null, null);

        return productMaterialPO.getId();
    }

    private ProductMaterialOperationRecordsPO saveMaterialOperationRecords(ProductMaterialPO productMaterialPO, ProductTicketPO productTicketPO,
                                              String operation, String materialBarcodeNo, Integer machineStopNo, String remainingReason, String snowflake) {
        ProductMaterialOperationRecordsDTO productMaterialOperationRecordsDTO =
            new ProductMaterialOperationRecordsDTO();
        productMaterialOperationRecordsDTO.setOperationType(operation);
        productMaterialOperationRecordsDTO.setTicketType(String.valueOf(TicketTypeEnum.PRODUCTION.getCode()));
        productMaterialOperationRecordsDTO.setTicketNumber(productTicketPO.getPlanTicketNo());
        productMaterialOperationRecordsDTO.setOperator(SecurityUtil.getUserId());
        productMaterialOperationRecordsDTO.setOriginalQuantity(productMaterialPO.getLoadingQuantity());
        productMaterialOperationRecordsDTO.setConsumptionQuantity(productMaterialPO.getConsumptionQuantity());
        productMaterialOperationRecordsDTO.setRemainingQuantity(productMaterialPO.getRemainingQuantity());
        productMaterialOperationRecordsDTO.setProductTicketId(productTicketPO.getId());
        productMaterialOperationRecordsDTO.setMaterialBarcodeNo(materialBarcodeNo);
        productMaterialOperationRecordsDTO.setProductMaterialId(productMaterialPO.getId());
        productMaterialOperationRecordsDTO.setMachineStopNo(machineStopNo);
        productMaterialOperationRecordsDTO.setRemainingReason(remainingReason);
        productMaterialOperationRecordsDTO.setCreateBy(SecurityUtil.getUserId());
        productMaterialOperationRecordsDTO.setCompanyCode(productMaterialPO.getCompanyCode());
        productMaterialOperationRecordsDTO.setRequestId(snowflake);

        productMaterialOperationRecordsDTO.setWarehouseNo(productMaterialPO.getWarehouseNo());
        productMaterialOperationRecordsDTO.setWarehouseName(productMaterialPO.getWarehouseName());
        productMaterialOperationRecordsDTO.setStorageNo(productMaterialPO.getStorageNo());
        productMaterialOperationRecordsDTO.setStorageName(productMaterialPO.getStorageName());

        // 调拨单  扣料单
        productMaterialOperationRecordsDTO.setTransferNo(productMaterialPO.getTransferNo());
        productMaterialOperationRecordsDTO.setDeductionNo(productMaterialPO.getDeductionNo());

        return productMaterialOperationRecordsService.saveOrUpdateMaterialUse(productMaterialOperationRecordsDTO);

      //  productMaterialOperationRecordsService.work(productMaterialOperationRecordsDTO, operation);
    }



    private void verify(String materialType, Long productTicketId, String machineName, String materialBarcodeNo, String materialPlace,
                        String materialCode, String processType, String processCode) {
        // 不可重复上料
        LambdaQueryWrapper<ProductMaterialPO> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(ProductMaterialPO::getMaterialBarcodeNo,materialBarcodeNo)
//                .eq(ProductMaterialPO::getProductTicketId,productTicketId)
                .eq(ProductMaterialPO::getStatus,MaterialEnum.IN_PRODUCTION.getCode());
        ProductMaterialPO productMaterialPO = getOne(lambdaQueryWrapper);
        if (ObjectUtil.isNotEmpty(productMaterialPO)) {
            throw new CommonException("该物料正在使用中，不可重复上料，使用中机台名称：" + productMaterialPO.getMachineName());
        }


        ProductMaterialCountDTO ProductMaterialCount =
                ProductMaterialCountService.getMaterialCountCount(processType, materialType,materialPlace);
        // 比较现有物料数量和原来数量的
        if (ObjectUtil.isNotNull(ProductMaterialCount)) {
            List<ProductMaterialPO> productMaterialPOS = getByMachineNameAndCode(machineName,materialCode);
            if (ProductMaterialCount.getMaterialCount() <= productMaterialPOS.size() ) {
                throw new CommonException("机台可上"+ materialCode +"物料数量为："+ProductMaterialCount.getMaterialCount()+ "，已上数量"+productMaterialPOS.size());
            }
            return;
        }

        MachineConfigInVO machineConfigInVO = MachineConfigInVO.builder()
               .machineName(machineName)
               .build();
        machineConfigInVO.setMachineName(machineName);
//        machineConfigInVO.setProcessCode(processCode);
        Integer isp = productionMachineConfigService.getMachineParts(machineConfigInVO);
        // 校验上料部位
        if (isp.equals(Integer.valueOf(BooleanEnum.FALSE.getCode()))) {
            // 不是按照机位上料，才需要校验
            log.info("该机台"+ machineName +"的工序"+ processCode +"不按照机位上料，需校验上料部位");
            validityPlace(materialType, productTicketId, machineName, materialPlace);
        }

    }

    private void validityPlace(String materialType, Long productTicketId, String machineName, String materialPlace) {
        // 物料类别对应的部件列表
        List<MaterialTypeoutVO> materialTypeoutVOS = getMaterialTypeList(productTicketId);
        Map<String, List<MaterialTypeoutVO.MaterialPlace>> materialPlaceMap = materialTypeoutVOS.stream().collect(
            Collectors.toMap(MaterialTypeoutVO::getMaterialType, MaterialTypeoutVO::getMaterialPlaces, (v1, v2) -> v2));
        List<MaterialTypeoutVO.MaterialPlace> materialPlaces = materialPlaceMap.get(materialType);

        // 数据库中该类别的数据
        List<MaterialInfoOutVO> materialInfoOutVOS = getMaterialInfoByCode(machineName, materialType);
        if (CollectionUtil.isNotEmpty(materialInfoOutVOS)) {
            List<String> materialPlaceNames =
                materialInfoOutVOS.stream().map(MaterialInfoOutVO::getMaterialPlaceName).collect(Collectors.toList());
            if (materialPlaceNames.contains(materialPlace)) {
                throw new CommonException("该部位已上料，请勿重复上料,请先下料之前的物料或者进行换料操作");
            }
        }

        log.info("该物料类型"+ materialType +"在bom表中有条" + materialPlaces.size() + "，已上料条"
                + materialInfoOutVOS.size());
        // 该物料 类别 的 部件信息（用途）存在多条，则需要校验该类型数量是否已经是最大数量
        if (CollectionUtil.isNotEmpty(materialPlaces) && materialPlaces.size() > 1) {
            // 当数据库中该类型的部件大于等于 bom表中的条数，则提示异常
            if (materialInfoOutVOS.size() >= materialPlaces.size()) {
                throw new CommonException("该物料类型"+ materialType +"在bom表中有条" + materialPlaces.size() + "，已上料条"
                    + materialInfoOutVOS.size() + "，请勿重复上料,请先下料之前的物料或者进行换料操作");
            }
        }
    }

    private List<ProductMaterialPO> getByMachineNameAndCode(String machineName, String materialCode) {
        LambdaQueryWrapper<ProductMaterialPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProductMaterialPO::getMachineName,machineName)
                .eq(ProductMaterialPO::getMaterialCode,materialCode)
                // 正在使用中的
                .eq(ProductMaterialPO::getStatus,MaterialEnum.IN_PRODUCTION.getCode())
                .eq(ProductMaterialPO::getDeleted, Integer.valueOf(BooleanEnum.FALSE.getCode()));
        return list(queryWrapper);
    }


    @Override
    public List<MaterialInfoOutVO> getMaterialList(Long productTicket) {
        LambdaQueryWrapper<ProductMaterialPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProductMaterialPO::getProductTicketId,productTicket)
                // 正在使用中的
                .eq(ProductMaterialPO::getStatus,MaterialEnum.IN_PRODUCTION.getCode());
        List<ProductMaterialPO> productMaterialPOS = list(queryWrapper);
        return BeanUtil.copyToList(productMaterialPOS,MaterialInfoOutVO.class);
    }

    @Override
    public List<GetListByMaterialCodeOutVO> getListByMaterialCode(GetListByMaterialCodeInVO inVO) {
        LambdaQueryWrapper<ProductMaterialPO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(inVO.getProductTicketId()),ProductMaterialPO::getProductTicketId,inVO.getProductTicketId())
                .eq(StrUtil.isNotBlank(inVO.getMaterialCode()),ProductMaterialPO::getMaterialCode,inVO.getMaterialCode())
                .like(StrUtil.isNotBlank(inVO.getMachineName()),ProductMaterialPO::getMachineName,inVO.getMachineName());
        queryWrapper.orderByDesc(ProductMaterialPO::getCreateTime);
        List<ProductMaterialPO> productMaterialPOS = list(queryWrapper);
        return BeanUtil.copyToList(productMaterialPOS, GetListByMaterialCodeOutVO.class);
    }

    /**
     * 查询上料记录
     */
    @Override
    public Pagination<MaterialRecordsOutVO> getMaterialRecords(ProductTicketPageInVO productTicketPageInVO) {
        Page<MaterialRecordsOutVO> iPage =
            productMaterialOperationRecordsService.getMaterialRecords(productTicketPageInVO);
        return getMaterialInfoOutVOPagination(iPage);
    }

    private Pagination<MaterialRecordsOutVO> getMaterialInfoOutVOPagination(IPage<MaterialRecordsOutVO> page) {
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return Pagination.newInstance(null);
        }

        List<Long> userIds =
                page.getRecords().stream().map(MaterialRecordsOutVO::getCreateBy).distinct().collect(Collectors.toList());
        Map<Long, String> userMap =
                ia01Service.getUserInfoByIds(userIds).stream().collect(Collectors.toMap(UserDTO::getId, UserDTO::getName));

        return Pagination.newInstance(page.getRecords().stream().map(materialRecordsOutVO -> {
            materialRecordsOutVO.setCreaterName(userMap.get(materialRecordsOutVO.getCreateBy()));
            return materialRecordsOutVO;
        }).collect(Collectors.toList()), page);
    }

    @Override
    public Pagination<MaterialRecordsOutVO> getMaterialRecordsByTicketBase(IPage page, ProductTicketBaseDTO productTicketBaseDTO) {
        Page<MaterialRecordsOutVO> iPage =
                productMaterialOperationRecordsService.getMaterialRecordsByTicketBase(page,productTicketBaseDTO);
        return getMaterialInfoOutVOPagination(page, iPage);
    }

    /**
     * 下料
     */
    @GlobalTransactional(rollbackFor = Exception.class,timeoutMills = 300000)
    @Transactional
    @Override
    public DownMaterialOutVO downMaterial(DownMaterialInVO downMaterialInVO) {
        ProductMaterialPO materialPO = getById(downMaterialInVO.getId());
        if (materialPO == null) {
            throw new CommonException("物料不存在");
        }
        if (MaterialEnum.IN_LIBRARY.getCode().equals(materialPO.getStatus())) {
            throw new CommonException("物料已下料，请勿重复操作");
        }
        // 剩余数量不能大于实际的剩余数量
        if (downMaterialInVO.getRemainingQuantity().compareTo(materialPO.getRemainingQuantity()) == 1) {
            throw new CommonException(
                    "剩余数量" + downMaterialInVO.getRemainingQuantity() + "不得大于实际的剩余数量" + materialPO.getRemainingQuantity());
        }
        // 消耗数量 不得大于 剩余数量
        if (downMaterialInVO.getConsumptionQuantity().compareTo(materialPO.getRemainingQuantity()) == 1) {
            throw new CommonException(
                    "消耗数量" + downMaterialInVO.getConsumptionQuantity() + "不得大于实际的剩余数量" + materialPO.getRemainingQuantity());
        }
        // 领用数量不等于消耗数量+剩余数量
        if (!(downMaterialInVO.getConsumptionQuantity().add(downMaterialInVO.getRemainingQuantity())
                .compareTo(materialPO.getLoadingQuantity()) == 0)) {
            throw new CommonException(
                    "消耗数量" + downMaterialInVO.getConsumptionQuantity() +
                            " + 剩余数量" + downMaterialInVO.getRemainingQuantity() + " = "
                            + downMaterialInVO.getConsumptionQuantity().add(downMaterialInVO.getRemainingQuantity())
                            + "不等于领用数量" + materialPO.getLoadingQuantity());
        }

        String LOCK_KEY = RedisCacheConstant.UPDATE_MATERIAL + materialPO.getMaterialBarcodeNo();
        return productMaterialService.downMaterial(LOCK_KEY, downMaterialInVO, materialPO);
    }

    @Override
    @RedisLock
    public DownMaterialOutVO downMaterial(String redisLockKey, DownMaterialInVO downMaterialInVO, ProductMaterialPO materialPO) {
        DownMaterialOutVO outVO = new DownMaterialOutVO();
        materialPO.setConsumptionQuantity(downMaterialInVO.getConsumptionQuantity());
        materialPO.setRemainingQuantity(downMaterialInVO.getRemainingQuantity());
        materialPO.setRemainingReason(downMaterialInVO.getRemainingReason());
        materialPO.setDownTime(new Date());
        materialPO.setStatus(MaterialEnum.IN_LIBRARY.getCode());


        ProductTicketPO productTicketPO = productTicketService.getById(materialPO.getProductTicketId());
        Snowflake snowflake = new Snowflake(1,1);
        String nextIdStr = snowflake.nextIdStr();

        // 保存栈板操作记录
        ProductMaterialOperationRecordsPO productMaterialOperationRecordsPO = saveMaterialOperationRecords(materialPO, productTicketPO, MaterialOperateTypeEnum.DOWN_MATERIAL.getCode(),
                materialPO.getMaterialBarcodeNo(), productTicketPO.getMachineStopNo(),
                downMaterialInVO.getRemainingReason(), nextIdStr);

        if (materialPO.getConsumptionQuantity().compareTo(BigDecimal.ZERO) == 1) {
            // 调用erp接口，推送消耗数量
            try {
                UpdateMaterialDTO updateMaterialDTO = new UpdateMaterialDTO();
                updateMaterialDTO.setFlag(UpdateMaterialTypeEnum.SUBTRACT.getIntCode());
                updateMaterialDTO.setOperateType(MaterialOperateTypeEnum.DOWN_MATERIAL.getCode());
                updateMaterialDTO.setNextIdStr(nextIdStr);
                updateMaterialDTO.setKey(null);
                updateMaterialDTO.setMaterialOperationRecordsId(productMaterialOperationRecordsPO.getId());

                ResponseCodeOutVO responseCodeOutVO = updateMaterial(materialPO, productTicketPO, updateMaterialDTO);
                log.info("downMaterial responseCodeOutVO:{}", JSON.toJSONString(responseCodeOutVO));
                materialPO.setDeductionNo(responseCodeOutVO.getMessage());
            } catch (Exception e) {
                log.error("下料推送erp异常:{}" , e.getMessage(),e);
                //  ERP 超领料 具体错误码
                if(e.getMessage().contains("申请量超过工单总应发")){
                    // mes 这边先下料成功 然后发起OA 超领料单
                    outVO.setMaterialOverClaimCode("csf-80040");

                    // 发起OA 超领单
                    productMaterialOperationRecordsService.sendSubmitWorkflowRequest(productMaterialOperationRecordsPO.getId());
                }else {
                    throw new CommonException(e.getMessage());
                }
            }
        }

        // 调拨单  扣料单
        productMaterialOperationRecordsPO.setTransferNo(materialPO.getTransferNo());
        productMaterialOperationRecordsPO.setDeductionNo(materialPO.getDeductionNo());
        productMaterialOperationRecordsService.updateById(productMaterialOperationRecordsPO);

        updateById(materialPO);
        outVO.setProductMaterialId(materialPO.getId());
        return outVO;
    }


    /**
     * 暂存
     */
    @GlobalTransactional(rollbackFor = Exception.class,timeoutMills = 300000)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String temporaryStorageMaterial(TemporaryStorageMaterialInVO inVO) {
        ProductMaterialPO materialPO = getById(inVO.getId());
        if (materialPO == null) {
            throw new CommonException("物料不存在");
        }
        if (MaterialEnum.TEMPORARY_STORAGE.getCode().equals(materialPO.getStatus())) {
            throw new CommonException("物料已暂存，请勿重复操作");
        }
        // 剩余数量不能大于实际的剩余数量
        if (inVO.getRemainingQuantity().compareTo(materialPO.getRemainingQuantity()) == 1) {
            throw new CommonException(
                    "剩余数量" + inVO.getRemainingQuantity() + "不得大于实际的剩余数量" + materialPO.getRemainingQuantity());
        }
        // 消耗数量 不得大于 剩余数量
        if (inVO.getConsumptionQuantity().compareTo(materialPO.getRemainingQuantity()) == 1) {
            throw new CommonException(
                    "消耗数量" + inVO.getConsumptionQuantity() + "不得大于实际的剩余数量" + materialPO.getRemainingQuantity());
        }
        // 领用数量不等于消耗数量+剩余数量
        if (!(inVO.getConsumptionQuantity().add(inVO.getRemainingQuantity())
                .compareTo(materialPO.getLoadingQuantity()) == 0)) {
            throw new CommonException(
                    "消耗数量" + inVO.getConsumptionQuantity() +
                            " + 剩余数量" + inVO.getRemainingQuantity() + " = "
                            + inVO.getConsumptionQuantity().add(inVO.getRemainingQuantity())
                            + "不等于领用数量" + materialPO.getLoadingQuantity());
        }

        String LOCK_KEY = RedisCacheConstant.UPDATE_MATERIAL + materialPO.getMaterialBarcodeNo();
        productMaterialService.temporaryStorageMaterial(LOCK_KEY,inVO, materialPO);

        return String.valueOf(materialPO.getId());
    }

    @Override
    @RedisLock
    public void temporaryStorageMaterial(String redisLockKey, TemporaryStorageMaterialInVO inVO, ProductMaterialPO materialPO) {
        materialPO.setConsumptionQuantity(inVO.getConsumptionQuantity());
        materialPO.setRemainingQuantity(inVO.getRemainingQuantity());
        materialPO.setRemainingReason(inVO.getRemainingReason());
        materialPO.setStatus(MaterialEnum.TEMPORARY_STORAGE.getCode());

        ProductTicketPO productTicketPO = productTicketService.getById(materialPO.getProductTicketId());
        Snowflake snowflake = new Snowflake(1,1);
        String nextIdStr = snowflake.nextIdStr();

        // 保存栈板操作记录
        ProductMaterialOperationRecordsPO productMaterialOperationRecordsPO = saveMaterialOperationRecords(materialPO, productTicketPO, MaterialOperateTypeEnum.TEMPORARY_STORAGE.getCode(),
                materialPO.getMaterialBarcodeNo(), productTicketPO.getMachineStopNo(),
                inVO.getRemainingReason(), nextIdStr);

        if (materialPO.getConsumptionQuantity().compareTo(BigDecimal.ZERO) == 1) {
            UpdateMaterialDTO updateMaterialDTO = new UpdateMaterialDTO();
            updateMaterialDTO.setFlag(UpdateMaterialTypeEnum.SUBTRACT.getIntCode());
            updateMaterialDTO.setOperateType(MaterialOperateTypeEnum.TEMPORARY_STORAGE.getCode());
            updateMaterialDTO.setNextIdStr(nextIdStr);
            updateMaterialDTO.setKey(null);
            updateMaterialDTO.setMaterialOperationRecordsId(productMaterialOperationRecordsPO.getId());

            ResponseCodeOutVO responseCodeOutVO = updateMaterial(materialPO, productTicketPO, updateMaterialDTO);
            materialPO.setDeductionNo(responseCodeOutVO.getMessage());
        }

        // 调拨单  扣料单
        productMaterialOperationRecordsPO.setTransferNo(materialPO.getTransferNo());
        productMaterialOperationRecordsPO.setDeductionNo(materialPO.getDeductionNo());
        productMaterialOperationRecordsService.updateById(productMaterialOperationRecordsPO);

        updateById(materialPO);
    }

    @Override
    public ResponseCodeOutVO updateMaterial(ProductMaterialPO materialPO, ProductTicketPO productTicketPO,UpdateMaterialDTO updateMaterialDTO) {
        if (BooleanEnum.FALSE.getCode().equals(pushSwitchConfig.getErpUpdateMaterial())) {
            return null;
        }

        // 获得结单类型
        MachineConfigInVO machineConfigInVO = MachineConfigInVO.builder()
                .machineName(productTicketPO.getMachineName())
                .build();
        Integer billingType = productionMachineConfigService.getBillingType(machineConfigInVO);
        updateMaterialDTO.setBillingType(billingType);
        // 需要汇总机台消耗物料信息
        ProductMachineMaterialRecordDTO productMachineMaterialRecordDTO = setProductMachineMaterialRecordDTO(materialPO, productTicketPO,updateMaterialDTO);
        productMachineMaterialRecordService.save(productMachineMaterialRecordDTO);

        // 获得结单类型
        // 并且需要时一单一结，才需要调用erp接口，推送消耗数量
        if (ProductConfigBillingTypeEnum.ONE_ORDER.getIntCode().equals(billingType)) {
            // 一单一结的话，直接去保存到物料使用表
            ProductMachineMaterialApportionmentPO productMachineMaterialApportionmentPO = saveMaterialUse(materialPO, productTicketPO, updateMaterialDTO);
            ResponseCodeOutVO responseCodeOutVO = updateMaterialToErpAndWms(materialPO, productTicketPO, updateMaterialDTO);

            productMachineMaterialApportionmentPO.setDeductionNo(responseCodeOutVO.getMessage());
            productMachineMaterialApportionmentService.updateById(productMachineMaterialApportionmentPO);
            return responseCodeOutVO;
        }
        return new ResponseCodeOutVO();
    }

    @Override
    public ResponseCodeOutVO updateMaterialToErpAndWms(ProductMaterialPO materialPO, ProductTicketPO productTicketPO, UpdateMaterialDTO updateMaterialDTO) {
        // 补丁   报文添加：stock_code库存特征，item_feature_no产品特征，对应wms的stock_code，product_code
        SrmBarcodeDetailPO srmBarcodeDetailPO = srmBarcodeDetailService.getSrmBarcodeDetailByBarcodeNo(materialPO.getMaterialBarcodeNo());

        // 调用erp接口，推送消耗数量
        ResponseCodeOutVO responseCodeOutVO = updateToErp(materialPO, productTicketPO, updateMaterialDTO,srmBarcodeDetailPO);
        // 调用wms系统，推动消耗数量
        BigDecimal qty = updateMaterialDTO.getQty() == null ? materialPO.getConsumptionQuantity() : updateMaterialDTO.getQty();
        updateBarcodeStore(qty, materialPO.getMaterialBarcodeNo(),
                responseCodeOutVO.getMessage(), updateMaterialDTO.getFlag());
        return responseCodeOutVO;
    }

    private ProductMachineMaterialApportionmentPO saveMaterialUse(ProductMaterialPO materialPO, ProductTicketPO productTicketPO, UpdateMaterialDTO updateMaterialDTO) {
        ProductMachineMaterialApportionmentPO productMachineMaterialApportionmentPO = new ProductMachineMaterialApportionmentPO();
        productMachineMaterialApportionmentPO.setMachineName(productTicketPO.getMachineName());
        productMachineMaterialApportionmentPO.setProduceDate(productTicketPO.getProduceDate());
        productMachineMaterialApportionmentPO.setShift(productTicketPO.getShift());
        productMachineMaterialApportionmentPO.setPlanTicketNo(productTicketPO.getPlanTicketNo());
        productMachineMaterialApportionmentPO.setMaterialCode(materialPO.getMaterialCode());
        productMachineMaterialApportionmentPO.setMaterialName(materialPO.getMaterialName());
        productMachineMaterialApportionmentPO.setMaterialUnit(materialPO.getMaterialUnit());
        productMachineMaterialApportionmentPO.setConsumptionQuantity(materialPO.getConsumptionQuantity());
        productMachineMaterialApportionmentPO.setRemainingQuantity(materialPO.getRemainingQuantity());
        productMachineMaterialApportionmentPO.setMaterialBarcodeNo(materialPO.getMaterialBarcodeNo());
        productMachineMaterialApportionmentPO.setMaterialType(materialPO.getMaterialType());
        productMachineMaterialApportionmentPO.setMaterialPlace(materialPO.getMaterialPlace());
        productMachineMaterialApportionmentPO.setMaterialPlaceName(materialPO.getMaterialPlaceName());
        productMachineMaterialApportionmentPO.setPurchaseBatch(materialPO.getPurchaseBatch());
        productMachineMaterialApportionmentPO.setProcess(productTicketPO.getProcess());
        productMachineMaterialApportionmentPO.setProcessCode(productTicketPO.getProcessCode());
        productMachineMaterialApportionmentPO.setProductTicketId(productTicketPO.getId());
        productMachineMaterialApportionmentPO.setUseType(MaterialUseTypeEnum.POSITIVE_MATERIAL.getIntCode());
        productMachineMaterialApportionmentPO.setCompanyCode(SecurityUtil.getCompanySite());
        productMachineMaterialApportionmentPO.setMaterialId(materialPO.getId());
        productMachineMaterialApportionmentPO.setProductName(productTicketPO.getProductName());
        productMachineMaterialApportionmentPO.setMaterialOperationId(updateMaterialDTO.getMaterialOperationRecordsId());
        productMachineMaterialApportionmentPO.setCreateBy(SecurityUtil.getUserId());
        productMachineMaterialApportionmentPO.setUpdateBy(SecurityUtil.getUserId());
        productMachineMaterialApportionmentPO.setWarehouseNo(materialPO.getWarehouseNo());
        productMachineMaterialApportionmentPO.setWarehouseName(materialPO.getWarehouseName());
        productMachineMaterialApportionmentPO.setStorageNo(materialPO.getStorageNo());
        productMachineMaterialApportionmentPO.setStorageName(materialPO.getStorageName());
        productMachineMaterialApportionmentService.save(productMachineMaterialApportionmentPO);
        return productMachineMaterialApportionmentPO;
    }

    private ProductMachineMaterialRecordDTO setProductMachineMaterialRecordDTO(ProductMaterialPO materialPO, ProductTicketPO productTicketPO,UpdateMaterialDTO updateMaterialDTO) {
        ProductMachineMaterialRecordDTO productMachineMaterialRecordDTO = new ProductMachineMaterialRecordDTO();
        productMachineMaterialRecordDTO.setCompanyCode(productTicketPO.getCompanyCode());
        productMachineMaterialRecordDTO.setMachineName(productTicketPO.getMachineName());
        productMachineMaterialRecordDTO.setProduceDate(productTicketPO.getProduceDate());
        productMachineMaterialRecordDTO.setShift(productTicketPO.getShift());
        productMachineMaterialRecordDTO.setPlanTicketNo(productTicketPO.getPlanTicketNo());
        productMachineMaterialRecordDTO.setProductTicketId(productTicketPO.getId());

        productMachineMaterialRecordDTO.setMaterialCode(materialPO.getMaterialCode());
        productMachineMaterialRecordDTO.setMaterialName(materialPO.getMaterialName());
        productMachineMaterialRecordDTO.setMaterialUnit(materialPO.getMaterialUnit());
        // 消耗数量
        productMachineMaterialRecordDTO.setConsumptionQuantity(materialPO.getConsumptionQuantity());
        productMachineMaterialRecordDTO.setRemainingQuantity(materialPO.getRemainingQuantity());
        productMachineMaterialRecordDTO.setMaterialBarcodeNo(materialPO.getMaterialBarcodeNo());
        productMachineMaterialRecordDTO.setMaterialType(materialPO.getMaterialType());
        productMachineMaterialRecordDTO.setMaterialPlace(materialPO.getMaterialPlace());
        productMachineMaterialRecordDTO.setMaterialPlaceName(materialPO.getMaterialPlaceName());
        productMachineMaterialRecordDTO.setPurchaseBatch(materialPO.getPurchaseBatch());
        productMachineMaterialRecordDTO.setMaterialId(materialPO.getId());
        productMachineMaterialRecordDTO.setMaterialOperationId(updateMaterialDTO.getMaterialOperationRecordsId());
        if (updateMaterialDTO.getBillingType().equals(ProductConfigBillingTypeEnum.ONE_DAY.getIntCode())){
            productMachineMaterialRecordDTO.setUseType(MaterialUseTypeEnum.APPORTIONMENT_MATERIAL.getIntCode());
        } else {
            productMachineMaterialRecordDTO.setUseType(MaterialUseTypeEnum.POSITIVE_MATERIAL.getIntCode());
        }

        MachineConfigInVO machineConfigInVO = MachineConfigInVO.builder()
                .machineName(productTicketPO.getMachineName())
                .build();
        machineConfigInVO.setMachineName(productTicketPO.getMachineName());
        Integer isp = productionMachineConfigService.getMachineParts(machineConfigInVO);
        if (isp.equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))) {
            // 机位
            productMachineMaterialRecordDTO.setParts(materialPO.getParts());
            productMachineMaterialRecordDTO.setPartsName(materialPO.getPartsName());
        }

        productMachineMaterialRecordDTO.setWarehouseNo(materialPO.getWarehouseNo());
        productMachineMaterialRecordDTO.setWarehouseName(materialPO.getWarehouseName());
        productMachineMaterialRecordDTO.setStorageNo(materialPO.getStorageNo());
        productMachineMaterialRecordDTO.setStorageName(materialPO.getStorageName());


        return productMachineMaterialRecordDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<GetMaterialDailySettlementOutVO> dailySettlementMaterial(DailySettlementMaterialInVO dailySettlementMaterialInVO) {
        // 必须先下机
        ProductTicketPO productionTicket = productTicketService.getProductionTicket(dailySettlementMaterialInVO.getMachineName());
        if (ObjectUtil.isNotNull(productionTicket)) {
            throw new CommonException(String.format("%s已存在工单：日期%s班次%s请先结束对应工单", dailySettlementMaterialInVO.getMachineName(),
                    DateUtil.format(productionTicket.getProduceDate(), DatePattern.NORM_DATE_PATTERN), ProductShitEnum.getProductShitEnum(productionTicket.getShift()).getName()));
        }

        // 获得结单类型
        MachineConfigInVO machineConfigInVO = MachineConfigInVO.builder()
                .machineName(dailySettlementMaterialInVO.getMachineName())
                .build();
        Integer billingType = productionMachineConfigService.getBillingType(machineConfigInVO);
        
        // 默认一日一结的信息
        ProductTicketPO productTicketPO = new ProductTicketPO();
        productTicketPO.setMachineName(dailySettlementMaterialInVO.getMachineName());
        productTicketPO.setProduceDate(dailySettlementMaterialInVO.getProduceDate());
        productTicketPO.setShift(dailySettlementMaterialInVO.getShift());


        if (dailySettlementMaterialInVO != null && CollectionUtil.isNotEmpty(dailySettlementMaterialInVO.getDailySettlementMaterials())) {
            dailySettlementMaterialInVO.getDailySettlementMaterials().forEach(dailySettlementMaterial -> {
                // 修改物料剩余数量
                ProductMaterialPO materialPO = getById(dailySettlementMaterial.getId());
                if (materialPO == null) {
                    throw new CommonException("物料不存在");
                }
                if (MaterialEnum.IN_LIBRARY.getCode().equals(materialPO.getStatus())) {
                    throw new CommonException("物料已下料，请勿重复操作");
                }

                BigDecimal consumptionQuantity = materialPO.getLoadingQuantity().subtract(dailySettlementMaterial.getRemainingQuantity());

                if (consumptionQuantity.compareTo(materialPO.getLoadingQuantity()) > 0) {
                    throw new CommonException("消耗数量" + consumptionQuantity + "不得大于领用数量" + materialPO.getLoadingQuantity());
                }

                materialPO.setConsumptionQuantity(consumptionQuantity);
                materialPO.setRemainingQuantity(dailySettlementMaterial.getRemainingQuantity());
                materialPO.setLoadingQuantity(materialPO.getRemainingQuantity());
                materialPO.setUpdateTime(new Date());
                materialPO.setUpdateBy(SecurityUtil.getUserId());
                // 用完了
                if (materialPO.getRemainingQuantity().compareTo(BigDecimal.ZERO) == 0) {
                    materialPO.setStatus(MaterialEnum.IN_LIBRARY.getCode());
                    materialPO.setDownTime(new Date());
                }
                productMaterialService.updateById(materialPO);

                String LOCK_KEY = RedisCacheConstant.UPDATE_MATERIAL + materialPO.getMaterialBarcodeNo();
                
                if (billingType.equals(ProductConfigBillingTypeEnum.ONE_ORDER.getIntCode())) {
                    // 一单一结 取物料的生产工单id
                    ProductTicketPO pt = productTicketService.getById(materialPO.getProductTicketId());
                    productMaterialService.dailySettlementMaterial(LOCK_KEY, materialPO,pt,billingType);
                } else {
                    // 一日一结  后续再分配
                    productMaterialService.dailySettlementMaterial(LOCK_KEY, materialPO,productTicketPO,billingType);
                }
                
            });
        }
        // 更新日结信息
        ProductMachineDayDTO productMachineDayDTO = new ProductMachineDayDTO();
        productMachineDayDTO.setMachineName(dailySettlementMaterialInVO.getMachineName());
        productMachineDayDTO.setProduceDate(dailySettlementMaterialInVO.getProduceDate());
        productMachineDayDTO.setShift(dailySettlementMaterialInVO.getShift());
        productMachineDayService.finishProductMachineDay(productMachineDayDTO);

        // 只是增加返回值给前端 与上面逻辑没关系
        GetMaterialDailySettlementInVO getMaterialDailySettlementInVO = new GetMaterialDailySettlementInVO();
        getMaterialDailySettlementInVO.setMachineName(dailySettlementMaterialInVO.getMachineName());
        getMaterialDailySettlementInVO.setProduceDate(dailySettlementMaterialInVO.getProduceDate());
        getMaterialDailySettlementInVO.setShift(dailySettlementMaterialInVO.getShift());
        return getMaterialDailySettlementApp(getMaterialDailySettlementInVO);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void dailySettlementMaterial(String redisLockKey,
        ProductMaterialPO materialPO, ProductTicketPO productTicketPO, Integer billingType) {
        Snowflake snowflake = new Snowflake(1,1);
        String nextIdStr = snowflake.nextIdStr();

        // 保存栈板操作记录
        ProductMaterialOperationRecordsPO productMaterialOperationRecordsPO = saveMaterialOperationRecords(materialPO,
            productTicketPO, MaterialOperateTypeEnum.DAILY_SETTLEMENT.getCode(), materialPO.getMaterialBarcodeNo(),
            productTicketPO.getMachineStopNo(), null, nextIdStr);

        UpdateMaterialDTO updateMaterialDTO = new UpdateMaterialDTO();
        updateMaterialDTO.setMaterialOperationRecordsId(productMaterialOperationRecordsPO.getId());
        updateMaterialDTO.setBillingType(billingType);
        updateMaterialDTO.setFlag(1);
        updateMaterial(materialPO, productTicketPO,updateMaterialDTO);

        updateById(materialPO);
    }

    @Override
    public List<GetMaterialDailySettlementOutVO>
        getMaterialDailySettlement(GetMaterialDailySettlementInVO getMaterialDailySettlementInVO) {
        // 查询物料消耗数量
        ProductMachineMaterialRecordDTO productMachineMaterialRecordDTO = new ProductMachineMaterialRecordDTO();
        productMachineMaterialRecordDTO.setMachineName(getMaterialDailySettlementInVO.getMachineName());
        productMachineMaterialRecordDTO.setProduceDate(getMaterialDailySettlementInVO.getProduceDate());
        productMachineMaterialRecordDTO.setShift(getMaterialDailySettlementInVO.getShift());
        productMachineMaterialRecordDTO.setMaterialBarcodeNo(getMaterialDailySettlementInVO.getMaterialBarcodeNo());
        productMachineMaterialRecordDTO.setMaterialCode(getMaterialDailySettlementInVO.getMaterialCode());
        productMachineMaterialRecordDTO.setMaterialName(getMaterialDailySettlementInVO.getMaterialName());

        List<ProductMachineMaterialRecordPO> consumptionQuantity = productMachineMaterialRecordService.getConsumptionQuantity(productMachineMaterialRecordDTO);

        if (CollectionUtil.isNotEmpty(consumptionQuantity)) {
            return BeanUtil.copyToList(consumptionQuantity, GetMaterialDailySettlementOutVO.class);
        }
        return null;
    }

    @Override
    public void batchDeductionMaterial(
        List<ProductMachineMaterialApportionmentPO> productMachineMaterialApportionmentPOList,
        ProductTicketPO productTicketPO,String prefix) {
        Long productTicketId = productMachineMaterialApportionmentPOList.get(0).getProductTicketId();
        String upRecordId =
                "PLKL-" +  prefix + "-" + String.format("%010d", productTicketId);

        String uuid = UUID.randomUUID().toString();
        InventoryUpdateToErpInDTO inventoryUpdateToErpInDTO = new InventoryUpdateToErpInDTO();
        inventoryUpdateToErpInDTO.setId(uuid);
        InventoryUpdateToErpInDTO.DataKeyDTO dataKeyDTO = new InventoryUpdateToErpInDTO.DataKeyDTO();
        // 获得机台的工序，所属工厂信息
        ProductionMachineOutVO productionMachineOutVO = productionMachineService
                .getProductionMachineByErpName(productTicketPO.getMachineName(), productTicketPO.getProcess());
        dataKeyDTO.setEntId(productionMachineOutVO.getFactoryCode());
        dataKeyDTO.setCompanyId(productionMachineOutVO.getEnterpriseNumbers());
        inventoryUpdateToErpInDTO.setDataKey(dataKeyDTO);
        InventoryUpdateToErpInDTO.PayloadDTO payloadDTO = new InventoryUpdateToErpInDTO.PayloadDTO();
        InventoryUpdateToErpInDTO.FieldDTO fieldDTO = new InventoryUpdateToErpInDTO.FieldDTO();
        fieldDTO.setDocTypeNo("S319"); // S319 扣料 S325 退料
        fieldDTO.setStatus("1"); // 1 扣料 3 退料
        fieldDTO.setSfda014(upRecordId);
        fieldDTO.setDocNo(uuid);
        // 根据id查询用户工号
        UserDTO userDTO = ia01Service.getUserInfoById(SecurityUtil.getUserId());
        fieldDTO.setApplicantNo(userDTO.getWorkcode());
        fieldDTO.setWorkstationNo(productionMachineOutVO.getProcessCode());
        String dateStr = updateReportDate();
        fieldDTO.setCreateDate(dateStr);
        payloadDTO.setFieldDTO(fieldDTO);

        List<InventoryUpdateToErpInDTO.WoItemDetailDTO> woItemDetailDTOS = Lists.newArrayList();
        BigDecimal sumQty = BigDecimal.ZERO;
        productMachineMaterialApportionmentPOList.forEach(productMachineMaterialApportionmentPO -> {
            InventoryUpdateToErpInDTO.WoItemDetailDTO woItemDetailDTO = new InventoryUpdateToErpInDTO.WoItemDetailDTO();
            woItemDetailDTO.setWoNo(productTicketPO.getPlanTicketNo());
            woItemDetailDTO.setSfbaseq(productMachineMaterialApportionmentPO.getMaterialSeq());
            woItemDetailDTO.setItemNo(productMachineMaterialApportionmentPO.getMaterialCode());
            woItemDetailDTO.setUnitNo(productMachineMaterialApportionmentPO.getMaterialUnit());

            // 部位传到erp
            woItemDetailDTO.setSfba002(productMachineMaterialApportionmentPO.getMaterialPlace());
            woItemDetailDTO.setSfba003(productionMachineOutVO.getProcessCode());

            if (SecurityUtil.getCompanySite().equals("SITE-16")) {
                woItemDetailDTO.setLocationNo(productMachineMaterialApportionmentPO.getStorageNo());
            }

            if (productMachineMaterialApportionmentPO.getConsumptionQuantity().compareTo(BigDecimal.ZERO) == 0) {
                log.info("物料{}的消耗数量为0，不扣料", productMachineMaterialApportionmentPO.getMaterialCode());
                return;
            }
            woItemDetailDTO.setQty(productMachineMaterialApportionmentPO.getConsumptionQuantity());
            sumQty.add(woItemDetailDTO.getQty());
            if (SecurityUtil.getCompanySite().equals("SITE-16")) {
                woItemDetailDTO.setWarehouseNo( "X002");
            } else {
                woItemDetailDTO.setWarehouseNo("X001");
            }
            woItemDetailDTO.setInputDatetime(dateStr);
            woItemDetailDTO.setLotNo(productMachineMaterialApportionmentPO.getPurchaseBatch());
            // 目前都是扣料
            woItemDetailDTO.setPositiveNegative("-1");
            woItemDetailDTO.setIssueToType("5");
            woItemDetailDTO.setSubType("0");
            woItemDetailDTO.setOpNo(productionMachineOutVO.getProcessCode());
            woItemDetailDTOS.add(woItemDetailDTO);
        });
        
        payloadDTO.setWoItemDetailDTOs(woItemDetailDTOS);
        inventoryUpdateToErpInDTO.setPayloads(Arrays.asList(payloadDTO));
        log.info("调用erp,更新库存信息");
        ResponseCodeOutVO responseCodeOutVO = iInventoryToErpService.update(inventoryUpdateToErpInDTO);

        // 保存记录
        Long userId = SecurityUtil.getUserId();
        taskExecutor.execute(() -> {
            ProductInterfaceRecordsPO productInterfaceRecordsPO = new ProductInterfaceRecordsPO();
            productInterfaceRecordsPO.setProductTicketId(productTicketPO.getId());
            productInterfaceRecordsPO.setCompanyCode(productTicketPO.getCompanyCode());
            productInterfaceRecordsPO.setMainInfo(upRecordId);
            productInterfaceRecordsPO.setBusinessType(InterfaceBusinessEnum.UPDATE_MATERIAL.getCode());
            productInterfaceRecordsPO.setResponse(responseCodeOutVO.getMessage());
            productInterfaceRecordsPO.setResult(Integer.valueOf(responseCodeOutVO.getCode()));
            productInterfaceRecordsPO.setCreateBy(userId);
            productInterfaceRecordsPO.setUpdateBy(userId);
            productInterfaceRecordsService.save(productInterfaceRecordsPO);
        });
        if (responseCodeOutVO.getCode().equals(BooleanEnum.FALSE.getCode())) {
            if (!responseCodeOutVO.getMessage().contains("单号已经存在")) {
                throw new CommonException(responseCodeOutVO.getMessage());
            }

            // 去ERP查询已经存在的单号
            List<SfdaTPO> sfdaTPOs = sfdaTService.getByMesDocNo(upRecordId);
            SfdaTPO sfdaTPO = null;
            if (CollectionUtil.isNotEmpty(sfdaTPOs)) {
                sfdaTPO = sfdaTPOs.get(0);
            }
            if (sfdaTPO == null) {
                log.error("ERP中不存在单号：{}", upRecordId);
                throw new CommonException(responseCodeOutVO.getMessage());
            }

            // 再去校验数量
            List<SfdcTPO> sfdcTPOS = sfdcTService.getBySfdcdocno(sfdaTPO.getSfdadocno());
            if (CollectionUtil.isEmpty(sfdcTPOS)) {
                log.error("sfdc中不存在单号：{}", sfdaTPO.getSfdadocno());
                throw new CommonException(responseCodeOutVO.getMessage());
            }

            BigDecimal sum = sfdcTPOS.stream()
                    .map(SfdcTPO::getSfdc007)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (sum.compareTo(sumQty) == 0) {
                responseCodeOutVO.setCode(BooleanEnum.TRUE.getCode());
                responseCodeOutVO.setMessage(sfdaTPO.getSfdadocno());
            } else {
                responseCodeOutVO.setMessage("单号已经存在,且两次扣料数据不同，" +
                        "已存在的扣料是" + sumQty + "，新扣料是" + sum);
                throw new CommonException(responseCodeOutVO.getMessage());
            }
        }


        // 调用wms系统，推动消耗数量
        String nextIdStr = responseCodeOutVO.getMessage();
        List<UpdateBarcodeStoreDTO> updateBarcodeStoreDTOList =
            setUpdateBarcodeStoreDTOS(productMachineMaterialApportionmentPOList, nextIdStr, -1);
        srmBarcodeStoreService.updateBarcodeStores(updateBarcodeStoreDTOList);

        // 更新扣料单号
        productMachineMaterialApportionmentPOList.forEach(productMachineMaterialApportionmentPO -> {
            productMachineMaterialApportionmentPO.setDeductionNo(responseCodeOutVO.getMessage());
        });
        productMachineMaterialApportionmentService.updateBatchById(productMachineMaterialApportionmentPOList);

        // 更新扣料记录
        List<ProductMaterialPO> productMaterialPOS =
            productMaterialService.listByIds(productMachineMaterialApportionmentPOList.stream()
                .map(ProductMachineMaterialApportionmentPO::getMaterialId).collect(Collectors.toList()));
        productMaterialPOS.forEach(productMaterialPO -> {
            productMaterialPO.setDeductionNo(responseCodeOutVO.getMessage());
        });
        productMaterialService.updateBatchById(productMaterialPOS);

        List<ProductMaterialOperationRecordsPO> productMaterialOperationRecordsPOS =
            productMaterialOperationRecordsService.listByIds(productMachineMaterialApportionmentPOList.stream()
                .map(ProductMachineMaterialApportionmentPO::getMaterialOperationId).collect(Collectors.toList()));
        productMaterialOperationRecordsPOS.forEach(productMaterialOperationRecordsPO -> {
            productMaterialOperationRecordsPO.setDeductionNo(responseCodeOutVO.getMessage());
        });
        productMaterialOperationRecordsService.updateBatchById(productMaterialOperationRecordsPOS);

    }

    @Override
    public List<UpdateBarcodeStoreDTO> setUpdateBarcodeStoreDTOS(
        List<ProductMachineMaterialApportionmentPO> productMachineMaterialApportionmentPOList, String nextIdStr,Integer flag) {
        List<UpdateBarcodeStoreDTO> updateBarcodeStoreDTOList = Lists.newArrayList();
        productMachineMaterialApportionmentPOList.forEach(productMachineMaterialApportionmentPO -> {
            UpdateBarcodeStoreDTO updateBarcodeStoreDTO = new UpdateBarcodeStoreDTO();
            SrmBarcodeStoreDTO srmBarcodeStoreDTO =
                    srmBarcodeStoreService.getXBCSrmBarcodeByBarcodeNo(productMachineMaterialApportionmentPO.getMaterialBarcodeNo());
            if (srmBarcodeStoreDTO == null) {
                throw new CommonException("查询不到对应线边仓物料信息");
            }
            // 基本信息
            setBaseInfo(updateBarcodeStoreDTO);
            // srm_bar_store_change_body条码库存调整单身档
            setSrmBarStoreChangeHead(updateBarcodeStoreDTO, nextIdStr);
            // srm_bar_store_change_head条码库存调整单头档
            setSrmBarStoreChangeBody(productMachineMaterialApportionmentPO.getConsumptionQuantity(), updateBarcodeStoreDTO,srmBarcodeStoreDTO);
            // srm_barcode_change条码异动档
            setSrmBarcodeChange(updateBarcodeStoreDTO,srmBarcodeStoreDTO, nextIdStr,flag);
            // srm_barcode_store条码库存档
            updateBarcodeStoreDTO.setId(srmBarcodeStoreDTO.getId());
            updateBarcodeStoreDTO.setConsumptionQuantity(productMachineMaterialApportionmentPO.getConsumptionQuantity());
            updateBarcodeStoreDTOList.add(updateBarcodeStoreDTO);
        });
        return updateBarcodeStoreDTOList;
    }

    @Override
    public List<GetMaterialDailySettlementOutVO> getMaterialDailySettlementApp(GetMaterialDailySettlementInVO getMaterialDailySettlementInVO) {
        // 查询物料消耗数量
        ProductMachineMaterialRecordDTO productMachineMaterialRecordDTO = new ProductMachineMaterialRecordDTO();
        productMachineMaterialRecordDTO.setMachineName(getMaterialDailySettlementInVO.getMachineName());
        productMachineMaterialRecordDTO.setProduceDate(getMaterialDailySettlementInVO.getProduceDate());
        productMachineMaterialRecordDTO.setShift(getMaterialDailySettlementInVO.getShift());
        List<ProductMachineMaterialRecordPO> consumptionQuantity = productMachineMaterialRecordService.getConsumptionQuantityApp(productMachineMaterialRecordDTO);

        if (CollectionUtil.isNotEmpty(consumptionQuantity)) {
            return BeanUtil.copyToList(consumptionQuantity, GetMaterialDailySettlementOutVO.class);
        }
        return null;
    }


    private ResponseCodeOutVO updateToErp(ProductMaterialPO productMaterialPO, ProductTicketPO productTicketPO, UpdateMaterialDTO updateMaterialDTO, SrmBarcodeDetailPO srmBarcodeDetailPO) {
        // 获得这个物料的最新的上料记录id
        Long latestRecordId;
        if (updateMaterialDTO.getKey() != null) {
            latestRecordId = updateMaterialDTO.getKey();
        } else {
            latestRecordId = productMaterialOperationRecordsService.getLatestRecordId(productMaterialPO.getId());
        }
        String upRecordId =
            "KL-" + updateMaterialDTO.getOperateType() + "-" + String.format("%010d", latestRecordId) + "-" + productMaterialPO.getId();

        InventoryUpdateToErpInDTO inventoryUpdateToErpInDTO = new InventoryUpdateToErpInDTO();
        inventoryUpdateToErpInDTO.setId(updateMaterialDTO.getNextIdStr());
        InventoryUpdateToErpInDTO.DataKeyDTO dataKeyDTO = new InventoryUpdateToErpInDTO.DataKeyDTO();
        // 获得机台的工序，所属工厂信息
        ProductionMachineOutVO productionMachineOutVO = productionMachineService
            .getProductionMachineByErpName(productTicketPO.getMachineName(), productTicketPO.getProcess());
        dataKeyDTO.setEntId(productionMachineOutVO.getFactoryCode());
        dataKeyDTO.setCompanyId(productionMachineOutVO.getEnterpriseNumbers());
        inventoryUpdateToErpInDTO.setDataKey(dataKeyDTO);
        InventoryUpdateToErpInDTO.PayloadDTO payloadDTO = new InventoryUpdateToErpInDTO.PayloadDTO();
        InventoryUpdateToErpInDTO.FieldDTO fieldDTO = new InventoryUpdateToErpInDTO.FieldDTO();
        if (updateMaterialDTO.getFlag().equals(UpdateMaterialTypeEnum.SUBTRACT.getIntCode())) {
            fieldDTO.setDocTypeNo("S319"); // S319 扣料 S325 退料
            fieldDTO.setStatus("1"); // 1 扣料 3 退料
        } else {
            fieldDTO.setDocTypeNo("S325"); // S319 扣料 S325 退料
            fieldDTO.setStatus("3"); // 1 扣料 3 退料
        }
        fieldDTO.setSfda014(upRecordId);
        fieldDTO.setDocNo(updateMaterialDTO.getNextIdStr());
        // 根据id查询用户工号
        UserDTO userDTO = ia01Service.getUserInfoById(SecurityUtil.getUserId());
        fieldDTO.setApplicantNo(userDTO.getWorkcode());
        fieldDTO.setWorkstationNo(productionMachineOutVO.getProcessCode());
        String dateStr = updateReportDate();
        fieldDTO.setCreateDate(dateStr);
        payloadDTO.setFieldDTO(fieldDTO);
        InventoryUpdateToErpInDTO.WoItemDetailDTO woItemDetailDTO = new InventoryUpdateToErpInDTO.WoItemDetailDTO();
        woItemDetailDTO.setWoNo(productTicketPO.getPlanTicketNo());
        woItemDetailDTO.setItemNo(productMaterialPO.getMaterialCode());
        woItemDetailDTO.setUnitNo(productMaterialPO.getMaterialUnit());

        // 部位传到erp
        woItemDetailDTO.setSfba002(productMaterialPO.getMaterialPlace());
        woItemDetailDTO.setSfba003(productionMachineOutVO.getProcessCode());
        woItemDetailDTO.setStockCode(srmBarcodeDetailPO.getStockCode());
        woItemDetailDTO.setItemFeatureNo(srmBarcodeDetailPO.getProductCode());

        if (SecurityUtil.getCompanySite().equals("SITE-16")) {
            woItemDetailDTO.setLocationNo(productMaterialPO.getStorageNo());
        }

        woItemDetailDTO.setQty(updateMaterialDTO.getQty() != null ? updateMaterialDTO.getQty() : productMaterialPO.getConsumptionQuantity() );
        if (SecurityUtil.getCompanySite().equals("SITE-16")) {
            woItemDetailDTO.setWarehouseNo("X002");
        } else {
            woItemDetailDTO.setWarehouseNo("X001");
        }
        woItemDetailDTO.setInputDatetime(dateStr);
        woItemDetailDTO.setLotNo(productMaterialPO.getPurchaseBatch());
        woItemDetailDTO.setPositiveNegative(String.valueOf(updateMaterialDTO.getFlag()));
        woItemDetailDTO.setIssueToType("5");
        woItemDetailDTO.setSubType("0");
        woItemDetailDTO.setOpNo(productionMachineOutVO.getProcessCode());
        payloadDTO.setWoItemDetailDTOs(Arrays.asList(woItemDetailDTO));
        inventoryUpdateToErpInDTO.setPayloads(Arrays.asList(payloadDTO));
        log.info("调用erp,更新库存信息");
        ResponseCodeOutVO responseCodeOutVO = iInventoryToErpService.update(inventoryUpdateToErpInDTO);

        // 保存记录
        Long userId = SecurityUtil.getUserId();
        taskExecutor.execute(() -> {
            saveProductInterfaceRecords(productMaterialPO, productTicketPO, responseCodeOutVO, userId);
        });

        if (responseCodeOutVO.getCode().equals(BooleanEnum.FALSE.getCode())) {
            if (responseCodeOutVO.getMessage().contains("单号已经存在")) {
                // 去erp查询已经存在的单号
                List<SfdaTPO> sfdaTPOs = sfdaTService.getByMesDocNo(upRecordId);
                if (CollectionUtil.isNotEmpty(sfdaTPOs)) {
                    SfdaTPO sfdaTPO = sfdaTPOs.get(0);
                    // 再去校验数量
                    List<SfdcTPO> sfdcTPOS = sfdcTService.getBySfdcdocno(sfdaTPO.getSfdadocno());
                    if (CollectionUtil.isNotEmpty(sfdcTPOS)) {
                        BigDecimal sum = sfdcTPOS.stream().map(SfdcTPO::getSfdc007).reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (sum.compareTo(woItemDetailDTO.getQty()) == 0) {
                            responseCodeOutVO.setCode(BooleanEnum.TRUE.getCode());
                            responseCodeOutVO.setMessage(sfdaTPO.getSfdadocno());
                            return responseCodeOutVO;
                        }
                        responseCodeOutVO.setMessage("单号已经存在,且两次扣料数据不同，" +
                                "已存在的扣料是" + woItemDetailDTO.getQty() + "，新扣料是" + sum );
                    }
                }
            }
            throw new CommonException(responseCodeOutVO.getMessage());
        }
        return responseCodeOutVO;
    }

    @Override
//    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveProductInterfaceRecords(ProductMaterialPO productMaterialPO, ProductTicketPO productTicketPO,
                                            ResponseCodeOutVO responseCodeOutVO, Long userId) {
        ProductInterfaceRecordsPO productInterfaceRecordsPO = new ProductInterfaceRecordsPO();
        productInterfaceRecordsPO.setProductTicketId(productTicketPO.getId());
        productInterfaceRecordsPO.setCompanyCode(productTicketPO.getCompanyCode());
        productInterfaceRecordsPO.setMainInfo(String.valueOf(productMaterialPO.getMaterialBarcodeNo()));
        productInterfaceRecordsPO.setBusinessType(InterfaceBusinessEnum.UPDATE_MATERIAL.getCode());
        productInterfaceRecordsPO.setResponse(responseCodeOutVO.getMessage());
        productInterfaceRecordsPO.setResult(Integer.valueOf(responseCodeOutVO.getCode()));
        productInterfaceRecordsPO.setCreateBy(userId);
        productInterfaceRecordsPO.setUpdateBy(userId);
        productInterfaceRecordsService.save(productInterfaceRecordsPO);
    }

    @Override
    public Pagination<MaterialRecordsOutVO> getMaterialDownRecords(ProductTicketPageInVO productTicketPageInVO) {
        Page<MaterialRecordsOutVO> iPage =
                productMaterialOperationRecordsService.getMaterialDownRecords(productTicketPageInVO);
        return getMaterialInfoOutVOPagination(iPage);
    }

    /**
     * 换料
     */
    @GlobalTransactional(rollbackFor = Exception.class,timeoutMills = 300000)
    @Transactional
    @Override
    public Long changeMaterial(ChangeMaterialInVO changeMaterialInVO) {
        if (changeMaterialInVO.getUpMaterialInfo() == null) {
            throw new CommonException("请扫码获得物料信息");
        }
        ProductMaterialPO productMaterialPO = getById(changeMaterialInVO.getCurrentMaterialInfo().getId());
        if (productMaterialPO != null) {
            String materialBarcodeNo = productMaterialPO.getMaterialBarcodeNo();
            if (materialBarcodeNo.equals(changeMaterialInVO.getUpMaterialInfo().getMaterialBarcodeNo())) {
                throw new CommonException("相同物料不允许换料：" + materialBarcodeNo);
            }
        }

        // 校验机位信息
        MachineConfigInVO machineConfigInVO = new MachineConfigInVO();
        machineConfigInVO.setMachineName(changeMaterialInVO.getMachineName());
        Integer isp = productionMachineConfigService.getMachineParts(machineConfigInVO);
        if (isp.equals(Integer.valueOf(BooleanEnum.TRUE.getCode()))) {
            if (StringUtils.isBlank(changeMaterialInVO.getUpMaterialInfo().getParts())) {
                throw new CommonException("当前机台需要机位信息，请选择机位信息");
            }
        }

        String LOCK_KEY = RedisCacheConstant.UPDATE_MATERIAL + productMaterialPO.getMaterialBarcodeNo();
        productMaterialService.changeMaterial(LOCK_KEY,changeMaterialInVO, productMaterialPO);

        return productMaterialPO.getId();
    }

    @Override
    @RedisLock
    public void changeMaterial(String redisLockKey, ChangeMaterialInVO changeMaterialInVO, ProductMaterialPO productMaterialPO) {
        MaterialByBarcodeNoInVO materialByBarcodeNoInVO = new MaterialByBarcodeNoInVO();
        materialByBarcodeNoInVO.setMachineName(changeMaterialInVO.getMachineName());
        materialByBarcodeNoInVO.setProductTicketId(changeMaterialInVO.getProductTicketId());
        materialByBarcodeNoInVO.setBarcodeNo(changeMaterialInVO.getUpMaterialInfo().getMaterialBarcodeNo());
        MaterialInfoOutVO materialInfoOutVO = getMaterialByBarcodeNo(materialByBarcodeNoInVO);

        if (changeMaterialInVO.getUpMaterialInfo().getLoadingQuantity()
            .compareTo(materialInfoOutVO.getLoadingQuantity()) != 0) {
            throw new CommonException("上料数量和查询是数量不同：" + changeMaterialInVO.getUpMaterialInfo().getLoadingQuantity()
                + ":" + materialInfoOutVO.getLoadingQuantity());
        }
        if (BigDecimal.ZERO.compareTo(changeMaterialInVO.getUpMaterialInfo().getLoadingQuantity()) >= 0) {
            throw new CommonException("上料数量不能小于等于0");
        }
        if (changeMaterialInVO.getUpMaterialInfo().getMaterialPlace() == null
            || changeMaterialInVO.getUpMaterialInfo().getMaterialPlace().length() == 0) {
            throw new CommonException("物料部件（用途）不能为空");
        }
        ProductTicketPO productTicketPO = productTicketService.verify(changeMaterialInVO.getProductTicketId());

        Snowflake snowflake = new Snowflake(1, 1);
        String nextIdStr = snowflake.nextIdStr();
        // 保存栈板操作记录 -- 下料记录
        ProductMaterialOperationRecordsPO productMaterialOperationRecordsPO = saveMaterialOperationRecords(productMaterialPO, productTicketPO,
                MaterialOperateTypeEnum.DOWN_MATERIAL.getCode(), productMaterialPO.getMaterialBarcodeNo(),
                changeMaterialInVO.getMachineStopNo(), changeMaterialInVO.getCurrentMaterialInfo().getRemainingReason(),
                nextIdStr);

        // 将当前用料信息保存为下料状态及 更新数据
        productMaterialPO = updateProductMaterialPO(productMaterialPO, changeMaterialInVO.getCurrentMaterialInfo(),
            MaterialEnum.IN_LIBRARY.getCode(), productTicketPO, nextIdStr,productMaterialOperationRecordsPO);

        // 调拨单  扣料单
        productMaterialOperationRecordsPO.setTransferNo(productMaterialPO.getTransferNo());
        productMaterialOperationRecordsPO.setDeductionNo(productMaterialPO.getDeductionNo());
        productMaterialOperationRecordsPO.setOriginalQuantity(productMaterialPO.getLoadingQuantity());
        productMaterialOperationRecordsPO.setConsumptionQuantity(productMaterialPO.getConsumptionQuantity());
        productMaterialOperationRecordsPO.setRemainingQuantity(productMaterialPO.getRemainingQuantity());
        productMaterialOperationRecordsPO.setWarehouseNo(productMaterialPO.getWarehouseNo());
        productMaterialOperationRecordsPO.setWarehouseName(productMaterialPO.getWarehouseName());
        productMaterialOperationRecordsPO.setStorageNo(productMaterialPO.getStorageNo());
        productMaterialOperationRecordsPO.setStorageName(productMaterialPO.getStorageName());

        productMaterialOperationRecordsService.updateById(productMaterialOperationRecordsPO);

        // 校验
        verify(changeMaterialInVO.getUpMaterialInfo().getMaterialType(), changeMaterialInVO.getProductTicketId(),
                changeMaterialInVO.getMachineName(), changeMaterialInVO.getUpMaterialInfo().getMaterialBarcodeNo(),
                changeMaterialInVO.getUpMaterialInfo().getMaterialPlace(),
                changeMaterialInVO.getUpMaterialInfo().getMaterialCode(), productTicketPO.getProcessType(), productTicketPO.getProcessCode());

        // 将上料信息保存为使用中状态
        ProductMaterialPO productMaterialPONew = saveProductMaterialPO(materialInfoOutVO, productTicketPO,
            changeMaterialInVO.getUpMaterialInfo(), MaterialEnum.IN_PRODUCTION.getCode());

        // 保存栈板操作记录 -- 上料记录
        saveMaterialOperationRecords(productMaterialPONew, productTicketPO,
            MaterialOperateTypeEnum.UP_MATERIAL.getCode(),
            changeMaterialInVO.getUpMaterialInfo().getMaterialBarcodeNo(), changeMaterialInVO.getMachineStopNo(), null,
            null);
    }
//
//    @Override
//    public void updateToWmsTest(String barcord,Integer flag,BigDecimal consumptionQuantity) {
//        UpdateBarcodeStoreDTO updateBarcodeStoreDTO = new UpdateBarcodeStoreDTO();
//        Snowflake snowflake = new Snowflake(1,1);
//        String nextIdStr = snowflake.nextIdStr();
//        SrmBarcodeStoreDTO srmBarcodeStoreDTO =
//                srmBarcodeStoreService.getXBCSrmBarcodeByBarcodeNo(barcord);
//        if (srmBarcodeStoreDTO == null) {
//            throw new CommonException("查询不到对应线边仓物料信息");
//        }
//        // 基本信息
//        updateBarcodeStoreDTO.setEnt("100");
//        updateBarcodeStoreDTO.setSite(SecurityUtil.getCompanySite());
//        updateBarcodeStoreDTO.setCreateBy(String.valueOf(SecurityUtil.getUserId()));
//        updateBarcodeStoreDTO.setCreateDate(new java.util.Date());
//        updateBarcodeStoreDTO.setUpdateBy(String.valueOf(SecurityUtil.getUserId()));
//        updateBarcodeStoreDTO.setUpdateDate(new java.util.Date());
//        updateBarcodeStoreDTO.setDelFlag(BooleanEnum.FALSE.getCode());
//        // srm_bar_store_change_body条码库存调整单身档
//        updateBarcodeStoreDTO.setDocNo(nextIdStr);
//        updateBarcodeStoreDTO.setConfirmDate(new java.util.Date());
//        updateBarcodeStoreDTO.setConfirmCode("Y");
//        updateBarcodeStoreDTO.setCreateOffice("新MES提交，自动审核通过");
//        updateBarcodeStoreDTO.setDeptName(DEPT_NAME);
//        updateBarcodeStoreDTO.setOwner(String.valueOf(SecurityUtil.getUserId()));
//        updateBarcodeStoreDTO.setStatus("Y");
//        updateBarcodeStoreDTO.setRemarks("新MES提交，扣除生产中使用的物料");
//        // updateBarcodeStoreDTO.setExtension();
//        updateBarcodeStoreDTO.setConfirmBy(String.valueOf(SecurityUtil.getUserId()));
//        // srm_bar_store_change_head条码库存调整单头档
//        updateBarcodeStoreDTO.setSeq(BigDecimal.ONE);
//        updateBarcodeStoreDTO.setBarcodeNo(srmBarcodeStoreDTO.getBarcodeNo());
//        updateBarcodeStoreDTO.setWarehouseNo(srmBarcodeStoreDTO.getWarehouseNo());
//        updateBarcodeStoreDTO.setStorageSpacesNo(srmBarcodeStoreDTO.getWarehouseNoId());
//        updateBarcodeStoreDTO.setLotNo(srmBarcodeStoreDTO.getLotNo());
//        updateBarcodeStoreDTO.setItemNo(srmBarcodeStoreDTO.getItemNo());
//        updateBarcodeStoreDTO.setQty(consumptionQuantity);
//
//        updateBarcodeStoreDTO.setDeptName(DEPT_NAME);
//        updateBarcodeStoreDTO.setOwner(String.valueOf(SecurityUtil.getUserId()));
//        updateBarcodeStoreDTO.setReferenceQty(BigDecimal.ZERO);
//        updateBarcodeStoreDTO.setErpLotNo(srmBarcodeStoreDTO.getLotNo());
//        // srm_barcode_change条码异动档
//        updateBarcodeStoreDTO.setWarehouseName(srmBarcodeStoreDTO.getWarehouseName());
//        updateBarcodeStoreDTO.setStorageSpacesName(srmBarcodeStoreDTO.getStorageSpacesName());
//        updateBarcodeStoreDTO.setBadStorage(srmBarcodeStoreDTO.getBadStorage());
//        updateBarcodeStoreDTO.setPlatform(srmBarcodeStoreDTO.getPlatform());
//        try {
//            updateBarcodeStoreDTO
//                    .setVldDate(cn.jihong.common.util.DateUtil.parseDate("9998-12-31", DatePattern.NORM_DATE_PATTERN));
//        } catch (ParseException e) {
//            log.error("异常信息", e);
//        }
//
//        updateBarcodeStoreDTO.setChagType(flag);
//        updateBarcodeStoreDTO.setSourceNo(nextIdStr);
//        updateBarcodeStoreDTO.setSourceItemNo(BigDecimal.ONE);
//        updateBarcodeStoreDTO.setTargetNo(nextIdStr);
//        updateBarcodeStoreDTO.setTargetItemNo(BigDecimal.ONE);
//        updateBarcodeStoreDTO.setPdaOpCode(SecurityUtil.getWorkcode());
//        try {
//            updateBarcodeStoreDTO.setScanDate(cn.jihong.common.util.DateUtil
//                    .parseDate(LocalDate.now().format(DatePattern.NORM_DATE_FORMATTER), DatePattern.NORM_DATE_PATTERN));
//            updateBarcodeStoreDTO.setGenTime(LocalDateTime.now().format(DatePattern.NORM_TIME_FORMATTER));
//        } catch (ParseException e) {
//            log.error("异常信息", e);
//        }
//        updateBarcodeStoreDTO.setIsCharged("Y");
//        updateBarcodeStoreDTO.setChagNumUni(srmBarcodeStoreDTO.getStockUnitNo());
//        updateBarcodeStoreDTO.setExchagRate(BigDecimal.ONE);
//
//        updateBarcodeStoreDTO.setErpChgCode("C");
//        updateBarcodeStoreDTO.setBarChgType("Z");
//        updateBarcodeStoreDTO.setCustomerNo(SecurityUtil.getCompanySite());
//        updateBarcodeStoreDTO.setCustomerName(SecurityUtil.getCompanyName());
//        updateBarcodeStoreDTO.setErpLotNo(srmBarcodeStoreDTO.getLotNo());
//        updateBarcodeStoreDTO.setStuffCode(srmBarcodeStoreDTO.getItemNo());
//        updateBarcodeStoreDTO.setItemName(srmBarcodeStoreDTO.getItemName());
//        updateBarcodeStoreDTO.setItemSpec(srmBarcodeStoreDTO.getItemSpec());
//        updateBarcodeStoreDTO.setIsVldDate("N");
//        updateBarcodeStoreDTO.setEffectiveDay(0);
//        updateBarcodeStoreDTO.setInNearDay(0);
//        updateBarcodeStoreDTO.setInNearControl("1");
//        updateBarcodeStoreDTO.setOutNearDay(0);
//        updateBarcodeStoreDTO.setOutNearControl("1");
//        updateBarcodeStoreDTO.setWarningDay(0);
//        updateBarcodeStoreDTO.setEffectiveType("2");
//        updateBarcodeStoreDTO.setContainCode(srmBarcodeStoreDTO.getContainCode());
//        updateBarcodeStoreDTO.setErpChgNo(srmBarcodeStoreDTO.getErpChgNo());
//        updateBarcodeStoreDTO.setErpChgLine(srmBarcodeStoreDTO.getErpChgLine());
//        updateBarcodeStoreDTO.setErpChgOrder(srmBarcodeStoreDTO.getErpChgOrder());
//        updateBarcodeStoreDTO.setStatus("Y");
//        updateBarcodeStoreDTO.setBarcode(srmBarcodeStoreDTO.getBarcodeNo());
//        updateBarcodeStoreDTO.setReferenceUnitNo(srmBarcodeStoreDTO.getReferenceUnitNo());
//        updateBarcodeStoreDTO.setReferenceUnitName(srmBarcodeStoreDTO.getReferenceUnitName());
//        updateBarcodeStoreDTO.setReferenceQty(srmBarcodeStoreDTO.getReferenceQty());
//        updateBarcodeStoreDTO.setPostDate(new java.util.Date());
//        updateBarcodeStoreDTO.setStockKeep(BigDecimal.ZERO);
//        updateBarcodeStoreDTO.setSourceBarStatus(srmBarcodeStoreDTO.getSourceBarStatus());
//        updateBarcodeStoreDTO.setTargetBarStatus(srmBarcodeStoreDTO.getTargetBarStatus());
//
//        updateBarcodeStoreDTO.setDeptName(DEPT_NAME);
//        updateBarcodeStoreDTO.setOwner(String.valueOf(SecurityUtil.getUserId()));
//        updateBarcodeStoreDTO.setReferenceQty(BigDecimal.ZERO);
//        updateBarcodeStoreDTO.setErpLotNo(srmBarcodeStoreDTO.getErpLotNo());
//        // srm_barcode_store条码库存档
//        updateBarcodeStoreDTO.setId(srmBarcodeStoreDTO.getId());
//        updateBarcodeStoreDTO.setConsumptionQuantity(consumptionQuantity);
//        srmBarcodeStoreService.updateBarcodeStore(updateBarcodeStoreDTO);
//    }


    /**
     * 查询物料类别列表
     */
    @Override
    public List<MaterialTypeoutVO> getMaterialTypeList(Long productTicketId) {
        List<BmbaTMaterialDTO> bmbaTMaterialList = getBmbaTMaterialList(productTicketId);
        if (CollectionUtil.isNotEmpty(bmbaTMaterialList)) {
            Map<String, List<BmbaTMaterialDTO>> materialMap =
                bmbaTMaterialList.stream().collect(Collectors.groupingBy(BmbaTMaterialDTO::getType));
            List<MaterialTypeoutVO> materialTypeoutVOS = materialMap.entrySet().stream().map(m -> {
                MaterialTypeoutVO materialTypeoutVO = new MaterialTypeoutVO();
                materialTypeoutVO.setMaterialType(m.getKey());
                List<BmbaTMaterialDTO> materialDTOS = m.getValue();
                if (CollectionUtil.isNotEmpty(materialDTOS)) {
                    List<MaterialTypeoutVO.MaterialPlace> materialPlaces = Lists.newArrayList();
                    for (int i = 0; i < materialDTOS.size(); i++) {
                        BmbaTMaterialDTO mm = materialDTOS.get(i);
                        MaterialTypeoutVO.MaterialPlace materialPlace = new MaterialTypeoutVO.MaterialPlace();
                        materialPlace.setPlace(mm.getPlace());
                        materialPlace
                            .setPlaceName(StringUtils.isBlank(mm.getPlaceName()) ? mm.getPlace() : mm.getPlaceName());
                        materialPlaces.add(materialPlace);
                    }
                    materialTypeoutVO.setMaterialPlaces(materialPlaces);
                }
                return materialTypeoutVO;
            }).distinct().collect(Collectors.toList());
            return materialTypeoutVOS;
        }
        return null;
    }

    @Override
    public MaterialInfoOutVO getMaterialInfo(Long id) {
        return BeanUtil.copyProperties(getById(id),MaterialInfoOutVO.class);
    }

    /**
     * 查询使用中的物料列表 -- 需要增加物料类别
     */
    @Override
    public GetMaterialListByTypeOutVO getMaterialListByType(Long productTicketId, String materialType) {
        GetMaterialListByTypeOutVO getMaterialListByTypeOutVO = new GetMaterialListByTypeOutVO();
        LambdaQueryWrapper<ProductMaterialPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProductMaterialPO::getProductTicketId,productTicketId)
                .eq(ProductMaterialPO::getMaterialType,materialType)
                // 正在使用中的
                .eq(ProductMaterialPO::getStatus,MaterialEnum.IN_PRODUCTION.getCode());
        List<ProductMaterialPO> productMaterialPOS = list(queryWrapper);
        List<MaterialInfoOutVO> materialInfoOutVOS = BeanUtil.copyToList(productMaterialPOS, MaterialInfoOutVO.class);
        getMaterialListByTypeOutVO.setCanAdd(true);
        if (CollectionUtil.isEmpty(materialInfoOutVOS)) {
            return getMaterialListByTypeOutVO;
        }
        getMaterialListByTypeOutVO.setMaterialInfoOutVOS(materialInfoOutVOS);
        List<BmbaTMaterialDTO> bmbaTMaterialDTOS = getBmbaTMaterialList(productTicketId);
        if (CollectionUtil.isNotEmpty(bmbaTMaterialDTOS)) {
            List<BmbaTMaterialDTO> bms = bmbaTMaterialDTOS.stream()
                .filter(b -> StringUtils.equals(materialType, b.getType())).collect(Collectors.toList());
            if (materialInfoOutVOS.size()>=bms.size()) {
                getMaterialListByTypeOutVO.setCanAdd(false);
            }
        }
        return getMaterialListByTypeOutVO;
    }

    private Pagination<MaterialRecordsOutVO> getMaterialInfoOutVOPagination(IPage page, Page<MaterialRecordsOutVO> iPage) {
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return Pagination.newInstance(null);
        }

        List<Long> userIds =
                iPage.getRecords().stream().map(MaterialRecordsOutVO::getCreateBy).distinct().collect(Collectors.toList());
        Map<Long, String> userMap =
                ia01Service.getUserInfoByIds(userIds).stream().collect(Collectors.toMap(UserDTO::getId, UserDTO::getName));

        return Pagination.newInstance(iPage.getRecords().stream().map(materialInfoOutVO -> {
            materialInfoOutVO.setCreaterName (userMap.get(materialInfoOutVO.getCreateBy()));
            return materialInfoOutVO;
        }).collect(Collectors.toList()), page);
    }

    /**
     * 根据物料类型，进行下料操作
     * @param saveMaterialInfoInVO
     */
    @Deprecated
    private void downMaterial(SaveMaterialInfoInVO saveMaterialInfoInVO) {
        LambdaQueryWrapper<ProductMaterialPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductMaterialPO::getMachineName, saveMaterialInfoInVO.getMachineName())
                .eq(ProductMaterialPO::getProductTicketId, saveMaterialInfoInVO.getProductTicketId())
                .eq(ProductMaterialPO::getMaterialType, saveMaterialInfoInVO.getUpMaterialInfo().getMaterialType())
                .eq(ProductMaterialPO::getStatus,Integer.valueOf(MaterialEnum.IN_PRODUCTION.getCode()));
        List<ProductMaterialPO> list = list(lambdaQueryWrapper);
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<ProductMaterialPO> productMaterialPOList = list.stream().map(productMaterialPO -> {
            productMaterialPO.setStatus(MaterialEnum.IN_LIBRARY.getCode());
            return productMaterialPO;
        }).collect(Collectors.toList());
        updateBatchById(productMaterialPOList);
    }


    private ProductMaterialPO saveProductMaterialPO(MaterialInfoOutVO materialInfoOutVO, ProductTicketPO productTicketPO,
                                                    SaveMaterialInfoInVO.MaterialInfo materialInfo, Integer status) {
        if (ObjectUtil.isNull(materialInfo)) {
            return null;
        }
        LambdaQueryWrapper<ProductMaterialPO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(ProductMaterialPO::getMaterialBarcodeNo,materialInfo.getMaterialBarcodeNo());
        List<ProductMaterialPO> productMaterialPOS = list(lambdaQueryWrapper);
        ProductMaterialPO productMaterialPO = null;
        if (CollectionUtil.isNotEmpty(productMaterialPOS)) {
            productMaterialPO = productMaterialPOS.get(0);
        } else{
            productMaterialPO = new ProductMaterialPO();
            BeanUtil.copyProperties(materialInfo, productMaterialPO);
            productMaterialPO.setCreateBy(SecurityUtil.getUserId());
        }
        productMaterialPO.setMachineName(productTicketPO.getMachineName());
        productMaterialPO.setProductTicketId(productTicketPO.getId());
        // 默认剩余数量等于上料数量
        productMaterialPO.setLoadingQuantity(materialInfo.getLoadingQuantity());
        productMaterialPO.setRemainingQuantity(materialInfo.getLoadingQuantity());
        // 消耗数量默认为0
        productMaterialPO.setConsumptionQuantity(BigDecimal.ZERO);
        productMaterialPO.setUpdateBy(SecurityUtil.getUserId());
        // 设置上料时间
        productMaterialPO.setLoadingTime(new java.util.Date());
        productMaterialPO.setStatus(status);
        productMaterialPO.setTransferNo(materialInfoOutVO.getTransferNo());
        productMaterialPO.setCompanyCode(productTicketPO.getCompanyCode());
        productMaterialPO.setParts(materialInfo.getParts());
        productMaterialPO.setPartsName(materialInfo.getPartsName());
        productMaterialPO.setMaterialPlace(materialInfo.getMaterialPlace());
        productMaterialPO.setMaterialPlaceName(materialInfo.getMaterialPlaceName());
        productMaterialPO.setWarehouseNo(materialInfoOutVO.getWarehouseNo());
        productMaterialPO.setWarehouseName(materialInfoOutVO.getWarehouseName());
        productMaterialPO.setStorageNo(materialInfoOutVO.getStorageNo());
        productMaterialPO.setStorageName(materialInfoOutVO.getStorageName());
        productMaterialPO.setPurchaseBatch(materialInfoOutVO.getPurchaseBatch());

        // 保存
        if (saveOrUpdate(productMaterialPO)) {
            return productMaterialPO;
        }
        return null;
    }

    /**
     * 将当前用料信息保存为下料状态及 更新数据
     * @param productMaterialPO
     * @param materialInfo
     * @param status
     * @param productTicketPO
     * @param nextIdStr
     * @param productMaterialOperationRecordsPO
     */
    private ProductMaterialPO updateProductMaterialPO(ProductMaterialPO productMaterialPO, ChangeMaterialInVO.MaterialInfo materialInfo, Integer status,
                                                      ProductTicketPO productTicketPO, String nextIdStr,
                                                      ProductMaterialOperationRecordsPO productMaterialOperationRecordsPO) {
        if (ObjectUtil.isNull(productMaterialPO)) {
            return null;
        }
        if (productMaterialPO.getStatus().equals(status)) {
            throw new CommonException("当前物料已下料，无法进行换料操作");
        }
        // 剩余数量不能大于实际的剩余数量
        if (materialInfo.getRemainingQuantity().compareTo(productMaterialPO.getRemainingQuantity()) == 1) {
            throw new CommonException(
                    "剩余数量" + materialInfo.getRemainingQuantity() + "不得大于实际的剩余数量" + productMaterialPO.getRemainingQuantity());
        }
        // 消耗数量 不得大于 剩余数量
        if (materialInfo.getConsumptionQuantity().compareTo(productMaterialPO.getRemainingQuantity()) == 1) {
            throw new CommonException(
                    "消耗数量" + materialInfo.getConsumptionQuantity() + "不得大于实际的剩余数量" + productMaterialPO.getRemainingQuantity());
        }
        // 领用数量不等于消耗数量+剩余数量
        if (!(materialInfo.getConsumptionQuantity().add(materialInfo.getRemainingQuantity())
            .compareTo(productMaterialPO.getLoadingQuantity()) == 0)) {
            throw new CommonException(
                    "消耗数量" + materialInfo.getConsumptionQuantity() +
                            " + 剩余数量" + materialInfo.getRemainingQuantity() + " = "
                            + materialInfo.getConsumptionQuantity().add(materialInfo.getRemainingQuantity())
                            + "不等于领用数量" + productMaterialPO.getLoadingQuantity());
        }
        productMaterialPO.setConsumptionQuantity(materialInfo.getConsumptionQuantity());
        productMaterialPO.setRemainingQuantity(materialInfo.getRemainingQuantity());
        productMaterialPO.setRemainingReason(materialInfo.getRemainingReason());
        productMaterialPO.setUpdateBy(SecurityUtil.getUserId());
        productMaterialPO.setStatus(status);
        productMaterialPO.setMachineStopNo(productMaterialPO.getMachineStopNo());
        // 设置下料时间
        productMaterialPO.setDownTime(new java.util.Date());

        // 调用erp接口，推送消耗数量
        if (productMaterialPO.getConsumptionQuantity().compareTo(BigDecimal.ZERO) == 1) {
            UpdateMaterialDTO updateMaterialDTO = new UpdateMaterialDTO();
            updateMaterialDTO.setFlag(UpdateMaterialTypeEnum.SUBTRACT.getIntCode());
            updateMaterialDTO.setOperateType(MaterialOperateTypeEnum.CHANGE_MATERIAL.getCode());
            updateMaterialDTO.setNextIdStr(nextIdStr);
            updateMaterialDTO.setKey(null);
            updateMaterialDTO.setMaterialOperationRecordsId(productMaterialOperationRecordsPO.getId());
            // 调用erp接口，推送消耗数量
            ResponseCodeOutVO responseCodeOutVO = updateMaterial(productMaterialPO, productTicketPO, updateMaterialDTO);
            productMaterialPO.setDeductionNo(responseCodeOutVO.getMessage());
        }
        updateById(productMaterialPO);
        return productMaterialPO;
    }


    @Override
    public Pagination<ProductMaterialPO> getListByProductTickIds(Long pageNum,Long pageSize,List<Long> productTickIdList,String materialCode) {
        LambdaQueryWrapper<ProductMaterialPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StringUtils.isNotBlank(materialCode),ProductMaterialPO::getMaterialCode,materialCode)
                .in(CollectionUtil.isNotEmpty(productTickIdList),ProductMaterialPO::getProductTicketId,productTickIdList).orderByDesc(ProductMaterialPO::getCreateTime);
        IPage<ProductMaterialPO> ipage = PageConvertor.toPage(pageNum,pageSize);
        IPage page = page(ipage, wrapper);
        if(CollectionUtil.isEmpty(page.getRecords())){
            return Pagination.newInstance(null,0,0);
        }
        return Pagination.newInstance(page.getRecords(),page);
    }

    @Override
    public Pagination<MaterialUseOutVO> getMaterialUseList(GetMaterialListPageInVO getMaterialListPageInVO) {
        if (StringUtils.isBlank(getMaterialListPageInVO.getCompanyCode())) {
            getMaterialListPageInVO.setCompanyCode(SecurityUtil.getCompanySite());
        }
        Page<MaterialUseOutVO> page =
            baseMapper.getMaterialUseList(getMaterialListPageInVO.getPage(), getMaterialListPageInVO);
        if(CollectionUtil.isEmpty(page.getRecords())){
            return Pagination.newInstance(null);
        }

        List<String> materialType = page.getRecords().stream().map(MaterialUseOutVO::getMaterialType).collect(Collectors.toList());
        List<RtaxlTVO> rtaxlTVOS = rtaxlTService.getByCodes(materialType);
        Map<String, String> rtaxlTMap = rtaxlTVOS.stream().collect(Collectors.toMap(RtaxlTVO::getRtaxl001, RtaxlTVO::getRtaxl003,(v1,v2)->v2));

        return Pagination.newInstance(page.getRecords().stream().map(r->{
            // 获得物料类别
            r.setMaterialTypeName(rtaxlTMap.get(r.getMaterialType()));
            r.setStatusName(MaterialEnum.getMaterialEnum(r.getStatus()).getName());
            return r;
        }).collect(Collectors.toList()), page);
    }

    @Override
    public MaterialUseOutVO getMaterialUseDetial(Long id) {
        return baseMapper.getMaterialUseDetial(id);
    }

    @Override
    public Pagination<MaterialUseRecordOutVO> getMaterialUseDetialList(ProductInfoPageInVO productInfoPageInVO) {
        Page<MaterialUseRecordOutVO> page = baseMapper.getMaterialUseDetialList(productInfoPageInVO.getPage(),
                productInfoPageInVO.getId());
        if (CollectionUtil.isEmpty(page.getRecords())) {
            return Pagination.newInstance(null);
        }
        List<Long> userIds =
                page.getRecords().stream().map(MaterialUseRecordOutVO::getCreateBy).distinct().collect(Collectors.toList());
        Map<Long, String> userMap =
                ia01Service.getUserInfoByIds(userIds).stream().collect(Collectors.toMap(UserDTO::getId, UserDTO::getName));
        return Pagination.newInstance(page.getRecords().stream().map(r->{
            if (r.getCreateBy() != null) {
                r.setCreaterName(userMap.get(r.getCreateBy()));
            }
            r.setOperationTypeName(MaterialOperateTypeEnum.getMaterialOperateTypeEnum(r.getOperationType()).getName());
            r.setTicketTypeName(TicketTypeEnum.getTicketTypeEnum(Integer.valueOf(r.getTicketType())).getCnName());
            return r;
        }).collect(Collectors.toList()), page);
    }

    @Override
    public List<MaterialInfoOutVO> getMaterialListByMachineName(GetByMachineNameInVO getByMachineNameInVO) {
        LambdaQueryWrapper<ProductMaterialPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProductMaterialPO::getMachineName,getByMachineNameInVO.getMachineName())
                // 正在使用中的
                .eq(ProductMaterialPO::getStatus,MaterialEnum.IN_PRODUCTION.getCode());
        List<ProductMaterialPO> productMaterialPOS = list(queryWrapper);
        return BeanUtil.copyToList(productMaterialPOS,MaterialInfoOutVO.class);
    }

    @Override
    public List<MaterialInfoOutVO> getMaterialListByMachineName(GetByMachineNameInVO getByMachineNameInVO,
        Integer status) {
        LambdaQueryWrapper<ProductMaterialPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProductMaterialPO::getMachineName, getByMachineNameInVO.getMachineName())
            // 正在使用中的
            .eq(ProductMaterialPO::getStatus, status);
        List<ProductMaterialPO> productMaterialPOS = list(queryWrapper);
        return BeanUtil.copyToList(productMaterialPOS, MaterialInfoOutVO.class);
    }

    /**
     * 查询暂存的物料列表
     */
    @Override
    public List<MaterialInfoOutVO> getStagingMaterialListByMachineName(GetByMachineNameInVO getByMachineNameInVO) {
        return getMaterialListByMachineName(getByMachineNameInVO,MaterialEnum.TEMPORARY_STORAGE.getCode());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long updateMaterialStatus(UpdateMaterialStatusInVO updateMaterialStatusInVO) {
        ProductMaterialPO materialPO = getById(updateMaterialStatusInVO.getId());
        ProductTicketPO productTicketPO = productTicketService.getById(updateMaterialStatusInVO.getProductTicketId());

        if (materialPO == null) {
            throw new CommonException("物料不存在");
        }
        if (!materialPO.getStatus().equals(MaterialEnum.TEMPORARY_STORAGE.getCode())) {
            throw new CommonException("物料已转产,请勿重复操作");
        }

        Integer status = null;
        // 判断类型
        if (MaterialOperateTypeEnum.UP_MATERIAL.getCode().equals(updateMaterialStatusInVO.getOperation())) {
            // 更新状态
            materialPO.setStatus(MaterialEnum.IN_LIBRARY.getCode());

            // 新的上料数量等于旧的剩余数量
            materialPO.setLoadingQuantity(materialPO.getRemainingQuantity());
            materialPO.setConsumptionQuantity(BigDecimal.ZERO);
            materialPO.setRemainingQuantity(materialPO.getRemainingQuantity());
            materialPO.setStatus(MaterialEnum.IN_PRODUCTION.getCode());
            materialPO.setProductTicketId(updateMaterialStatusInVO.getProductTicketId());
            materialPO.setMachineName(productTicketPO.getMachineName());

            // 校验当前工单能否上此物料
            log.info("校验暂存物料能否上料");
            BmbaTMaterialDTO materialDTOS =
                    getBmbaTMaterialDTO(productTicketPO, materialPO.getMaterialCode());
            if (ObjectUtil.isNull(materialDTOS)) {
                log.error("查询到的物料类别为空");
                throw new CommonException("工单:" + productTicketPO.getPlanTicketNo() + " \n工序:" + productTicketPO.getProcess() + " \n物料:"
                        + materialPO.getMaterialBarcodeNo() + "未开料");
            }
            validityPlace(materialPO.getMaterialType(), updateMaterialStatusInVO.getProductTicketId(),
                    materialPO.getMachineName(), materialPO.getMaterialPlace());

        }
        if (MaterialOperateTypeEnum.DOWN_MATERIAL.getCode().equals(updateMaterialStatusInVO.getOperation())) {
            status = MaterialEnum.IN_LIBRARY.getCode();
            // 更新状态
            materialPO.setStatus(status);
        }

        saveMaterialOperationRecords(materialPO, productTicketPO, updateMaterialStatusInVO.getOperation(),
                materialPO.getMaterialBarcodeNo(), productTicketPO.getMachineStopNo(),
                materialPO.getRemainingReason(),null);
        saveOrUpdate(materialPO);
        return updateMaterialStatusInVO.getId();
    }



    public void updateBarcodeStore(BigDecimal consumptionQuantity,String barcodeNo,
                                   String nextIdStr, Integer flag) {
        UpdateBarcodeStoreDTO updateBarcodeStoreDTO = new UpdateBarcodeStoreDTO();
        SrmBarcodeStoreDTO srmBarcodeStoreDTO =
            srmBarcodeStoreService.getXBCSrmBarcodeByBarcodeNo(barcodeNo);
        if (srmBarcodeStoreDTO == null) {
            throw new CommonException("查询不到对应线边仓物料信息");
        }
        // 基本信息
        setBaseInfo(updateBarcodeStoreDTO);
        // srm_bar_store_change_body条码库存调整单身档
        setSrmBarStoreChangeHead(updateBarcodeStoreDTO,nextIdStr);
        // srm_bar_store_change_head条码库存调整单头档
        setSrmBarStoreChangeBody(consumptionQuantity,  updateBarcodeStoreDTO,srmBarcodeStoreDTO);
        // srm_barcode_change条码异动档
        setSrmBarcodeChange( updateBarcodeStoreDTO,srmBarcodeStoreDTO,nextIdStr,flag);
        // srm_barcode_store条码库存档
        updateBarcodeStoreDTO.setId(srmBarcodeStoreDTO.getId());
        updateBarcodeStoreDTO.setConsumptionQuantity(consumptionQuantity);
        srmBarcodeStoreService.updateBarcodeStore(updateBarcodeStoreDTO);
    }

    private void setBaseInfo(UpdateBarcodeStoreDTO updateBarcodeStoreDTO) {
        // 基础信息
        updateBarcodeStoreDTO.setEnt("100");
        updateBarcodeStoreDTO.setSite(SecurityUtil.getCompanySite());
        updateBarcodeStoreDTO.setCreateBy(String.valueOf(SecurityUtil.getUserId()));
        updateBarcodeStoreDTO.setCreateDate(new java.util.Date());
        updateBarcodeStoreDTO.setUpdateBy(String.valueOf(SecurityUtil.getUserId()));
        updateBarcodeStoreDTO.setUpdateDate(new java.util.Date());
        updateBarcodeStoreDTO.setDelFlag(BooleanEnum.FALSE.getCode());
    }

    private void setSrmBarcodeChange(UpdateBarcodeStoreDTO updateBarcodeStoreDTO,
                                     SrmBarcodeStoreDTO srmBarcodeStoreDTO, String nextIdStr, Integer flag) {
        updateBarcodeStoreDTO.setWarehouseName(srmBarcodeStoreDTO.getWarehouseName());
        updateBarcodeStoreDTO.setStorageSpacesName(srmBarcodeStoreDTO.getStorageSpacesName());
        updateBarcodeStoreDTO.setBadStorage(srmBarcodeStoreDTO.getBadStorage());
        updateBarcodeStoreDTO.setPlatform(srmBarcodeStoreDTO.getPlatform());
        try {
            updateBarcodeStoreDTO
                .setVldDate(cn.jihong.common.util.DateUtil.parseDate("9998-12-31", DatePattern.NORM_DATE_PATTERN));
        } catch (ParseException e) {
            log.error("异常信息", e);
        }

        updateBarcodeStoreDTO.setChagType(flag);
        updateBarcodeStoreDTO.setSourceNo(nextIdStr);
        updateBarcodeStoreDTO.setSourceItemNo(BigDecimal.ONE);
        updateBarcodeStoreDTO.setTargetNo(nextIdStr);
        updateBarcodeStoreDTO.setTargetItemNo(BigDecimal.ONE);
        updateBarcodeStoreDTO.setPdaOpCode(SecurityUtil.getWorkcode());
        try {
            updateBarcodeStoreDTO.setScanDate(cn.jihong.common.util.DateUtil
                .parseDate(LocalDate.now().format(DatePattern.NORM_DATE_FORMATTER), DatePattern.NORM_DATE_PATTERN));
            updateBarcodeStoreDTO.setGenTime(LocalDateTime.now().format(DatePattern.NORM_TIME_FORMATTER));
        } catch (ParseException e) {
            log.error("异常信息", e);
        }
        updateBarcodeStoreDTO.setIsCharged("Y");
        updateBarcodeStoreDTO.setChagNumUni(srmBarcodeStoreDTO.getStockUnitNo());
        updateBarcodeStoreDTO.setExchagRate(BigDecimal.ONE);
//        updateBarcodeStoreDTO.setAppid();
//        updateBarcodeStoreDTO.setTimesTamp();
//        updateBarcodeStoreDTO.setAppmodule();
//        updateBarcodeStoreDTO.setAppSerialNum();
        updateBarcodeStoreDTO.setErpChgCode("C");
        updateBarcodeStoreDTO.setBarChgType("Z");
        updateBarcodeStoreDTO.setCustomerNo(SecurityUtil.getCompanySite());
        updateBarcodeStoreDTO.setCustomerName(SecurityUtil.getCompanyName());
        updateBarcodeStoreDTO.setErpLotNo(srmBarcodeStoreDTO.getLotNo());
        updateBarcodeStoreDTO.setStuffCode(srmBarcodeStoreDTO.getItemNo());
        updateBarcodeStoreDTO.setItemName(srmBarcodeStoreDTO.getItemName());
        updateBarcodeStoreDTO.setItemSpec(srmBarcodeStoreDTO.getItemSpec());
        updateBarcodeStoreDTO.setIsVldDate("N");
        updateBarcodeStoreDTO.setEffectiveDay(0);
        updateBarcodeStoreDTO.setInNearDay(0);
        updateBarcodeStoreDTO.setInNearControl("1");
        updateBarcodeStoreDTO.setOutNearDay(0);
        updateBarcodeStoreDTO.setOutNearControl("1");
        updateBarcodeStoreDTO.setWarningDay(0);
        updateBarcodeStoreDTO.setEffectiveType("2");
        updateBarcodeStoreDTO.setContainCode(srmBarcodeStoreDTO.getContainCode());
        updateBarcodeStoreDTO.setErpChgNo(srmBarcodeStoreDTO.getErpChgNo());
        updateBarcodeStoreDTO.setErpChgLine(srmBarcodeStoreDTO.getErpChgLine());
        updateBarcodeStoreDTO.setErpChgOrder(srmBarcodeStoreDTO.getErpChgOrder());
        updateBarcodeStoreDTO.setStatus("Y");
//         updateBarcodeStoreDTO.setApplyActuRelat("");
        updateBarcodeStoreDTO.setBarcode(srmBarcodeStoreDTO.getBarcodeNo());
        updateBarcodeStoreDTO.setReferenceUnitNo(srmBarcodeStoreDTO.getReferenceUnitNo());
        updateBarcodeStoreDTO.setReferenceUnitName(srmBarcodeStoreDTO.getReferenceUnitName());
        updateBarcodeStoreDTO.setReferenceQty(srmBarcodeStoreDTO.getReferenceQty());
        updateBarcodeStoreDTO.setPostDate(new java.util.Date());
        updateBarcodeStoreDTO.setStockKeep(BigDecimal.ZERO);
        updateBarcodeStoreDTO.setSourceBarStatus(srmBarcodeStoreDTO.getSourceBarStatus());
        updateBarcodeStoreDTO.setTargetBarStatus(srmBarcodeStoreDTO.getTargetBarStatus());

        updateBarcodeStoreDTO.setDeptName(DEPT_NAME);
        updateBarcodeStoreDTO.setOwner(String.valueOf(SecurityUtil.getUserId()));
        updateBarcodeStoreDTO.setReferenceQty(BigDecimal.ZERO);
        updateBarcodeStoreDTO.setErpLotNo(srmBarcodeStoreDTO.getErpLotNo());

    }

    private void setSrmBarStoreChangeBody(BigDecimal consumptionQuantity,
        UpdateBarcodeStoreDTO updateBarcodeStoreDTO, SrmBarcodeStoreDTO srmBarcodeStoreDTO) {

        updateBarcodeStoreDTO.setSeq(BigDecimal.ONE);
        updateBarcodeStoreDTO.setBarcodeNo(srmBarcodeStoreDTO.getBarcodeNo());
        updateBarcodeStoreDTO.setWarehouseNo(srmBarcodeStoreDTO.getWarehouseNo());
        updateBarcodeStoreDTO.setStorageSpacesNo(srmBarcodeStoreDTO.getWarehouseNoId());
        updateBarcodeStoreDTO.setLotNo(srmBarcodeStoreDTO.getLotNo());
        updateBarcodeStoreDTO.setItemNo(srmBarcodeStoreDTO.getItemNo());
        updateBarcodeStoreDTO.setQty(consumptionQuantity);

        updateBarcodeStoreDTO.setDeptName(DEPT_NAME);
        updateBarcodeStoreDTO.setOwner(String.valueOf(SecurityUtil.getUserId()));
        updateBarcodeStoreDTO.setReferenceQty(BigDecimal.ZERO);
        updateBarcodeStoreDTO.setErpLotNo(srmBarcodeStoreDTO.getLotNo());

    }

    private void setSrmBarStoreChangeHead(
        UpdateBarcodeStoreDTO updateBarcodeStoreDTO, String nextIdStr) {
        // 业务信息
        updateBarcodeStoreDTO.setDocNo(nextIdStr);
        updateBarcodeStoreDTO.setConfirmDate(new java.util.Date());
        updateBarcodeStoreDTO.setConfirmCode("Y");
        updateBarcodeStoreDTO.setCreateOffice("新MES提交，自动审核通过");
        updateBarcodeStoreDTO.setDeptName(DEPT_NAME);
        updateBarcodeStoreDTO.setOwner(String.valueOf(SecurityUtil.getUserId()));
        updateBarcodeStoreDTO.setStatus("Y");
        updateBarcodeStoreDTO.setRemarks("新MES提交，扣除生产中使用的物料");
        // updateBarcodeStoreDTO.setExtension();
        updateBarcodeStoreDTO.setConfirmBy(String.valueOf(SecurityUtil.getUserId()));
    }


    @Override
    public void updateToErpTest(Long productMaterialId) {
        Snowflake snowflake = new Snowflake(1, 1);
        String nextIdStr = snowflake.nextIdStr();

        ProductMaterialPO productMaterialPO = getById(productMaterialId);
        ProductTicketPO productTicketPO = productTicketService.getById(productMaterialPO.getProductTicketId());


        InventoryUpdateToErpInDTO inventoryUpdateToErpInDTO = new InventoryUpdateToErpInDTO();
        inventoryUpdateToErpInDTO.setId(nextIdStr);
        InventoryUpdateToErpInDTO.DataKeyDTO dataKeyDTO = new InventoryUpdateToErpInDTO.DataKeyDTO();
        // 获得机台的工序，所属工厂信息
        ProductionMachineOutVO productionMachineOutVO = productionMachineService
                .getProductionMachineByErpName(productTicketPO.getMachineName(), productTicketPO.getProcess());
        dataKeyDTO.setEntId(productionMachineOutVO.getFactoryCode());
        dataKeyDTO.setCompanyId(productionMachineOutVO.getEnterpriseNumbers());
        inventoryUpdateToErpInDTO.setDataKey(dataKeyDTO);
        InventoryUpdateToErpInDTO.PayloadDTO payloadDTO = new InventoryUpdateToErpInDTO.PayloadDTO();
        InventoryUpdateToErpInDTO.FieldDTO fieldDTO = new InventoryUpdateToErpInDTO.FieldDTO();
        fieldDTO.setDocTypeNo("S319");
        fieldDTO.setDocNo(nextIdStr);
        fieldDTO.setStatus("1");
        // 根据id查询用户工号
        UserDTO userDTO = ia01Service.getUserInfoById(SecurityUtil.getUserId());
        fieldDTO.setApplicantNo(userDTO.getWorkcode());
        fieldDTO.setWorkstationNo(productionMachineOutVO.getProcessCode());
        String dateStr = updateReportDate();
        fieldDTO.setCreateDate(dateStr);
        payloadDTO.setFieldDTO(fieldDTO);
        InventoryUpdateToErpInDTO.WoItemDetailDTO woItemDetailDTO = new InventoryUpdateToErpInDTO.WoItemDetailDTO();
        woItemDetailDTO.setWoNo(productTicketPO.getPlanTicketNo());
        woItemDetailDTO.setItemNo(productMaterialPO.getMaterialCode());
        woItemDetailDTO.setUnitNo(productMaterialPO.getMaterialUnit());

        woItemDetailDTO.setQty(productMaterialPO.getConsumptionQuantity());
        if (SecurityUtil.getCompanySite().equals("SITE-16")) {
            woItemDetailDTO.setWarehouseNo( "X002");
        } else {
            woItemDetailDTO.setWarehouseNo("X001");
        }
        woItemDetailDTO.setInputDatetime(dateStr);
        woItemDetailDTO.setLotNo(productMaterialPO.getPurchaseBatch());
        woItemDetailDTO.setPositiveNegative("-1");
        woItemDetailDTO.setIssueToType("5");
        woItemDetailDTO.setSubType("0");
        woItemDetailDTO.setOpNo(productionMachineOutVO.getProcessCode());
        payloadDTO.setWoItemDetailDTOs(Arrays.asList(woItemDetailDTO));
        inventoryUpdateToErpInDTO.setPayloads(Arrays.asList(payloadDTO));
        if (BooleanEnum.TRUE.getCode().equals(pushSwitchConfig.getErpUpdateMaterial())) {
            log.info("调用erp,更新库存信息");
            ResponseCodeOutVO responseCodeOutVO = iInventoryToErpService.update(inventoryUpdateToErpInDTO);

            // 保存记录
            Long userId = SecurityUtil.getUserId();
            taskExecutor.execute(() -> {
                saveProductInterfaceRecords(productMaterialPO, productTicketPO, responseCodeOutVO,userId);
            });

            if (responseCodeOutVO.getCode().equals(BooleanEnum.FALSE.getCode())) {
                throw new CommonException(responseCodeOutVO.getMessage());
            }

        }
    }

    @Override
    public List<MaterialTotalUseOutVO> getMaterialTotalUse(ProductTicketVO productTicketVO) {
        if (productTicketVO == null || StringUtils.isBlank(productTicketVO.getMachineName())) {
            throw new CommonException("机台名称不能为空");
        }
        ProductMachineTicketPO productMachineTicketPO =
            productMachineTicketService.getByMachineName(productTicketVO.getMachineName());
        if (productMachineTicketPO == null) {
            throw new CommonException("机台未在生产工单中，无法获取对应工单的累计用料信息：" + productTicketVO.getMachineName());
        }

        List<Integer> operationTypes = Lists.newArrayList();
        operationTypes.add(MaterialOperateTypeEnum.DOWN_MATERIAL.getIntCode());
        operationTypes.add(MaterialOperateTypeEnum.CHANGE_MATERIAL.getIntCode());
        operationTypes.add(MaterialOperateTypeEnum.TEMPORARY_STORAGE.getIntCode());
        List<MaterialTotalUseOutVO> materialTotalUseOutVOS = baseMapper.getMaterialTotalUse(
            productMachineTicketPO.getPlanTicketNo(), productTicketVO.getMachineName(), operationTypes);

        List<ProductTicketPO> listByPlanTicketNo = productTicketService
            .getListByPlanTicketNo(productMachineTicketPO.getPlanTicketNo(), productTicketVO.getMachineName());
        ProductTicketPO productTicketPO = null;
        if (CollectionUtil.isNotEmpty(listByPlanTicketNo)) {
            productTicketPO = listByPlanTicketNo.get(0);
        } else {
            throw new CommonException(
                "当前机台未生产工单：" + productTicketVO.getMachineName() + " - " + productMachineTicketPO.getPlanTicketNo());
        }
        List<SfbaTVO> billingQuantity =
            sfbaTService.getBillingQuantity(productTicketPO.getPlanTicketNo(), productTicketPO.getProcessCode());

        if (CollectionUtil.isEmpty(billingQuantity)) {
            log.error("查询不到开单数量");
            return materialTotalUseOutVOS;
        }
        Map<String,Map<String,BigDecimal>> materialCodeMap = Maps.newHashMap();
        Map<String, List<SfbaTVO>> erpMaterialCodeMap =
            billingQuantity.stream().collect(Collectors.groupingBy(SfbaTVO::getSfba005));
        erpMaterialCodeMap.entrySet().stream().forEach(p -> {
            Map<String,BigDecimal> materialPlaceMap = Maps.newHashMap();
            Map<String, List<SfbaTVO>> placeMap = p.getValue().stream().collect(Collectors.groupingBy(SfbaTVO::getSfba002));
            placeMap.entrySet().stream().forEach(place -> {
                BigDecimal reduce = place.getValue().stream().map(SfbaTVO::getSfba013).filter(ObjectUtil::isNotNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                materialPlaceMap.put(place.getKey(),reduce);
            });
            materialCodeMap.put(p.getKey(),materialPlaceMap);
        });

        return materialTotalUseOutVOS.stream().map(materialTotalUseOutVO -> {
            BigDecimal bigDecimal = BigDecimal.ZERO;
            Map<String, BigDecimal> stringBigDecimalMap = materialCodeMap.get(materialTotalUseOutVO.getMaterialCode());
            if (stringBigDecimalMap != null) {
                bigDecimal = stringBigDecimalMap.get(materialTotalUseOutVO.getMaterialPlace());
                materialTotalUseOutVO.setBillingPcsQuantity(bigDecimal == null ? BigDecimal.ZERO : bigDecimal);
            }
            return materialTotalUseOutVO;
        }).collect(Collectors.toList());
    }

    @Transactional
    @GlobalTransactional(rollbackFor = Exception.class,timeoutMills = 300000)
    @Override
    public void updateMaterialCount(UpdateMaterialInVO updateMaterialInVO) {
        ProductMaterialOperationRecordsPO productMaterialOperationRecordsPO =
            productMaterialOperationRecordsService.getById(updateMaterialInVO.getMaterialRecordId());
        ProductMaterialPO productMaterialPO = getById(productMaterialOperationRecordsPO.getProductMaterialId());
        ProductTicketPO productTicketPO = productTicketService.getById(productMaterialPO.getProductTicketId());

        // 校验 物料必须是在最新的一条才能修改
        List<ProductMaterialOperationRecordsPO> productMaterialOperationRecordsPOS =
            productMaterialOperationRecordsService.getByMateriaId(productMaterialPO.getId());
        ProductMaterialOperationRecordsPO productMaterialOperationRecords = productMaterialOperationRecordsPOS.get(0);
        if (!productMaterialOperationRecords.getId().equals(updateMaterialInVO.getMaterialRecordId())) {
            throw new CommonException("当前操作记录不是最后一条，不允许修改");
        }

        // 原来消耗数量
        BigDecimal consumptionQuantityOld = productMaterialOperationRecordsPO.getConsumptionQuantity();
        BigDecimal remainingQuantityOld = productMaterialOperationRecordsPO.getRemainingQuantity();

        String LOCK_KEY = RedisCacheConstant.UPDATE_MATERIAL + productMaterialPO.getMaterialBarcodeNo();
        productMaterialService.updateMateria(LOCK_KEY,updateMaterialInVO, productMaterialPO, productTicketPO, consumptionQuantityOld,
                remainingQuantityOld);
    }

    @Override
    @RedisLock
    public void updateMateria(String redisLockKey, UpdateMaterialInVO updateMaterialInVO, ProductMaterialPO productMaterialPO, ProductTicketPO productTicketPO, BigDecimal consumptionQuantityOld, BigDecimal remainingQuantityOld) {
        // 加上原来的消耗数量，减去新的消耗数量
        productMaterialPO.setConsumptionQuantity(BigDecimal.ZERO.subtract(consumptionQuantityOld));
        productMaterialPO.setRemainingQuantity(remainingQuantityOld.add(consumptionQuantityOld));
        saveMaterialOperationRecords(productMaterialPO, productTicketPO,
            MaterialOperateTypeEnum.DOWN_MATERIAL.getCode(), productMaterialPO.getMaterialBarcodeNo(),
            productTicketPO.getMachineStopNo(), productMaterialPO.getRemainingReason(), null);

        // 更新本地数据
        productMaterialPO.setConsumptionQuantity(updateMaterialInVO.getConsumptionQuantity());
        // 加上原来的消耗数量，减去新的消耗数量
        productMaterialPO.setRemainingQuantity(
            productMaterialPO.getRemainingQuantity().subtract(updateMaterialInVO.getConsumptionQuantity()));
        updateById(productMaterialPO);

        // 新增操作记录
        ProductMaterialOperationRecordsPO productMaterialOperationRecordsPO = saveMaterialOperationRecords(productMaterialPO, productTicketPO,
                MaterialOperateTypeEnum.DOWN_MATERIAL.getCode(), productMaterialPO.getMaterialBarcodeNo(),
                productTicketPO.getMachineStopNo(), productMaterialPO.getRemainingReason(), null);

        // 更新智物流数据

        Snowflake snowflake = new Snowflake(1, 1);
        String nextIdStr = snowflake.nextIdStr();
        BigDecimal different = consumptionQuantityOld.subtract(updateMaterialInVO.getConsumptionQuantity());
        if (different.compareTo(BigDecimal.ZERO) != 0) {
            // 调用erp接口，推送消耗数量
            // 比如 之前是 扣料200 现在 扣料300 则表示需要再扣料 (-100) 所以传给erp的是 -1 100 传给 wms 的是 -1 100
            // 比如 之前是 扣料300 现在 扣料200 则表示之前多扣料 (100) 所以传给erp的是 1 100 传给 wms 的是 1 100
            Integer flag = UpdateMaterialTypeEnum.SUBTRACT.getIntCode();
            if (different.compareTo(BigDecimal.ZERO) > 0) {
                flag = UpdateMaterialTypeEnum.ADD.getIntCode();
            }
            // 需要将差额转成正数
            productMaterialPO.setConsumptionQuantity(different.multiply(BigDecimal.valueOf(flag)));
            // 更新数据可以更新多次，所以这个幂等就不控制了，让其可以多次修改
            UpdateMaterialDTO updateMaterialDTO = new UpdateMaterialDTO();
            updateMaterialDTO.setFlag(flag);
            updateMaterialDTO.setOperateType(MaterialOperateTypeEnum.DOWN_MATERIAL.getCode());
            updateMaterialDTO.setNextIdStr(nextIdStr);
            updateMaterialDTO.setKey(System.currentTimeMillis());
            updateMaterialDTO.setMaterialOperationRecordsId(productMaterialOperationRecordsPO.getId());
            updateMaterial(productMaterialPO, productTicketPO,  updateMaterialDTO);
        }
    }

    private String updateReportDate() {
        // 有多个地方使用到类似的。注意看其他地方要不要改，按照方法名取搜索
        OoabTPO stopDate = ooabTService.getStopDate();
        if (stopDate != null) {
            String stopDateStr = stopDate.getOoab002();
            stopDateStr = stopDateStr.replace("/", "-");
            Date stopDateDate = DateUtil.parse(stopDateStr, DatePattern.NORM_DATE_PATTERN);
            // 比较日期
            Date currentDate = DateUtil.parse(DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN), DatePattern.NORM_DATE_PATTERN);
            log.info("截止日期为：" + stopDateStr);
            log.info("当前日期为：" + currentDate);
            int result = DateUtil.compare(stopDateDate, currentDate);
            if (result >= 0) {
                log.info("截止日期在今天之后，日期加一天");
                // 截止日期在今天之后，日期加一天
                return DateUtil.format(DateUtil.offsetDay(stopDateDate, 1), DatePattern.PURE_DATETIME_FORMAT);
            }
        }
        return DateUtil.format(new Date(), DatePattern.PURE_DATETIME_FORMAT);
    }

}
