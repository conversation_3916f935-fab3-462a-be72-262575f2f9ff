package cn.jihong.mes.production.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.jihong.calibre.review.server.api.model.dto.DefectDetermineDTO;
import cn.jihong.calibre.review.server.api.model.dto.UpdateDefectNumDTO;
import cn.jihong.calibre.review.server.api.model.enums.DefectDetermineStatusEnum;
import cn.jihong.calibre.review.server.api.model.enums.TicketStatusEnum;
import cn.jihong.calibre.review.server.api.model.vo.CreateBizFromProductionOutVO;
import cn.jihong.calibre.review.server.api.service.IQcDefectDetermineBizService;
import cn.jihong.common.enums.BooleanEnum;
import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.common.util.SecurityUtil;
import cn.jihong.common.util.StringUtil;
import cn.jihong.mes.api.model.vo.ProductionMachineOutVO;
import cn.jihong.mes.api.service.IProductionMachineService;
import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.enums.PalletOperateStatusEnum;
import cn.jihong.mes.production.api.model.enums.PalletOperateTypeEnum;
import cn.jihong.mes.production.api.model.po.ProductDefectiveProductsCallbackPO;
import cn.jihong.mes.production.api.model.po.ProductDefectiveProductsPO;
import cn.jihong.mes.production.api.model.po.ProductPalletPO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.GetListByDefectiveSourceNameInVO;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketPageInVO;
import cn.jihong.mes.production.api.model.vo.in.QualityCheckUpdatesDefectiveInVO;
import cn.jihong.mes.production.api.model.vo.in.SaveDefectiveProductsInfoInVO;
import cn.jihong.mes.production.api.model.vo.out.DefectiveProductsInfoOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetListByDefectiveSourceNameOutVO;
import cn.jihong.mes.production.api.model.vo.out.SaveDefectiveProductsInfoOutVO;
import cn.jihong.mes.production.api.service.*;
import cn.jihong.mes.production.app.mapper.ProductDefectiveProductsMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;
import cn.jihong.oa.ehr.api.model.dto.UserDTO;
import cn.jihong.oa.ehr.api.service.IA01Service;
import cn.jihong.oa.erp.api.model.dto.BmbaTMaterialDTO;
import cn.jihong.oa.erp.api.model.po.EcaaucTPO;
import cn.jihong.oa.erp.api.model.vo.GetEcffucTListVO;
import cn.jihong.oa.erp.api.model.vo.GetProcessSeqByTickNoOutVO;
import cn.jihong.oa.erp.api.service.IEcaaucTService;
import cn.jihong.oa.erp.api.service.IEcffucTService;
import cn.jihong.oa.erp.api.service.ISfaaTService;
import cn.jihong.oa.erp.api.service.ISfcbTService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.seata.core.context.RootContext;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 * 不良品记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Slf4j
@DubboService
public class ProductDefectiveProductsServiceImpl
    extends JiHongServiceImpl<ProductDefectiveProductsMapper, ProductDefectiveProductsPO>
    implements IProductDefectiveProductsService {

    private static Pattern chinesePattern = Pattern.compile("[\\u4e00-\\u9fa5]");



    @Resource
    private IProductTicketService productTicketService;
    @DubboReference
    private IA01Service ia01Service;
    @DubboReference
    private IQcDefectDetermineBizService qcDefectDetermineBizService;
    @DubboReference
    private IProductionMachineService productionMachineService;
    @DubboReference
    private IEcffucTService ecffucTService;

    @DubboReference
    private ISfaaTService sfaaTService;
    @DubboReference
    private IEcaaucTService ecaaucTService;
    @Resource
    private IProductLastPalletService productLastPalletService;
    @Resource
    private IProductDefectiveProductsCallbackService productDefectiveProductsCallbackService;
    @Resource
    private IProductInfoService productInfoService;
    @DubboReference
    private ISfcbTService sfcbTService;


    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 保存不良品信息
     */
    @Transactional
    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public SaveDefectiveProductsInfoOutVO saveDefectiveProductsInfo(SaveDefectiveProductsInfoInVO saveDefectiveProductsInfoInVO){
        log.info("Seata全局事务id=================>{}", RootContext.getXID());
        return saveDefectiveProductsInfoNoSeata(saveDefectiveProductsInfoInVO);
    }

    /**
     * 保存不良品信息   --- 不包含seata全局事务
     */
    @Override
    public SaveDefectiveProductsInfoOutVO saveDefectiveProductsInfoNoSeata(SaveDefectiveProductsInfoInVO saveDefectiveProductsInfoInVO) {

        SaveDefectiveProductsInfoOutVO outVO = new SaveDefectiveProductsInfoOutVO();
        ProductTicketPO productTicketPO =
            productTicketService.verify(saveDefectiveProductsInfoInVO.getProductTicketId());

        verify(saveDefectiveProductsInfoInVO);

        // 获得工序的单位
        EcaaucTPO ecaaucTPO =
            ecaaucTService.getByCompanyProcess(productTicketPO.getCompanyCode(), productTicketPO.getProcessCode());
        if (ecaaucTPO != null) {
            saveDefectiveProductsInfoInVO.setUnit(ecaaucTPO.getEcaauc009());
        }

        // 保存本地数据
        ProductDefectiveProductsPO productDefectiveProductsPO =
            BeanUtil.copyProperties(saveDefectiveProductsInfoInVO, ProductDefectiveProductsPO.class);
        productDefectiveProductsPO.setCompanyCode(productTicketPO.getCompanyCode());
        productDefectiveProductsPO.setCreateBy(SecurityUtil.getUserId());

        save(productDefectiveProductsPO);

        /*// 不良品工序 与 当前工单的工序一致 则默认为 报废入库处理
        if(productTicketPO.getProcessCode().equals(saveDefectiveProductsInfoInVO.getDefectiveProductsSource())) {
            productDefectiveProductsPO.setIsCallback(Integer.valueOf(BooleanEnum.TRUE.getCode()));
            save(productDefectiveProductsPO);

            // 将不良品信息存入redis 当前工序和不良品工序一致时候 无需判定，方便回调的时候取用
            redisTemplate.opsForValue().set(RedisCacheConstant.DEFECTIVE + productDefectiveProductsPO.getId(), JSON.toJSONString(productDefectiveProductsPO), 72, TimeUnit.HOURS);
        }else{
            save(productDefectiveProductsPO);
        }*/


        // 创建不良品栈板   ------ 不会进入这个方法了
        if (StringUtils.isNotBlank(saveDefectiveProductsInfoInVO.getPalletCode())) {
            ProductPalletPO productPalletPO = createProductPallet(saveDefectiveProductsInfoInVO, productTicketPO);
            // 创建报不良操作记录
            productLastPalletService.savePalletOperationRecords(productPalletPO.getProductionNum(), BigDecimal.ZERO,
                    productPalletPO.getProductionNum(), PalletOperateTypeEnum.DEFECTIVE_PRODUCTS.getCode(), productTicketPO,
                    productPalletPO.getId(), productPalletPO.getPalletCode(), null, null);
        }

        // 创建不良品工单
        CreateBizFromProductionOutVO createBizFromProductionOutVO = createDefectiveProductsTicket(saveDefectiveProductsInfoInVO, productDefectiveProductsPO.getId());
        BeanUtil.copyProperties(createBizFromProductionOutVO,outVO);
        return outVO;
    }

    /**
     * 保存校验
     * @param saveDefectiveProductsInfoInVO
     */
    private void verify(SaveDefectiveProductsInfoInVO saveDefectiveProductsInfoInVO) {
        // 栈板码只能使用一次
        ProductPalletPO productPalletPO =
            productLastPalletService.getByPalletCode(saveDefectiveProductsInfoInVO.getPalletCode());
        if (productPalletPO != null) {
            throw new CommonException("该栈板码"+ saveDefectiveProductsInfoInVO.getPalletCode() +"已使用，请勿重复操作");
        }
    }

    /**
     * 创建不良品栈板
     * @param saveDefectiveProductsInfoInVO
     * @param productTicketPO
     */
    private ProductPalletPO createProductPallet(SaveDefectiveProductsInfoInVO saveDefectiveProductsInfoInVO, ProductTicketPO productTicketPO) {
        ProductPalletPO productPalletPO = new ProductPalletPO();
        productPalletPO.setCompanyCode(productTicketPO.getCompanyCode());
        productPalletPO.setMachineName(productTicketPO.getMachineName());
        productPalletPO.setProductTicketId(productTicketPO.getId());
        productPalletPO.setProductionOrder(productTicketPO.getPlanTicketNo());
        productPalletPO.setPalletCode(saveDefectiveProductsInfoInVO.getPalletCode());
        productPalletPO.setPalletSource(productTicketPO.getProcess());
        productPalletPO.setShiftSource(productTicketPO.getShift());
        productPalletPO.setProductionTime(new java.util.Date());
        productPalletPO.setProductionNum(saveDefectiveProductsInfoInVO.getDefectiveProductsQuantity());
        productPalletPO.setUnit(saveDefectiveProductsInfoInVO.getUnit());
        productPalletPO.setRemainingQuantity(saveDefectiveProductsInfoInVO.getDefectiveProductsQuantity());
        productPalletPO.setStatus(PalletOperateStatusEnum.IN_LIBRARY.getCode());
        productPalletPO.setCreateBy(SecurityUtil.getUserId());
        productPalletPO.setIsDefective(Integer.valueOf(BooleanEnum.TRUE.getCode()));
        productLastPalletService.save(productPalletPO);
        return productPalletPO;
    }

    private CreateBizFromProductionOutVO createDefectiveProductsTicket(SaveDefectiveProductsInfoInVO saveDefectiveProductsInfoInVO, Long palletId) {
        DefectDetermineDTO defectDetermineDTO = new DefectDetermineDTO();
        ProductTicketPO productTicketPO =
            productTicketService.getById(saveDefectiveProductsInfoInVO.getProductTicketId());

        defectDetermineDTO.setMachineName(productTicketPO.getMachineName());
        defectDetermineDTO.setProductionWorkOrderNo(productTicketPO.getPlanTicketNo());
        defectDetermineDTO.setProductionName(productTicketPO.getProductName());
        defectDetermineDTO.setProductionDate(productTicketPO.getProduceDate());
        defectDetermineDTO.setProductionSerialNo(productTicketPO.getShift());
        defectDetermineDTO.setDefectPhenomenonCode(saveDefectiveProductsInfoInVO.getDefectiveProductsReason());
        defectDetermineDTO.setDefectPhenomenon(saveDefectiveProductsInfoInVO.getDefectiveProductsReasonName());
        defectDetermineDTO.setDefectNum(saveDefectiveProductsInfoInVO.getDefectiveProductsQuantity());
        defectDetermineDTO.setProcess(saveDefectiveProductsInfoInVO.getDefectiveProductsSourceName());
        defectDetermineDTO.setProcessCode(saveDefectiveProductsInfoInVO.getDefectiveProductsSource());
//        defectDetermineDTO.setPalletId(palletId);
        defectDetermineDTO.setProductDefectiveProductsId(palletId);
        defectDetermineDTO.setProductTicketId(saveDefectiveProductsInfoInVO.getProductTicketId());
        defectDetermineDTO.setCreateBy(Math.toIntExact(productTicketPO.getCreateBy()));
        List<UserDTO> userInfoByIds = ia01Service.getUserInfoByIds(Arrays.asList(productTicketPO.getCreateBy()));
        defectDetermineDTO
            .setCreatorName(CollectionUtil.isNotEmpty(userInfoByIds) && ObjectUtil.isNotNull(userInfoByIds.get(0))
                ? userInfoByIds.get(0).getName() : "");

        ProductionMachineOutVO productionMachineOutVO = productionMachineService
            .getProductionMachineByErpName(productTicketPO.getMachineName(), productTicketPO.getProcess());
        defectDetermineDTO.setCompanyCode(productionMachineOutVO.getEnterpriseNumbers());

        return qcDefectDetermineBizService.createBizFromProduction(defectDetermineDTO);
    }

    /**
     * 查询不良品记录
     */
    @Override
    public Pagination<DefectiveProductsInfoOutVO>
        getDefectiveProductsRecords(ProductTicketPageInVO productTicketPageInVO) {
        Page<DefectiveProductsInfoOutVO> iPage = baseMapper.getDefectiveProductsRecords(productTicketPageInVO.getPage(),
            productTicketPageInVO.getProductTicketId(),SecurityUtil.getCompanySite());
        return getDefectiveProductsInfoOutVOPagination(iPage);
    }

    private Pagination<DefectiveProductsInfoOutVO> getDefectiveProductsInfoOutVOPagination(Page<DefectiveProductsInfoOutVO> iPage) {
        if (CollectionUtil.isEmpty(iPage.getRecords())) {
            return Pagination.newInstance(null);
        }

        List<Long> userIds = Lists.newArrayList();
        Map<Long, String> userMap = Maps.newConcurrentMap();
        userIds.addAll(iPage.getRecords().stream().map(DefectiveProductsInfoOutVO::getCreateBy).distinct()
            .collect(Collectors.toList()));
        String userIdsStr = iPage.getRecords().stream().map(p -> p.getTeamUsers()).filter(StringUtil::isNotBlank)
            .collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(userIdsStr)) {
            userIds.addAll(Arrays.stream(userIdsStr.split(",")).filter(userId->{
                return !chinesePattern.matcher(userId).find();
            }).map(u -> Long.valueOf(u)).collect(Collectors.toList()));
            userMap.putAll(ia01Service.getUserInfoByIds(userIds).stream()
                .collect(Collectors.toMap(UserDTO::getId, UserDTO::getName)));
        }

        return Pagination.newInstance(iPage.getRecords().stream().map(outboundInfoOutVO -> {
            outboundInfoOutVO.setCreaterName(userMap.get(outboundInfoOutVO.getCreateBy()));
            if (StringUtils.isNotBlank(outboundInfoOutVO.getTeamUsers()) && !chinesePattern.matcher(outboundInfoOutVO.getTeamUsers()).find()) {
                String userName =
                        Arrays.stream(outboundInfoOutVO.getTeamUsers().split(",")).filter(StringUtil::isNotBlank).map(id -> {
                            return userMap.get(Long.valueOf(id));
                        }).collect(Collectors.joining(","));
                outboundInfoOutVO.setTeamUsersName(userName);
            }

            return outboundInfoOutVO;
        }).collect(Collectors.toList()), iPage);
    }


    /**
     * 查询不良原因
     */
    @Override
    public Object getEcffucTList(Long productTicketId) {
        GetEcffucTListVO getEcffucTListVO = new GetEcffucTListVO();
        ProductTicketPO productTicketPO = productTicketService.getById(productTicketId);
        ProductionMachineOutVO productionMachineOutVO = productionMachineService
                .getProductionMachineByErpName(productTicketPO.getMachineName(), productTicketPO.getProcess());
        if (productionMachineOutVO == null) {
            throw new CommonException("未找到对应不良原因,请联系管理员");
        }
        getEcffucTListVO.setProcessCode(productionMachineOutVO.getProcessCode());
        getEcffucTListVO.setCompanyCode(productionMachineOutVO.getEnterpriseNumbers());

        return ecffucTService.getEcffucTList(getEcffucTListVO);
    }

    /**
     * 获得不良品记录
     */
    @Override
    public Pagination<DefectiveProductsInfoOutVO> getDefectiveProductsRecordsByTicketBase(IPage page, ProductTicketBaseDTO productTicketBaseDTO) {
        Page<DefectiveProductsInfoOutVO> iPage = baseMapper.getDefectiveProductsRecordsByTicketBase(page,
                productTicketBaseDTO);
        return getDefectiveProductsInfoOutVOPagination(iPage);
    }

    @Override
    public List<BmbaTMaterialDTO> getProcessList(Long productTicketId) {
        // 获得所在工序 process
        ProductTicketPO productTicketPO = productTicketService.getById(productTicketId);

        // 获得工单对应的产品编号
        String processCode = productTicketPO.getProcessCode();

        List<GetProcessSeqByTickNoOutVO> processSeqList = sfcbTService.getProcessSeqByTickNo(productTicketPO.getPlanTicketNo());
        List<BmbaTMaterialDTO> bmbaTMaterialDTOS = Lists.newArrayList();
        for (GetProcessSeqByTickNoOutVO getProcessSeqByTickNoOutVO : processSeqList) {
            BmbaTMaterialDTO bmbaTMaterialDTO = new BmbaTMaterialDTO();
            bmbaTMaterialDTO.setProcess(getProcessSeqByTickNoOutVO.getSfcb003());
            bmbaTMaterialDTO.setProcessName(getProcessSeqByTickNoOutVO.getProcessName());
            bmbaTMaterialDTOS.add(bmbaTMaterialDTO);
        }

        // 调用方法，将具有相同 processCode 的对象移动到列表首位
        Collections.sort(bmbaTMaterialDTOS, processCodeComparator(processCode));

        return bmbaTMaterialDTOS;
    }

//    public static void main(String[] args) {
//        String processCode = "5";
//        List<BmbaTMaterialDTO> processList = Lists.newArrayList();
//        for (int i = 0; i < 5; i++) {
//            BmbaTMaterialDTO bmbaTMaterialDTO = new BmbaTMaterialDTO();
//            bmbaTMaterialDTO.setProcess(String.valueOf(i+1));
//            processList.add(bmbaTMaterialDTO);
//        }
//        // 调用方法，将具有相同 processCode 的对象移动到列表首位
//        Collections.sort(processList, processCodeComparator(processCode));
//        processList.stream().forEach(dto -> System.out.println(dto.getProcess()));
//    }

    // 定义一个排序规则，使得具有相同 processCode 的对象排在前面
    private Comparator<BmbaTMaterialDTO> processCodeComparator(String targetProcessCode) {
        return (o1, o2) -> {
            // 如果对象的 processCode 与目标 processCode 相同，则返回负值，否则返回正值
            if (o1.getProcess().equals(targetProcessCode) && !o2.getProcess().equals(targetProcessCode)) {
                return -1; // o1 在前面
            } else if (!o1.getProcess().equals(targetProcessCode) && o2.getProcess().equals(targetProcessCode)) {
                return 1; // o2 在前面
            } else {
                return 0; // 保持原有顺序
            }
        };
    }


    @Override
    public List<GetListByDefectiveSourceNameOutVO> getListByDefectiveSourceName(GetListByDefectiveSourceNameInVO inVO) {
        return baseMapper.getListByDefectiveSourceName(inVO);
    }

    /**
     * 质检回调更新不良品信息
     */
    @Override
    public void qualityCheckUpdatesDefective(List<QualityCheckUpdatesDefectiveInVO> qualityCheckUpdatesDefectiveInVOs) {
        for (QualityCheckUpdatesDefectiveInVO qualityCheckUpdatesDefectiveInVO : qualityCheckUpdatesDefectiveInVOs) {
            ProductTicketPO productTicketPO =
                    productTicketService.getById(qualityCheckUpdatesDefectiveInVO.getProductTicketId());
//            // 更新不良品栈板
//            ProductPalletPO productPalletPO =
//                    productLastPalletService.getById(qualityCheckUpdatesDefectiveInVO.getPalletId());
            if (DefectDetermineStatusEnum.HEAVY_PROCESSING.getCode()
                    .equals(qualityCheckUpdatesDefectiveInVO.getCheckResut())) {
//                productPalletPO.setIsDefective(Integer.valueOf(BooleanEnum.TRUE.getCode()));
                // 暂时放到不良品数量
//              更新重工数量
//            productTicketPO.setHeavyProduct(
//                productTicketPO.getHeavyProduct().add(qualityCheckUpdatesDefectiveInVO.getDefectiveProductsQuantity()));
                // 更新不良品数量
                productTicketPO.setDefectiveProduct(
                        productTicketPO.getDefectiveProduct().add(qualityCheckUpdatesDefectiveInVO.getDefectiveProductsQuantity()));
            }
            if (DefectDetermineStatusEnum.SCRAP_STORAGE.getCode()
                    .equals(qualityCheckUpdatesDefectiveInVO.getCheckResut())) {
//                productPalletPO.setIsDefective(Integer.valueOf(BooleanEnum.TRUE.getCode()));
                // 更新不良品数量
                productTicketPO.setDefectiveProduct(
                        productTicketPO.getDefectiveProduct().add(qualityCheckUpdatesDefectiveInVO.getDefectiveProductsQuantity()));
            }
            if (DefectDetermineStatusEnum.CONCESSION_TREATMENT.getCode()
                    .equals(qualityCheckUpdatesDefectiveInVO.getCheckResut())) {
//                productPalletPO.setIsDefective(Integer.valueOf(BooleanEnum.FALSE.getCode()));
                // 更新生产数量
                productTicketPO.setRealProduct(
                        productTicketPO.getRealProduct().add(qualityCheckUpdatesDefectiveInVO.getDefectiveProductsQuantity()));
            }
//            productPalletPO.setProductionNum(qualityCheckUpdatesDefectiveInVO.getDefectiveProductsQuantity());
//            productLastPalletService.updateById(productPalletPO);
            productTicketService.updateById(productTicketPO);

            // 更新不良品记录
            ProductDefectiveProductsPO productDefectiveProductsPO = getById(qualityCheckUpdatesDefectiveInVO.getProductDefectiveProductsId());
            if (productDefectiveProductsPO == null) {
                throw new CommonException("工单" + productTicketPO.getPlanTicketNo() + "不存在不良id为"
                        + qualityCheckUpdatesDefectiveInVO.getProductDefectiveProductsId() + "的不良品记录");
            }
            productDefectiveProductsPO.setIsCallback(Integer.valueOf(BooleanEnum.TRUE.getCode()));
            updateById(productDefectiveProductsPO);

                /*// redis 获取对应id 的不良品申请信息
                Object productDefectiveProductObj = redisTemplate.opsForValue().get(RedisCacheConstant.DEFECTIVE + qualityCheckUpdatesDefectiveInVO.getProductDefectiveProductsId());
                if(productDefectiveProductObj !=null ) {
                    // 在发起不良品判定的时候已经设置回调成功  这里无需设置 setIsCallback(Integer.valueOf(BooleanEnum.TRUE.getCode()));
                    productDefectiveProductsPO = JSON.parseObject(productDefectiveProductObj.toString(), ProductDefectiveProductsPO.class);
                }else {
                    throw new CommonException("工单" + productTicketPO.getPlanTicketNo() + "不存在不良id为"
                            + qualityCheckUpdatesDefectiveInVO.getProductDefectiveProductsId() + "的不良品记录");
                }
            }else {
                productDefectiveProductsPO.setIsCallback(Integer.valueOf(BooleanEnum.TRUE.getCode()));
                updateById(productDefectiveProductsPO);
            }*/

//            // 创建报不良操作记录
//            productLastPalletService.savePalletOperationRecords(
//                    qualityCheckUpdatesDefectiveInVO.getDefectiveProductsQuantity(), BigDecimal.ZERO,
//                    qualityCheckUpdatesDefectiveInVO.getDefectiveProductsQuantity(),
//                    PalletOperateTypeEnum.QUALITY_CHECK_UPDATES.getCode(), productTicketPO, productPalletPO.getId(),
//                    productPalletPO.getPalletCode(), null, null);

            // 保存回调信息
            saveDefectiveCallback(qualityCheckUpdatesDefectiveInVO, productDefectiveProductsPO);

            // 创建生产汇总数据---根据生产工程单汇总
            productInfoService.createOrUpdateProductInfo(qualityCheckUpdatesDefectiveInVO.getProductTicketId(), null);
        }

    }

    @Override
    public List<ProductDefectiveProductsPO> getListByProductTicketId(Long productTicketId) {
        LambdaQueryWrapper<ProductDefectiveProductsPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductDefectiveProductsPO::getProductTicketId,productTicketId);
        List<ProductDefectiveProductsPO> productDefectiveProductsPOS = list(lambdaQueryWrapper);
        return productDefectiveProductsPOS;
    }

    @Override
    public void saveDefectiveProductsInfoByPC(SaveDefectiveProductsInfoInVO saveDefectiveProductsInfoInVO) {
        ProductTicketPO productTicketPO =
                productTicketService.getById(saveDefectiveProductsInfoInVO.getProductTicketId());
        if (StringUtils.isNotBlank(saveDefectiveProductsInfoInVO.getPalletCode())) {
            // 栈板码只能使用一次
            verify(saveDefectiveProductsInfoInVO);
        }
        // 已经上报的不允许再次新增
        if (Integer.valueOf(BooleanEnum.TRUE.getCode()).equals(productTicketPO.getIsDefectionSignUp())) {
            throw new CommonException("已经上报到erp，不允许新增");
        }


        // 获得工序的单位
        EcaaucTPO ecaaucTPO =
                ecaaucTService.getByCompanyProcess(productTicketPO.getCompanyCode(), productTicketPO.getProcessCode());
        if (ecaaucTPO != null) {
            saveDefectiveProductsInfoInVO.setUnit(ecaaucTPO.getEcaauc009());
        }

        // 保存本地数据
        ProductDefectiveProductsPO productDefectiveProductsPO =
                BeanUtil.copyProperties(saveDefectiveProductsInfoInVO, ProductDefectiveProductsPO.class);
        productDefectiveProductsPO.setCompanyCode(productTicketPO.getCompanyCode());
        productDefectiveProductsPO.setCreateBy(SecurityUtil.getUserId());
        save(productDefectiveProductsPO);

        // 创建不良品栈板   ------ 不会进入这个方法了
        if (StringUtils.isNotBlank(saveDefectiveProductsInfoInVO.getPalletCode())) {
            ProductPalletPO productPalletPO = createProductPallet(saveDefectiveProductsInfoInVO, productTicketPO);
            // 创建报不良操作记录
            productLastPalletService.savePalletOperationRecords(productPalletPO.getProductionNum(), BigDecimal.ZERO,
                    productPalletPO.getProductionNum(), PalletOperateTypeEnum.DEFECTIVE_PRODUCTS.getCode(), productTicketPO,
                    productPalletPO.getId(), productPalletPO.getPalletCode(), null, null);
        }

        // 创建不良品工单
        createDefectiveProductsTicket(saveDefectiveProductsInfoInVO, productDefectiveProductsPO.getId());

    }

    /**
     * 更新不良品信息
     */
    @Transactional
    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public void updateDefectiveProductsInfo(SaveDefectiveProductsInfoInVO saveDefectiveProductsInfoInVO) {
        ProductDefectiveProductsPO productDefectiveProductsPO = getById(saveDefectiveProductsInfoInVO.getId());
        BigDecimal defectiveProductsQuantityOld = productDefectiveProductsPO.getDefectiveProductsQuantity();

        ProductTicketPO productTicketPO = productTicketService.getById(productDefectiveProductsPO.getProductTicketId());
        if (productTicketPO.getIsDefectionSignUp() != null && Integer.valueOf(BooleanEnum.TRUE.getCode()).equals(productTicketPO.getIsDefectionSignUp())) {
            throw new CommonException("已经上报到erp，不允许修改");
        }

        // 更新不良品信息
        productDefectiveProductsPO.setDefectiveProductsReason(saveDefectiveProductsInfoInVO.getDefectiveProductsReason());
        productDefectiveProductsPO.setDefectiveProductsReasonName(saveDefectiveProductsInfoInVO.getDefectiveProductsReasonName());
        productDefectiveProductsPO.setDefectiveProductsSource(saveDefectiveProductsInfoInVO.getDefectiveProductsSource());
        productDefectiveProductsPO.setDefectiveProductsSourceName(saveDefectiveProductsInfoInVO.getDefectiveProductsSourceName());
        productDefectiveProductsPO.setDefectiveProductsQuantity(saveDefectiveProductsInfoInVO.getDefectiveProductsQuantity());
        updateById(productDefectiveProductsPO);

        // 更新回调信息
        UpdateDefectNumDTO updateDefectNumDTO = new UpdateDefectNumDTO();
        updateDefectNumDTO.setProductTicketId(productDefectiveProductsPO.getProductTicketId());
        updateDefectNumDTO.setProductDefectiveProductsId(productDefectiveProductsPO.getId());
        updateDefectNumDTO.setDefectNum(saveDefectiveProductsInfoInVO.getDefectiveProductsQuantity());

        UpdateDefectNumDTO updateDefectNumDTO1 = qcDefectDetermineBizService.updateDefectNum(updateDefectNumDTO);
        // 不是已完成（未完成），后续还会回调，所以本次就不更新，后续回调的时候再更新
        if (updateDefectNumDTO1.getStatus() != null && !updateDefectNumDTO1.getStatus().equals(Integer.valueOf(TicketStatusEnum.COMPLETED.getCode()))) {
            return; // 不进行更新
        }
        // 如果判定完成了，则 更新 不良品数量，及回调数量
        if (updateDefectNumDTO1.getDefectNum() != null && updateDefectNumDTO1.getDefectNum().compareTo(BigDecimal.ZERO) >= 0) {
            productDefectiveProductsCallbackService.updateDefectiveProductsCallback(productDefectiveProductsPO);

            // 更新工单表  减去旧的，增加信息
            productTicketPO.setDefectiveProduct(productTicketPO.getDefectiveProduct().subtract(defectiveProductsQuantityOld).add(updateDefectNumDTO1.getDefectNum()));
            productTicketService.updateById(productTicketPO);
        }
    }

    private void saveDefectiveCallback(QualityCheckUpdatesDefectiveInVO qualityCheckUpdatesDefectiveInVO, ProductDefectiveProductsPO productDefectiveProductsPO) {
        ProductDefectiveProductsCallbackPO productDefectiveProductsCallbackPO = new ProductDefectiveProductsCallbackPO();
        productDefectiveProductsCallbackPO.setProductDefectiveId(productDefectiveProductsPO.getId());
        productDefectiveProductsCallbackPO.setCompanyCode(productDefectiveProductsPO.getCompanyCode());
        productDefectiveProductsCallbackPO.setMachineName(productDefectiveProductsPO.getMachineName());
        productDefectiveProductsCallbackPO.setProductTicketId(productDefectiveProductsPO.getProductTicketId());
        productDefectiveProductsCallbackPO.setDefectiveProductsReason(productDefectiveProductsPO.getDefectiveProductsReason());
        productDefectiveProductsCallbackPO.setDefectiveProductsReasonName(productDefectiveProductsPO.getDefectiveProductsReasonName());
        productDefectiveProductsCallbackPO.setUnit(productDefectiveProductsPO.getUnit());
        productDefectiveProductsCallbackPO.setDefectiveProductsSource(productDefectiveProductsPO.getDefectiveProductsSource());
        productDefectiveProductsCallbackPO.setDefectiveProductsSourceName(productDefectiveProductsPO.getDefectiveProductsSourceName());
        productDefectiveProductsCallbackPO.setPalletCode(productDefectiveProductsPO.getPalletCode());
        productDefectiveProductsCallbackPO.setCreateBy(SecurityUtil.getUserId());

        productDefectiveProductsCallbackPO.setDefectiveProductsQuantity(qualityCheckUpdatesDefectiveInVO.getDefectiveProductsQuantity());
        productDefectiveProductsCallbackPO.setCheckResult(qualityCheckUpdatesDefectiveInVO.getCheckResut());

        productDefectiveProductsCallbackService.save(productDefectiveProductsCallbackPO);
    }
}
