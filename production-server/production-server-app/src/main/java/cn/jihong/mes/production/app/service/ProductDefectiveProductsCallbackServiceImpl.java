package cn.jihong.mes.production.app.service;

import cn.jihong.mes.production.api.model.po.ProductDefectiveProductsPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.dubbo.config.annotation.DubboService;

import cn.jihong.mes.production.api.model.po.ProductDefectiveProductsCallbackPO;
import cn.jihong.mes.production.api.service.IProductDefectiveProductsCallbackService;
import cn.jihong.mes.production.app.mapper.ProductDefectiveProductsCallbackMapper;
import cn.jihong.mybatis.service.impl.JiHongServiceImpl;

import java.util.List;

/**
 * <p>
 * 不良品回调表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@DubboService
public class ProductDefectiveProductsCallbackServiceImpl
    extends JiHongServiceImpl<ProductDefectiveProductsCallbackMapper, ProductDefectiveProductsCallbackPO>
    implements IProductDefectiveProductsCallbackService {

    @Override
    public List<ProductDefectiveProductsCallbackPO> getListByProductTicketId(Long productTicketId) {
        LambdaQueryWrapper<ProductDefectiveProductsCallbackPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ProductDefectiveProductsCallbackPO::getProductTicketId,productTicketId);
        List<ProductDefectiveProductsCallbackPO> productDefectiveProductsCallbackPOS = list(lambdaQueryWrapper);
        return productDefectiveProductsCallbackPOS;
    }

    @Override
    public void updateDefectiveProductsCallback(ProductDefectiveProductsPO productDefectiveProductsPO) {
        LambdaUpdateWrapper<ProductDefectiveProductsCallbackPO> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(ProductDefectiveProductsCallbackPO::getProductDefectiveId,productDefectiveProductsPO.getId())
                       .set(ProductDefectiveProductsCallbackPO::getDefectiveProductsQuantity,productDefectiveProductsPO.getDefectiveProductsQuantity());
        update(lambdaUpdateWrapper);
    }
}
