package cn.jihong.mes.production.app.controller;

import cn.jihong.common.model.Pagination;
import cn.jihong.common.model.StandardResult;
import cn.jihong.common.model.resultcode.OperateCode;
import cn.jihong.mes.production.api.model.vo.in.ProductTicketPageInVO;
import cn.jihong.mes.production.api.model.vo.in.SaveDefectiveProductsInfoInVO;
import cn.jihong.mes.production.api.model.vo.out.DefectiveProductsInfoOutVO;
import cn.jihong.mes.production.api.service.IProductDefectiveProductsService;
import cn.jihong.oa.erp.api.model.dto.BmbaTMaterialDTO;
import cn.jihong.oa.erp.api.model.dto.EcffucTDTO;
import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 不良品表 前端控制器
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@RestController
@RequestMapping("/productDefectiveProducts")
@ShenyuSpringMvcClient(path = "/productDefectiveProducts/**")
public class ProductDefectiveProductsController {

    @Resource
    private IProductDefectiveProductsService productDefectiveProductsService;

    /**
     * 保存不良品信息
     */
    @PostMapping("/saveDefectiveProductsInfo")
    public StandardResult
        saveDefectiveProductsInfo(@RequestBody @Valid SaveDefectiveProductsInfoInVO saveDefectiveProductsInfoInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productDefectiveProductsService.saveDefectiveProductsInfo(saveDefectiveProductsInfoInVO));
    }

    /**
     * 更新不良品信息
     */
    @PostMapping("/updateDefectiveProductsInfo")
    public StandardResult
    updateDefectiveProductsInfo(@RequestBody @Valid SaveDefectiveProductsInfoInVO saveDefectiveProductsInfoInVO) {
        productDefectiveProductsService.updateDefectiveProductsInfo(saveDefectiveProductsInfoInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 保存不良品信息 PC 端
     */
    @PostMapping("/saveDefectiveProductsInfoByPC")
    public StandardResult
    saveDefectiveProductsInfoByPC(@RequestBody @Valid SaveDefectiveProductsInfoInVO saveDefectiveProductsInfoInVO) {
        productDefectiveProductsService.saveDefectiveProductsInfoByPC(saveDefectiveProductsInfoInVO);
        return StandardResult.resultCode(OperateCode.SUCCESS);
    }

    /**
     * 查询不良品记录
     */
    @PostMapping("/getDefectiveProductsRecords")
    public StandardResult<Pagination<DefectiveProductsInfoOutVO>>
        getDefectiveProductsRecords(@RequestBody @Valid ProductTicketPageInVO productTicketPageInVO) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            productDefectiveProductsService.getDefectiveProductsRecords(productTicketPageInVO));
    }

    /**
     * 查询不良原因
     */
    @GetMapping("/getEcffucTList/{productTicketId}")
    public StandardResult<List<EcffucTDTO>> getEcffucTList(@PathVariable Long productTicketId) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
            (List<EcffucTDTO>)productDefectiveProductsService.getEcffucTList(productTicketId));
    }

    /**
     * 查询工序列表
     */
    @GetMapping("/getProcessList/{productTicketId}")
    public StandardResult<List<BmbaTMaterialDTO>> getProcessList(@PathVariable Long productTicketId) {
        return StandardResult.resultCode(OperateCode.SUCCESS,
                (List<BmbaTMaterialDTO>)productDefectiveProductsService.getProcessList(productTicketId));
    }


}
