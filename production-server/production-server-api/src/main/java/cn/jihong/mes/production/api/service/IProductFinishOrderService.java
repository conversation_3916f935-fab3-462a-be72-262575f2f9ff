package cn.jihong.mes.production.api.service;

import cn.jihong.mes.production.api.model.po.ProductFinishOrderPO;
import cn.jihong.mes.production.api.model.vo.in.ProductPlanTicketVO;
import cn.jihong.mes.production.api.model.vo.in.SaveFinishOrderInfoInVO;
import cn.jihong.mybatis.api.service.IJiHongService;

/**
 * <p>
 * 结单信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
public interface IProductFinishOrderService extends IJiHongService<ProductFinishOrderPO> {

    /**
     * 结单
     */
    String saveFinishOrderInfo(SaveFinishOrderInfoInVO saveFinishOrderInfoInVO);

    /**
     * 结单
     */
    String saveFinishOrderInfo(ProductPlanTicketVO productPlanTicketVO);


    /**
     * 根据工单id查询
     */
    ProductFinishOrderPO getByProductTicketId(Long productTicketId);

    /**
     * 校验在使用中
     * @param machineName
     */
    void verifyInUse(String machineName);

    /**
     * 校验暂存
     * @param machineName
     */
    void verifyStaging(String machineName);
}
