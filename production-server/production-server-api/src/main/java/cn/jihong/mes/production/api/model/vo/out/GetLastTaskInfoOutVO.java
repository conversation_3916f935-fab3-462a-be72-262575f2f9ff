package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class GetLastTaskInfoOutVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 机台名称
     */
    @NotBlank(message = "机台名称不能为空")
    private String machineName;


    /**
     * 班次  1 白班  2 夜班
     */
    @NotNull(message = "班次不能为空")
    private Integer shift;

    List<GetDefaultRoleListsOutVO> roleLists;

}
