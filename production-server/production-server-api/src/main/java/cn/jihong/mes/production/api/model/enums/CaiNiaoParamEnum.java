package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

/**
 * 菜鸟科技对接信息
 */
@Getter
public enum CaiNiaoParamEnum {

    XIAO_GAN("SITE-20","<PERSON><PERSON>"),
    LANG_FANG_YI_QI("SITE-08","Jihong"),
    LANG_FANG_ER_QI("SITE-082","Jihong"),
    ;

    /**
     * 据点
     */
    private String companyCode;

    /**
     * 租户code
     */
    private String tenantCode;

    CaiNiaoParamEnum(String companyCode,  String tenantCode) {
        this.companyCode = companyCode;
        this.tenantCode = tenantCode;
    }

    public static CaiNiaoParamEnum getCaiNiaoParamEnum(String code) {
        for (CaiNiaoParamEnum value : CaiNiaoParamEnum.values()) {
            if (value.getCompanyCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的麦当劳据点信息");
    }



}
