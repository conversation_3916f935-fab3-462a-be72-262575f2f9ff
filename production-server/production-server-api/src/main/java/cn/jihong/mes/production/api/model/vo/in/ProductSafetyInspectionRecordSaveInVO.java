package cn.jihong.mes.production.api.model.vo.in;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 安全点检记录保存参数
 */
@Data
public class ProductSafetyInspectionRecordSaveInVO implements Serializable {

    /**
     * ID(修改时传入)
     */
    private Long id;

    /**
     * 机台名称
     */
    @NotBlank(message = "机台名称不能为空")
    private String machineName;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 班次
     */
    private Integer shift;

    /**
     * 点检状态（0：未完成，1：已完成）
     */
    @NotNull(message = "点检状态不能为空")
    private Integer inspectionStatus;

    /**
     * 点检项目JSON
     */
    private String inspectionItems;

    /**
     * 生产工单号
     */
    private String productTicketNo;

    /**
     * 备注
     */
    private String remark;
} 