package cn.jihong.mes.production.api.model.enums;

import lombok.Getter;

/**
 * 机位
 */
@Getter
public enum MachinePartsEnum implements IntCodeEnum{

    SingleA("1", "单面机A机"),
    SingleB("2", "单面机B机"),
    SingleE("3", "单面机E机"),
    SingleF("4", "单面机F机"),
    SingleG("5", "糊机"),
    SingleH("6", "电刀"),
    SingleI("7", "收纸机"),
    ;

    private String code;

    private String name;

    MachinePartsEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static MachinePartsEnum getMachinePartsEnum(String code) {
        for (MachinePartsEnum value : MachinePartsEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new RuntimeException("不存在的状态");
    }

    @Override
    public <T extends Enum<?>> Object getCode(T enumValue) {
        return ((MachinePartsEnum) enumValue).getCode();
    }
}
