package cn.jihong.mes.production.api.model.vo.out;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class GetMaterialDailySettlementOutVO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long taskId;

    private String businessNo;

    /**
     * 物料序列
     */
    private String materialSeq;

    /**
     * 物料编号
     */
    private String materialBarcodeNo;

    /**
     * 物料code
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料单位
     */
    private String materialUnit;

    /**
     * 上料数量
     */
    private BigDecimal loadingQuantity;


    /**
     * 物料消耗数量
     */
    private BigDecimal consumptionQuantity;
    private BigDecimal consumptionQuantityP3;
    /**
     * 剩余数量
     */
    private BigDecimal remainingQuantity;
    private BigDecimal remainingQuantityP3;


    /**
     * 物料部件(用途)
     */
    private String materialPlace;


    /**
     * 物料部件名称（用途名称）
     */
    private String materialPlaceName;

    private String process;
    private String processCode;


    /**
     * 生产计划工单单号
     */
    private String planTicketNo;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 报工数量
     */
    private BigDecimal reportedQuantity = BigDecimal.ZERO;

    /**
     * 不良品数量
     */
    private BigDecimal defectiveProduct = BigDecimal.ZERO;

    /**
     * 未确认不良品数量
     */
    private BigDecimal unconfirDefectiveQuantity = BigDecimal.ZERO;

    /**
     * 扣料单号
     */
    private String deductionNo;

    /**
     * 物料使用类型：  10 正扣料  20 分摊  30 倒扣料
     */
    private Integer useType;

}
