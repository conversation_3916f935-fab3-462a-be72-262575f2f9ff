package cn.jihong.mes.production.api.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 箱码明细
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
@Data
public class ProductBoxBarcodeDetailDetailDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    private Long id;


    /**
     * 工厂代码
     */
    private String companyCode;


    /**
     * 箱码号段id
     */
    private Long productBarcodeId;

    private Long productBarcodeDetailId;


    /**
     * 箱码开始号
     */
    private String barcodeNo;


    /**
     * 生产工程单号
     */
    private String planTicketNo;


    /**
     * 产品名称
     */
    private String productName;


    /**
     * 料号
     */
    private String materialCode;


    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date productionDate;


    /**
     * 批次号
     */
    private String lotNo;


    /**
     * 箱数量
     */
    private Long boxNum;


    /**
     * 批次识别码
     */
    private String batchCode;


    /**
     * 机台 暂时默认页面保存赋值是 01
     */
    private String machine;


    /**
     * 班次 01 02
     */
    private String shift;


    /**
     * 保质期 默认在当前日期 + 2 年
     */
    private String expirationDate;


    /**
     * 作废状态 0 作废   1 未作废
     */
    private Integer disableStatus;


    /**
     * 打印状态 0 未打印  1 已打印
     */
    private Integer printStatus;

    /**
     * 客户编号
     */
    private String customerNo;

    private String skuCode;

    /**
     * 激活状态 0 未激活 1 已激活  2 已销毁
     */
    private Integer activa;

}
