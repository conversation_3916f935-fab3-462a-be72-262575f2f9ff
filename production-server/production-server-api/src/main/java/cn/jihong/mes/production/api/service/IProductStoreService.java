package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.dto.EnumDTO;
import cn.jihong.mes.production.api.model.dto.ProductStoreDTO;
import cn.jihong.mes.production.api.model.dto.ScanInfoDTO;
import cn.jihong.mes.production.api.model.po.ProductStorePO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.out.PalletStoreOutVO;
import cn.jihong.mes.production.api.model.vo.out.QueryPalletInfoOutVO;
import cn.jihong.mes.production.api.model.vo.out.QueryStorageInfoOutVO;
import cn.jihong.mes.production.api.model.vo.out.SaveOutboundAndDefectiveInfoOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;
import cn.jihong.oa.erp.api.model.vo.PageListCommonInVO;

import java.util.List;

/**
 * 入库表 服务类
 *
 * <AUTHOR>
 * @since 2024-12-03
 */
public interface IProductStoreService extends IJiHongService<ProductStorePO> {

    void inboundRequestByBox(String lock_key, PushWmsAndErpInVO pushWmsAndErpInVO);



    void inboundRequestByPallet(String lock_key, InboundRequestByPalletInVO inboundRequestByPalletInVO);

    SaveOutboundAndDefectiveInfoOutVO saveOutboundAndDefectiveInfo(SaveOutboundAndDefectiveInfo saveOutboundAndDefectiveInfo);

    String request(ProductStoreDTO productStoreDTO);


    PalletStoreOutVO scanByPallet(ScanInfoDTO scanInfo);

    Pagination<QueryStorageInfoOutVO> scanByTicket(QueryStoreByTicket queryStoreByTicket);

    String confirmInbound(String lock_key, ConfirmInboundInVO confirmInboundInVO);

    String confirmPulled(ConfirmInboundInVO confirmInboundInVO);


    void saveDraftRequest(List<PalletStoreOutVO> palletStoreOutVOS);

    List<PalletStoreOutVO> getDraftRequest();

    void saveDraftPull(List<PalletStoreOutVO> palletStoreOutVOS);

    List<PalletStoreOutVO> getDraftPull();

    void saveDraftWarehous(List<PalletStoreOutVO> palletStoreOutVOS);

    List<PalletStoreOutVO> getDraftWarehous();

    ProductStorePO getByPalletCode(String palletCode);

    Pagination<QueryPalletInfoOutVO> queryPalletInfo(QueryPalletInfoInVO queryPalletInfoInVO);

    Pagination<QueryStorageInfoOutVO> queryStorageInfo(QueryPalletInfoInVO queryPalletInfoInVO);

    String requestByPallet(ProductStoreDTO productStoreDTO);

    EnumDTO getDefaultStorage();

    void setDefaultStorage(EnumDTO enumDTO);

    Pagination<EnumDTO> getStorages(PageListCommonInVO pageListCommonInVO);

    String confirmInboundByPallet(String lock_key, ConfirmInboundInVO confirmInboundInVO);

    List<ProductStorePO> getByApplyNo(List<String> applyNos);

    Pagination<EnumDTO> getWarehouse(PageListCommonInVO pageListCommonInVO);

    EnumDTO getDefaultWarehouse();

    void deleteApplyNo(String storeApplyNo);

    String getBarcodeLotNo(String barcode);

    String updateBarcodeLotNo(String barcode, String lotNo);

    List<ProductStorePO> getByPlanTicketNO(String productTicketNo);

    void verifyPolletCodeFromWms(List<String> polletCodes);

    void verifyPalletCodeForWms(List<String> palletCodes);

    void verifyBoxtCodeForWms(List<String> boxCodes);
}
