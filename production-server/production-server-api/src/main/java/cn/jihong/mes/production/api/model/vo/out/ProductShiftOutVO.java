package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class ProductShiftOutVO implements Serializable {

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 班次 1 白班  2 夜班
     */
    private Integer shift;

    /**
     * 班组人员id
     */
    private String teamUsers;

    /**
     * 班组人员名称
     */
    private String teamUsersName;

    /**
     * 生产工单id
     */
    private Long productTicketId;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 工序名称
     */
    private String process;

    /**
     * 角色/岗位名称
     */
    private String roleName;


    /**
     * 角色/岗位Code
     */
    private String roleCode;

}
