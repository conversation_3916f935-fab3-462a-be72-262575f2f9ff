package cn.jihong.mes.production.api.model.vo.out;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 安全点检记录输出参数
 */
@Data
public class ProductSafetyInspectionRecordOutVO implements Serializable {

    /**
     * ID
     */
    private Long id;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 班次
     */
    private Integer shift;

    /**
     * 点检状态（0：未完成，1：已完成）
     */
    private Integer inspectionStatus;

    /**
     * 点检时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inspectionTime;

    /**
     * 点检人
     */
    private String inspector;

    /**
     * 备注
     */
    private String remark;
} 