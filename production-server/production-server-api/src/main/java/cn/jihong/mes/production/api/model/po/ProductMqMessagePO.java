package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 *
 * <AUTHOR>
 * @since 2024-07-25
 */
@Getter
@Setter
@TableName("product_mq_message")
public class ProductMqMessagePO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String MESSAGEID = "messageId";
    public static final String USERINFO = "userInfo";
    public static final String BIZCODE = "bizCode";
    public static final String REQUESTDATA = "requestData";
    public static final String RESPONSEDATA = "responseData";
    public static final String RETRYTIMES = "retryTimes";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";


    @TableId(value = ID, type = IdType.AUTO)
    private Long id;


    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;


    /**
     * 消息id
     */
    @TableField(MESSAGEID)
    private String messageId;


    /**
     * 用户信息
     */
    @TableField(USERINFO)
    private String userInfo;


    /**
     * 消息类型
     */
    @TableField(BIZCODE)
    private String bizCode;


    /**
     * 请求数据
     */
    @TableField(REQUESTDATA)
    private String requestData;


    /**
     * 响应数据
     */
    @TableField(RESPONSEDATA)
    private String responseData;


    /**
     * 重试次数
     */
    @TableField(RETRYTIMES)
    private Integer retryTimes;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

}
