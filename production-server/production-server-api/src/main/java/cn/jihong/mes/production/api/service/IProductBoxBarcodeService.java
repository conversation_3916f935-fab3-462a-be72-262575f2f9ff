package cn.jihong.mes.production.api.service;

import cn.jihong.common.model.Pagination;
import cn.jihong.mes.production.api.model.po.ProductBoxBarcodePO;
import cn.jihong.mes.production.api.model.vo.in.AddProductBoxBarcodeInVO;
import cn.jihong.mes.production.api.model.vo.in.GetProductBoxBarcodeInVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductBoxBarcodeDetailOutVO;
import cn.jihong.mes.production.api.model.vo.out.GetProductBoxBarcodeOutVO;
import cn.jihong.mybatis.api.service.IJiHongService;

import java.util.List;

/**
 * 箱码号段 服务类
 *
 * <AUTHOR>
 * @since 2024-07-09
 */
public interface IProductBoxBarcodeService extends IJiHongService<ProductBoxBarcodePO> {

    /**
     * 查询条码段列表
     * @param getProductBoxBarcodeInVO
     * @return
     */
    Pagination<GetProductBoxBarcodeOutVO> getProductBoxBarcodeList(GetProductBoxBarcodeInVO getProductBoxBarcodeInVO);


    /**
     * 启用/禁用条码
     * @param id
     */
    void enableBoxBarcode(Integer id);

    Long addBoxBarcode(AddProductBoxBarcodeInVO addProductBoxBarcodeInVO);

    List<GetProductBoxBarcodeDetailOutVO> getListByBarcodes(List<String> caseCodes);

    GetProductBoxBarcodeOutVO getBoxBarcode(Integer id);

    GetProductBoxBarcodeOutVO getEnableBoxBarcode(String customerNo);
}
