package cn.jihong.mes.production.api.model.vo.in;


import cn.jihong.common.model.dto.PageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

@Data
public class GetProductReportByFactoryInVO extends PageRequest {

    /**
     * 公司code
     */
    private String companyCode;


    /**
     * 机台名称
     */
    private String machineName;


    /**
     * 工程单号
     */
    private String planTicketNo;


    /**
     * 生产日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String produceDate;

    /**
     * 排产状态  0 未排产 1 已排产
     */
    private Integer planStatus;

    /**
     * 生产状态  0 未生产 1 已产中
     */
    private Integer productStatus;

    /**
     * 排序字段
     */
    private String sortName;

    /**
     * 排序类型 ASC/DESC 升序 降序
     */
    private String sortType = "DESC";

    /**
     * 班次
     */
    private Integer shift;

}
