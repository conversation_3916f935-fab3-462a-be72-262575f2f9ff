package cn.jihong.mes.production.api.model.vo.out.logistics;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025-03-07 11:48
 */
@Data
public class ItemQueryByConditionOutVO implements Serializable {

    /**
     * 托盘 ID
     */
    private String PalletId;

    /**
     * Erp 托盘 ID
     */
    private String ERPPalletId;

    /**
     * 托盘编号
     */
    private Integer PalletToken;

    /**
     * 订单号
     */
    private Integer OrderToken;

    /**
     * 物料类型
     */
    private Integer MaterielType;

    /**
     * 合并模式
     */
    private Integer MergerMode;

    /**
     * 当前地址
     */
    private Integer CurrentAddress;

    /**
     * 当前跨度
     */
    private Integer CurrentSpan;

    /**
     * 位置索引
     */
    private Integer PlaceIndex;

    /**
     * 位置索引（矩阵）
     */
    private Integer PlaceIndex_Matrix;

    /**
     * 托盘长度
     */
    private Integer PalletLength;

    /**
     * 托盘宽度
     */
    private Integer PalletWidth;

    /**
     * 托盘列数
     */
    private Integer PalletColumn;

    /**
     * 入口设备 ID
     */
    private Integer EntranceDeviceId;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date CreatedAt;

    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ModityAt;

    /**
     * 出库时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date OutWarehouseAt;

    /**
     * 创建者
     */
    private String CreateBy;

    /**
     * 是否在库
     */
    private Boolean AtWarehouse;

    /**
     * 是否已提交
     */
    private Boolean Submit;

    /**
     * PLC 接收器完成
     */
    private Boolean PlcAcceptorDone;

    /**
     * 无效标志
     */
    private Boolean Invalid;

    /**
     * 物料类别 ID
     */
    private String CategoryId;

    /**
     * 物料类别名称
     */
    private String CategoryName;

    /**
     * 托盘状态
     */
    private Integer StatusTag;

    /**
     * 附加状态标签
     */
    private Integer AdditionStatusTag;

    /**
     * Erp 订单 IDs
     */
    private String ErpOrderIds;

    /**
     * 客户简称
     */
    private String CustShortNames;

    /**
     * 产品简称描述
     */
    private String ProdShortDesps;

    /**
     * 瓦楞描述
     */
    private String FluteDesps;

    /**
     * 数量描述
     */
    private String CountDesps;

    /**
     * 规格描述
     */
    private String SpecDesps;

    /**
     * 计划数量描述
     */
    private String PlanCountDesps;

    /**
     * 总面积
     */
    private Double TotalSquare;

    /**
     * 入库数量描述
     */
    private String InStorageCountDesps;

    /**
     * 出库数量描述
     */
    private String OutStorageCountDesps;

    /**
     * 无效数量描述
     */
    private String InvalidCountDesps;

    /**
     * 计划下一个程序
     */
    private String PlanNextProcedure;
}
