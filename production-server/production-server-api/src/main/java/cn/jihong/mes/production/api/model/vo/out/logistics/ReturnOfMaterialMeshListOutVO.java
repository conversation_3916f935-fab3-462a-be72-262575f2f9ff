package cn.jihong.mes.production.api.model.vo.out.logistics;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-02-26 15:05
 */
@Data
public class ReturnOfMaterialMeshListOutVO implements Serializable {

    /**
     * 网带编号
     */
    private int MeshId;

    /**
     * 区域编号
     */
    private int AreaId;

    /**
     * 区域描述
     */
    private String AreaDesc;

    /**
     * 最大宽度
     */
    private int MaxWidth;

    /**
     * 最大长度
     */
    private int MaxLength;

    /**
     * 位置值
     */
    private int Position;

    /**
     * Erp 设备编号
     */
    private String ERPDeviceId;

    /**
     * 设备描述
     */
    private String MeshDesc;

    /**
     * 被组合的网带编号
     */
    private int GroupedBy;

    /**
     * 前方轨道编号
     */
    private int FrontTrackIndex;

    /**
     * 后方轨道编号
     */
    private int BehindTrackIndex;

    /**
     * 靠近的轨道编号
     */
    private int NearByTrackIndex;

    /**
     * 禁止进入
     */
    private boolean ProhibitEntry;

    /**
     * 禁止使用
     */
    private boolean ProhibitUsed;

    /**
     * 允许使用的剩余长度，含即将进入的任务占用的长度
     */
    private int AllowUsedRemainLength;

    /**
     * 实际的剩余长度，不含即将进入的任务
     */
    private int ActualRemainLength;

    /**
     * 设备类型
     */
    private int DeviceType;

    /**
     * 同区域显示顺序
     */
    private int SameAreaDisplayIndex;

    /**
     * 是否可以执行
     */
    private boolean EnableExecute;

}
