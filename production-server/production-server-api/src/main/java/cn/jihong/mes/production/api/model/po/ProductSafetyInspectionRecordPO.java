package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 安全点检记录表
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Getter
@Setter
@TableName("product_safety_inspection_record")
public class ProductSafetyInspectionRecordPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String COMPANY_CODE = "company_code";
    public static final String MACHINE_NAME = "machine_name";
    public static final String PRODUCE_DATE = "produce_date";
    public static final String SHIFT = "shift";
    public static final String INSPECTION_STATUS = "inspection_status";
    public static final String INSPECTION_ITEMS = "inspection_items";
    public static final String INSPECTOR = "inspector";
    public static final String INSPECTOR_NAME = "inspector_name";
    public static final String INSPECTION_TIME = "inspection_time";
    public static final String PRODUCT_TICKET_NO = "product_ticket_no";
    public static final String REMARK = "remark";
    public static final String VERSION = "version";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";

    /**
     * 主键ID
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;

    /**
     * 据点
     */
    @TableField(COMPANY_CODE)
    private String companyCode;

    /**
     * 机台名称
     */
    @TableField(MACHINE_NAME)
    private String machineName;

    /**
     * 生产日期
     */
    @TableField(PRODUCE_DATE)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date produceDate;

    /**
     * 班次(1-白班，2-夜班)
     */
    @TableField(SHIFT)
    private Integer shift;

    /**
     * 点检状态(0-未点检，1-已点检，2-未开机)
     */
    @TableField(INSPECTION_STATUS)
    private Integer inspectionStatus;

    /**
     * 点检项目JSON(包含点检项ID和结果)
     */
    @TableField(INSPECTION_ITEMS)
    private String inspectionItems;

    /**
     * 点检人员ID
     */
    @TableField(INSPECTOR)
    private Long inspector;

    /**
     * 点检人员姓名
     */
    @TableField(INSPECTOR_NAME)
    private String inspectorName;

    /**
     * 点检时间
     */
    @TableField(INSPECTION_TIME)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date inspectionTime;

    /**
     * 生产工单号
     */
    @TableField(PRODUCT_TICKET_NO)
    private String productTicketNo;

    /**
     * 备注
     */
    @TableField(REMARK)
    private String remark;

    /**
     * 版本号
     */
    @TableField(VERSION)
    private String version;

    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;
} 