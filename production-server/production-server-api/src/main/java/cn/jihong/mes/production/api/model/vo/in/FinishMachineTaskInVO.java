package cn.jihong.mes.production.api.model.vo.in;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/12/19 13:53
 */
@Data
public class FinishMachineTaskInVO implements Serializable {
    private static final long serialVersionUID = -3661186696662917173L;

    /**
     * 机台任务id
     */
    private Long id;


    /**
     * 原来的机台状态类型
     */
    private Integer originalType;

    /**
     * 报工数量
     */
    private BigDecimal reportedQuantity;

    /**
     * 报工生产工单id 如果类型是生产则必传
     */
    private Long reportedProductId;

    /**
     * 是否结束  0-不结束 1-结束
     */
    private String finished;
}
