package cn.jihong.mes.production.api.service;


import cn.jihong.common.exception.CommonException;
import cn.jihong.common.model.Pagination;
import cn.jihong.mes.api.model.po.ProductionPlanPO;
import cn.jihong.mes.production.api.model.dto.DeviceOEEReportDTO;
import cn.jihong.mes.production.api.model.dto.ProductChangeProductionDTO;
import cn.jihong.mes.production.api.model.dto.ProductTicketBaseDTO;
import cn.jihong.mes.production.api.model.dto.ProductionPlanDTO;
import cn.jihong.mes.production.api.model.po.ProductTicketPO;
import cn.jihong.mes.production.api.model.vo.in.*;
import cn.jihong.mes.production.api.model.vo.in.logistics.CallMaterialInVO;
import cn.jihong.mes.production.api.model.vo.out.*;
import cn.jihong.mybatis.api.service.IJiHongService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 生产工单信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
public interface IProductTicketService extends IJiHongService<ProductTicketPO> {

    @Override
    default boolean updateById(ProductTicketPO entity) {
        boolean isUpdated = IJiHongService.super.updateById(entity);
        if (!isUpdated) {
            throw new CommonException("乐观锁更新失败，数据可能已被其他线程修改，请重试。");
        }
        return false;
    }

    /**
     * 校验，只允许生产中的工单
     * @param id
     */
    ProductTicketPO verify(Long id);

    /**
     * 创建生产工单并分配给自己
     *
     * @param redisLockKey
     * @param vo
     * @return {@link String}
     */
    Long createAndProcessor(String redisLockKey,CreateProductTicketInVO vo);

    String callMaterial(CallMaterialInVO inVO);

//    /**
//     * 创建转产生产工单
//     * @param vo
//     * @return {@link String}
//     */
//    Long createChangeProduction(CreateWorkOrderProductionTransferVO vo);


    ProductTicketPO getProductionTicket(String machineName);

    /**
     * 获得最后一个工单
     * @param machineName
     * @return
     */
    ProductTicketPO getLastProductionTicket(String machineName);


    /**
     * 获得当前机台的工单 --  使用中的工单
     * @param machineName
     * @return {@link String}
     */
    Long getProductionTicketNo(String machineName);


    /**
     * 获得当前机台的工单  -- 机台绑定的生产工程单的工单
     * @param machineName
     * @return {@link String}
     */
    Long getProductionMachineTicket(String machineName,Integer ticketType);


    /**
     * 获得当前机台的详情
     * @param id
     * @return {@link ProductionTicketInfoOutVO}
     */
    ProductionTicketInfoOutVO getProductionTicketInfo(Long id);

    /**
     * 获得当前机台的设备止码
     *
     * @param id
     */
    Integer getMachineStopNo(Long id);

    /**
     * 更新当前机台的设备止码
     *
     * @param id
     */
    void updateMachineStopNo(Long id,Integer stopNo);

    /**
     * 修改订单状态
     * @param productTicketPO
     * @param status
     * @param producedQuantity
     */
    void updateStatus(ProductTicketPO productTicketPO, int status, BigDecimal producedQuantity);

    List<ProductionPlanDTO> getProductPlanList(GetProductPlanListVO vo);


    /**
     * 获得生产工单信息
     */
    Pagination<ProductionTicketInfoOutVO> getProductTicketList(GetProductTicketListInVO getProductTicketListInVO);



    ProductTicketShiftOutVO getPlanTicketNoList( ProductTicketPO productTicketPO);

    /**
     * 获得生产工程单
     * @param page
     * @param productTicketBaseDTO
     * @return
     */
    Pagination<ProductionTicketInfoOutVO> getTicketByTicketBase(IPage page, ProductTicketBaseDTO productTicketBaseDTO);

    /**
     * 获取生产工单 通过生产工程单号
     * @param planTicketNo
     * @param status
     * @return: java.util.List<cn.jihong.mes.production.api.model.vo.out.GetListByPlanTicketNoOutVO>
     * <AUTHOR>
     * @date: 2023/11/14 10:42
     */
    List<GetListByPlanTicketNoOutVO> getListByPlanTicketNo(String planTicketNo, Integer status);

    /**
     * 获取生产工单 通过生产工程单号
     * @param planTicketNo
     * @return
     */
    List<ProductTicketPO> getListByPlanTicketNo(String planTicketNo,String machineName);

    /**
     * 获取生产工单 通过生产工程单号
     * @param planTicketNo
     * @return
     */
    List<ProductTicketPO> getListByPlanTicketNo(String planTicketNo);

    /**
     * 通过工程单号和工序，获得用料信息
     * @return {@link List}<{@link ProductTicketPO}>
     */
    List<ProductTicketPO> getListByPlanTicketNoAndProcess(String planTicketNo,String process);

    /**
     * 获取状态为进行中的转产记录
     * @param productChangeProductionDTO
     * @return
     */
    List<ProductChangeProdutionOutVO> getChangeProductTicketList(ProductChangeProductionDTO productChangeProductionDTO);

    /**
     * 创建结单生产工单
     * @param vo
     * @return
     */
    Long createFinishProduction(CreateFinishProductionInVO vo);

    /**
     * 获取结单记录
     * @param dto
     * @return
     */
    List<ProductChangeProdutionOutVO> getFinishProductTicketList(ProductChangeProductionDTO dto);

    /**
     * 报工
     */
    String signingUpForWork(String redisLockKey,Long id);

    /**
     * 转产确定---校验
     */
    String saveChangeInfo(ProductPlanTicketVO productPlanTicketVO);

    /**
     * 未结单的生产工单
     * @param inVO
     * @return: java.util.List<cn.jihong.mes.production.api.model.vo.out.UnfinishedOrderListOutVO>
     * <AUTHOR>
     * @date: 2023/12/26 11:43
     */
    List<UnfinishedOrderListOutVO> unfinishedOrderList(UnfinishedOrderListInVO inVO);

    /**
     * 获得生产中的详情
     * @param id
     * @return
     */
    ProductionTicketInfoOutVO getProductingTicketInfo(Long id);

    /**
     * 获得机台当前绑定的工程单
     * @param machineName
     * @return {@link String}
     */
    String getPalnTicketNo(String machineName);

    /**
     * 获得工程单列表
     * @param planTicketNo
     * @return
     */
    List<String> getPalnTicketNoList(String planTicketNo);

    /**
     * 分组查询工程单下的单条工单列表
     * @param machineName
     * @param produceDate
     * @param shift
     * @return: java.util.List<cn.jihong.mes.production.api.model.vo.out.GetProductTicketInfoListOutVO>
     * <AUTHOR>
     * @date: 2024/3/15 11:13
     */
    List<GetProductTicketInfoListOutVO> getProductTicketInfoList(String machineName,String produceDate,Integer shift);

    /**
     * 获得生产日报（工厂）
     * @param getProductReportByFactoryInVO
     * @return
     */
    Page<GetProductReportByFactoryOutVO> getReportByFactory(GetProductReportByFactoryInVO getProductReportByFactoryInVO);

    List<GetProduceDetailsOutVO> getProduceDetailsCalibreReview(String planTicketNo);

    List<GetMaterialDetailsOutVO> getMaterialDetailsCalibreReview(String planTicketNo);

    Page<GetMaterialUseDetailsOutVO> getMaterialUseDetails(IPage page, GetMaterialUseDetailsInVO getMaterialUseDetailsInVO);

    List<GetMachineGroupReportOutVO> getMachineGroupReport(GetMachineGroupReportInVO getMachineGroupReportInVO);

    GetErpTicketInfoOutVO getTicketInfo(String planTicketNo);

    Page<GetProductReportByFactoryOutVO> getProductPlanReport(GetProductReportByFactoryInVO getProductReportByFactoryInVO);

    List<GetValuationTypeOutVO> getValuationType(GetValuationTypeInVO getValuationTypeInVO);

    GetErpTicketInfoOutVO getTicketDetailInfo(String planTicketNo);


    /**
     * 修改计件类型
     * @param productTicketId
     * @param pieceType
     * @return: java.lang.Boolean
     * <AUTHOR>
     * @date: 2024-08-26 16:47
     */
    Boolean updatePieceType(Long productTicketId,String pieceType);

    /**
     * 获得上一个任务的任务信息
     */
    GetLastTaskInfoOutVO getLastTaskInfo(GetLastTaskInfoInVO vo);

    List<GetOneDayTaskSummaryOutVO> getOneDayTaskSummary(String machineName, Date produceDate, Integer shift);

    List<GetOneDayTaskSummaryOutVO> getOneDayTaskSummaryDetail(String machineName, Date produceDate, Integer shift);

    List<GetReportShiftNoOutVO> getReportShit(Long productTicketId);

    List<ProductTicketPO> getListByMachineNameAndProduceDate(String machineName,String processName,int year, int month);

    List<DeviceOEEReportDTO> deviceOEEReportShow(String companyCode, String machineName, String processName, String produceYearMonth);

    List<ProductionPlanPO> getTileWireProductPlanList(GetTileWireProductPlanListInVO inVO);

    /**
     * 瓦线DCS订单API
     *
     * @param productionPlanIdList
     * @return
     */
    Boolean tileWirePostOrder(List<Long> productionPlanIdList);

    List<GetProcessQuantityByPlanTicketNo> getProcessQuantityByPlanTicketNo(String planTicketNo);

    Boolean cancelGoodsReport(Long id);

    Boolean cancelBadGoodsReport(Long id);

    Boolean cancelMaterialReport(Long id);
}
