package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SaveDefectiveProductsInfoInVO extends ProductTicketVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;

    /**
     * 不良原因
     */
    @NotBlank(message = "不良原因不能为空")
    private String defectiveProductsReason;
    private String defectiveProductsReasonName;

    /**
     * 不良数量
     */
    private BigDecimal defectiveProductsQuantity;

    /**
     * 单位
     */
    private String unit;

    /**
     * 不良来源
     */
    @NotBlank(message = "不良来源不能为空")
    private String defectiveProductsSource;
    private String defectiveProductsSourceName;

    /**
     * 栈板码
     */
    private String palletCode;

}
