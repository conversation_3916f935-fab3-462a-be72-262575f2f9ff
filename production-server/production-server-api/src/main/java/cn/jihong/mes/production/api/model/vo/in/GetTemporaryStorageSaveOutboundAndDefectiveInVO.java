package cn.jihong.mes.production.api.model.vo.in;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/4/11 14:45
 */
@Data
public class GetTemporaryStorageSaveOutboundAndDefectiveInVO implements Serializable {
    private static final long serialVersionUID = 3967710158612601350L;

    /**
     * 栈板码
     */
    @NotBlank(message = "栈板码不能为空")
    private String palletCode;
}
