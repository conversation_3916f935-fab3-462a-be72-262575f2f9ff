package cn.jihong.mes.production.api.model.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 不良品记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-19
 */
@Getter
@Setter
@TableName("product_defective_products")
public class ProductDefectiveProductsPO implements Serializable {
    private static final long serialVersionUID = 1L;

    public static final String ID = "id";
    public static final String MACHINE_NAME = "machine_name";
    public static final String PRODUCT_TICKET_ID = "product_ticket_id";
    public static final String DEFECTIVE_PRODUCTS_REASON = "defective_products_reason";
    public static final String DEFECTIVE_PRODUCTS_REASON_NAME = "defective_products_reason_name";
    public static final String DEFECTIVE_PRODUCTS_QUANTITY = "defective_products_quantity";
    public static final String UNIT = "unit";
    public static final String DEFECTIVE_PRODUCTS_SOURCE = "defective_products_source";
    public static final String DEFECTIVE_PRODUCTS_SOURCE_NAME = "defective_products_source_name";
    public static final String PALLET_CODE = "pallet_code";
    public static final String CREATE_BY = "create_by";
    public static final String CREATE_TIME = "create_time";
    public static final String UPDATE_BY = "update_by";
    public static final String UPDATE_TIME = "update_time";
    public static final String DELETED = "deleted";
    public static final String COMPANY_CODE = "company_code";
    public static final String IS_CALLBACK = "is_callback";


    /**
     * id
     */
    @TableId(value = ID, type = IdType.AUTO)
    private Long id;

    /**
     * 工厂代码
     */
    @TableField(COMPANY_CODE)
    private String companyCode;

    /**
     * 是否回调
     */
    @TableField(IS_CALLBACK)
    private Integer isCallback;

    /**
     * 机台名称
     */
    @TableField(MACHINE_NAME)
    private String machineName;


    /**
     * 生产工单id
     */
    @TableField(PRODUCT_TICKET_ID)
    private Long productTicketId;

    /**
     * 不良原因
     */
    @TableField(DEFECTIVE_PRODUCTS_REASON)
    private String defectiveProductsReason;

    /**
     * 不良原因名称
     */
    @TableField(DEFECTIVE_PRODUCTS_REASON_NAME)
    private String defectiveProductsReasonName;

    /**
     * 不良数量
     */
    @TableField(DEFECTIVE_PRODUCTS_QUANTITY)
    private BigDecimal defectiveProductsQuantity;


    /**
     * 单位
     */
    @TableField(UNIT)
    private String unit;


    /**
     * 不良来源
     */
    @TableField(DEFECTIVE_PRODUCTS_SOURCE)
    private String defectiveProductsSource;

    /**
     * 不良来源
     */
    @TableField(DEFECTIVE_PRODUCTS_SOURCE_NAME)
    private String defectiveProductsSourceName;


    /**
     * 栈板码
     */
    @TableField(PALLET_CODE)
    private String palletCode;


    /**
     * 创建人
     */
    @TableField(CREATE_BY)
    private Long createBy;


    /**
     * 创建时间
     */
    @TableField(value = CREATE_TIME, fill = FieldFill.INSERT)
    private Date createTime;


    /**
     * 编辑人
     */
    @TableField(UPDATE_BY)
    private Long updateBy;


    /**
     * 编辑时间
     */
    @TableField(value = UPDATE_TIME, fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    /**
     * 是否删除
     */
    @TableField(value = DELETED, fill = FieldFill.INSERT)
    @TableLogic
    private Integer deleted;

}
